package log

import (
	"encoding/json"

	"go.opentelemetry.io/otel/attribute"
	oteltrace "go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// addFieldAsAttribute adds a Zap field as a OpenTelemetry trace attribute.
func addFieldAsAttribute(span oteltrace.Span, field zap.Field) {
	switch field.Type {
	case zapcore.StringType:
		span.SetAttributes(attribute.String(field.Key, field.String))

	case zapcore.BoolType:
		span.SetAttributes(attribute.Bool(field.Key, field.Integer == 1))

	case zapcore.Int64Type, zapcore.Int32Type, zapcore.Int16Type, zapcore.Int8Type, zapcore.Uint64Type,
		zapcore.Uint32Type, zapcore.Uint16Type, zapcore.Uint8Type:

		span.SetAttributes(attribute.Int(field.Key, int(field.Integer)))

	case zapcore.Float64Type, zapcore.Float32Type:
		span.SetAttributes(attribute.Float64(field.Key, field.Interface.(float64)))

	case zapcore.DurationType:
		span.SetAttributes(attribute.Int64(field.Key, field.Integer))

	default:
		v, err := json.Marshal(field.Interface)
		if err == nil {
			span.SetAttributes(attribute.String(field.Key, string(v)))
		}
	}
}
