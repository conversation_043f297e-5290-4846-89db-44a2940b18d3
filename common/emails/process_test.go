package emails

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/integrations/tms/quantumedge"
	"github.com/drumkitai/drumkit/common/models"
)

// TestQuantumEdgeProcessorFlow tests that QuantumEdge IDs are handled with DB-only lookups
// and that live TMS calls are suppressed
func TestQuantumEdgeProcessorFlow(t *testing.T) {
	ctx := context.Background()
	service := models.Service{
		Model: gorm.Model{ID: 1},
		Name:  "Test Service",
	}

	t.Run("FreightTrackingID found in DB - no TMS call", func(t *testing.T) {
		// Setup: Mock functions
		originalGetTMSList := dbGetTMSListFunc
		originalGetLoad := dbGetLoadByFreightTrackingIDFunc
		originalGetLoadsFromTMS := getLoadsFromTMSFunc
		originalGetLoadByCustomerRef := dbGetLoadByCustomerRefFunc
		defer func() {
			dbGetTMSListFunc = originalGetTMSList
			dbGetLoadByFreightTrackingIDFunc = originalGetLoad
			getLoadsFromTMSFunc = originalGetLoadsFromTMS
			dbGetLoadByCustomerRefFunc = originalGetLoadByCustomerRef
		}()

		tmsCallCount := 0
		dbCallCount := 0

		// Mock: Customer ref lookup (not used in this test but needed to avoid nil pointer)
		dbGetLoadByCustomerRefFunc = func(context.Context, string, uint) (*models.Load, error) {
			return nil, gorm.ErrRecordNotFound
		}

		// Mock: Return QuantumEdge TMS
		dbGetTMSListFunc = func(context.Context, uint) ([]models.Integration, error) {
			return []models.Integration{
				{
					Model:     gorm.Model{ID: 16},
					Name:      models.QuantumEdge,
					ServiceID: 1,
					Tenant:    "test",
				},
			}, nil
		}

		// Mock: DB returns a load (found in DB)
		dbGetLoadByFreightTrackingIDFunc = func(
			_ context.Context,
			serviceID uint,
			freightTrackingID string,
		) (models.Load, error) {
			dbCallCount++
			return models.Load{
				Model:             gorm.Model{ID: 1},
				ServiceID:         serviceID,
				FreightTrackingID: freightTrackingID,
				TMSID:             16,
			}, nil
		}

		// Mock: TMS call should NOT be invoked
		getLoadsFromTMSFunc = func(
			context.Context,
			TMSFreightIDForIntegration,
			models.Service,
		) ([]models.Load, error) {
			tmsCallCount++
			t.Error("TMS call should not be made when load is found in DB")
			return nil, nil
		}

		// Test: Direct DB lookup with FreightTrackingIDType
		freightID := tmsFreightID{
			IDType: quantumedge.FreightTrackingIDType,
			ID:     "1398904",
		}

		dbLoad, err := getLoadFromDBByIDType(ctx, freightID, service)

		// Should find the load in DB
		require.NoError(t, err)
		require.NotNil(t, dbLoad)
		assert.Equal(t, "1398904", dbLoad.FreightTrackingID)

		// Assertions
		assert.Equal(t, 1, dbCallCount, "DB should be called once")
		assert.Equal(t, 0, tmsCallCount, "TMS should NOT be called when load is in DB")
	})

	t.Run("FreightTrackingID NOT in DB - suppressed (no TMS call)", func(t *testing.T) {
		// Setup: Mock functions
		originalGetTMSList := dbGetTMSListFunc
		originalGetLoad := dbGetLoadByFreightTrackingIDFunc
		originalGetLoadsFromTMS := getLoadsFromTMSFunc
		originalGetLoadByCustomerRef := dbGetLoadByCustomerRefFunc
		defer func() {
			dbGetTMSListFunc = originalGetTMSList
			dbGetLoadByFreightTrackingIDFunc = originalGetLoad
			getLoadsFromTMSFunc = originalGetLoadsFromTMS
			dbGetLoadByCustomerRefFunc = originalGetLoadByCustomerRef
		}()

		tmsCallCount := 0
		dbCallCount := 0

		// Mock: Customer ref lookup (not used in this test but needed to avoid nil pointer)
		dbGetLoadByCustomerRefFunc = func(context.Context, string, uint) (*models.Load, error) {
			return nil, gorm.ErrRecordNotFound
		}

		// Mock: Return QuantumEdge TMS
		dbGetTMSListFunc = func(context.Context, uint) ([]models.Integration, error) {
			return []models.Integration{
				{
					Model:     gorm.Model{ID: 16},
					Name:      models.QuantumEdge,
					ServiceID: 1,
					Tenant:    "test",
				},
			}, nil
		}

		// Mock: DB returns not found
		dbGetLoadByFreightTrackingIDFunc = func(context.Context, uint, string) (models.Load, error) {
			dbCallCount++
			return models.Load{}, gorm.ErrRecordNotFound
		}

		// Mock: TMS call should NOT be invoked (suppressed)
		getLoadsFromTMSFunc = func(
			context.Context,
			TMSFreightIDForIntegration,
			models.Service,
		) ([]models.Load, error) {
			tmsCallCount++
			t.Error("TMS call should be suppressed for QuantumEdge")
			return nil, nil
		}

		// Test: Direct DB lookup with FreightTrackingIDType that doesn't exist
		freightID := tmsFreightID{
			IDType: quantumedge.FreightTrackingIDType,
			ID:     "9999999",
		}

		// Try DB lookup first
		_, err := getLoadFromDBByIDType(ctx, freightID, service)

		// Should return not found
		assert.Error(t, err)
		assert.Equal(t, gorm.ErrRecordNotFound, err)

		// Check if TMS is suppressed (would happen in the Analyze function)
		tmsIntegration := models.Integration{
			Model: gorm.Model{ID: 16},
			Name:  models.QuantumEdge,
		}
		assert.Contains(t, suppressProcessorLookupTMS, tmsIntegration.Name,
			"QuantumEdge should be in suppression list to prevent TMS calls")

		// Assertions
		assert.Equal(t, 1, dbCallCount, "DB should be called once")
		assert.Equal(t, 0, tmsCallCount, "TMS should be suppressed (not called)")
	})

	t.Run("RefNumberIDType found in DB - no TMS call", func(t *testing.T) {
		// Setup: Mock functions
		originalGetLoadByCustomerRef := dbGetLoadByCustomerRefFunc
		originalGetLoadsFromTMS := getLoadsFromTMSFunc
		originalGetLoad := dbGetLoadByFreightTrackingIDFunc
		defer func() {
			dbGetLoadByCustomerRefFunc = originalGetLoadByCustomerRef
			getLoadsFromTMSFunc = originalGetLoadsFromTMS
			dbGetLoadByFreightTrackingIDFunc = originalGetLoad
		}()

		tmsCallCount := 0
		dbCallCount := 0

		// Mock: FreightID lookup (not used in this test but needed to avoid nil pointer)
		dbGetLoadByFreightTrackingIDFunc = func(context.Context, uint, string) (models.Load, error) {
			return models.Load{}, gorm.ErrRecordNotFound
		}

		// Mock: DB returns a load with customer ref
		dbGetLoadByCustomerRefFunc = func(
			_ context.Context,
			customRef string,
			serviceID uint,
		) (*models.Load, error) {
			dbCallCount++
			load := &models.Load{
				Model:     gorm.Model{ID: 2},
				ServiceID: serviceID,
				TMSID:     16,
			}
			load.Customer.RefNumber = customRef
			return load, nil
		}

		// Mock: TMS call should NOT be invoked
		getLoadsFromTMSFunc = func(
			context.Context,
			TMSFreightIDForIntegration,
			models.Service,
		) ([]models.Load, error) {
			tmsCallCount++
			t.Error("TMS call should not be made when ref number is found in DB")
			return nil, nil
		}

		// Test: Lookup by ref number
		freightID := tmsFreightID{
			IDType: quantumedge.RefNumberIDType,
			ID:     "REF123",
		}

		dbLoad, err := getLoadFromDBByIDType(ctx, freightID, service)

		// Should find the load in DB
		require.NoError(t, err)
		require.NotNil(t, dbLoad)
		assert.Equal(t, "REF123", dbLoad.Customer.RefNumber)

		// Assertions
		assert.Equal(t, 1, dbCallCount, "DB should be called once")
		assert.Equal(t, 0, tmsCallCount, "TMS should NOT be called")
	})

	t.Run("RefNumberIDType NOT in DB - suppressed (no TMS call)", func(t *testing.T) {
		// Setup: Mock functions
		originalGetLoadByCustomerRef := dbGetLoadByCustomerRefFunc
		originalGetLoadsFromTMS := getLoadsFromTMSFunc
		originalGetLoad := dbGetLoadByFreightTrackingIDFunc
		defer func() {
			dbGetLoadByCustomerRefFunc = originalGetLoadByCustomerRef
			getLoadsFromTMSFunc = originalGetLoadsFromTMS
			dbGetLoadByFreightTrackingIDFunc = originalGetLoad
		}()

		tmsCallCount := 0
		dbCallCount := 0

		// Mock: FreightID lookup (not used in this test but needed to avoid nil pointer)
		dbGetLoadByFreightTrackingIDFunc = func(
			context.Context,
			uint,
			string,
		) (models.Load, error) {
			return models.Load{}, gorm.ErrRecordNotFound
		}

		// Mock: DB returns not found
		dbGetLoadByCustomerRefFunc = func(_ context.Context, _ string, _ uint) (*models.Load, error) {
			dbCallCount++
			return nil, gorm.ErrRecordNotFound
		}

		// Mock: TMS call should NOT be invoked (suppressed)
		getLoadsFromTMSFunc = func(
			context.Context,
			TMSFreightIDForIntegration,
			models.Service,
		) ([]models.Load, error) {
			tmsCallCount++
			t.Error("TMS call should be suppressed for QuantumEdge")
			return nil, nil
		}

		// Test: Lookup by ref number
		freightID := tmsFreightID{
			IDType: quantumedge.RefNumberIDType,
			ID:     "NOTFOUND",
		}

		_, err := getLoadFromDBByIDType(ctx, freightID, service)

		// Should return not found
		assert.Error(t, err)
		assert.Equal(t, gorm.ErrRecordNotFound, err)

		// Assertions
		assert.Equal(t, 1, dbCallCount, "DB should be called once")
		assert.Equal(t, 0, tmsCallCount, "TMS should be suppressed")
	})

	t.Run("QuantumEdge is in suppression list", func(t *testing.T) {
		// Verify QuantumEdge is in the suppression list
		assert.Contains(t, suppressProcessorLookupTMS, models.QuantumEdge,
			"QuantumEdge should be in suppressProcessorLookupTMS array")
	})
}
