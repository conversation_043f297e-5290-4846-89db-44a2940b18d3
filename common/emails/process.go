package emails

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"regexp"
	"slices"
	"strings"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/sqsclient"
	"github.com/drumkitai/drumkit/common/integrations/llm/openai"
	"github.com/drumkitai/drumkit/common/integrations/tms"
	"github.com/drumkitai/drumkit/common/integrations/tms/aljex"
	"github.com/drumkitai/drumkit/common/integrations/tms/ascend"
	"github.com/drumkitai/drumkit/common/integrations/tms/globaltranztms"
	"github.com/drumkitai/drumkit/common/integrations/tms/mcleodenterprise"
	"github.com/drumkitai/drumkit/common/integrations/tms/quantumedge"
	"github.com/drumkitai/drumkit/common/integrations/tms/revenova"
	"github.com/drumkitai/drumkit/common/integrations/tms/threeg"
	"github.com/drumkitai/drumkit/common/integrations/tms/turvo"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	emailDB "github.com/drumkitai/drumkit/common/rds/email"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
	"github.com/drumkitai/drumkit/common/rds/order"
	"github.com/drumkitai/drumkit/common/redis"
)

var (
	getLoadsFromTMSFunc              = getLoadFromTMSByIDType
	upsertEmailFunc                  = emailDB.UpsertEmailAndLoads
	upsertEmailWithTxFunc            = emailDB.UpsertEmailAndLoadsWithTx
	upsertLoadFunc                   = loadDB.UpsertLoad
	dbDeletePlaceholderLoadsFunc     = loadDB.DeletePlaceholderLoads
	dbGetLoadByFreightTrackingIDFunc = loadDB.GetLoadByFreightIDAndService
	dbGetLoadByExternalTMSIDFunc     = loadDB.GetLoadByExternalTMSIDAndService
	dbGetLoadByCustomerRefFunc       = loadDB.GetLoadByCustomerRef
	dbGetLoadByPONumFunc             = loadDB.GetLoadByPONum
	dbGetTMSListFunc                 = integrationDB.GetTMSListByServiceID
	dbGetOrderByReferenceFunc        = order.GetOrderByReference
)

const (
	RedisTMSIdentifierTTL = 1 * time.Hour // TTL for TMS 404'd searches with customer refs, PO #s, etc.
)

type (
	TMSFreightIDForIntegration struct {
		FreightID   tmsFreightID
		integration models.Integration
	}
)

var (
	suppressProcessorLookupTMS = []models.IntegrationName{
		models.GlobalTranzTMS,
		models.QuantumEdge,
		models.Revenova,
	}
)

// Analyze is the main function for processing emails. It auto-detects and fetches the loads from the TMS,
// upserts the loads and emails, classifies the email, then runs the email through corresponding LLM pipelines.
// The function fail-closes on critical errors, namely inserting the email in the DB.
// Afterward, it fails open on LLM pipeline errors
func Analyze(
	ctx context.Context,
	email *models.IngestedEmail,
	service models.Service,
	sqsClient sqsclient.API,
	opts ...Option,
) (_ uint, err error) {

	options := &Options{
		AppTmpDir:  "",
		Service:    nil,
		EmailLoads: nil,
	}

	for _, opt := range opts {
		opt(options)
	}

	ctx, metaSpan := otel.StartSpan(ctx, "emails.Analyze", nil)
	defer func() { metaSpan.End(err) }()

	email.ServiceID = helpers.Or(email.ServiceID, service.ID)

	tmsIntegrations, err := dbGetTMSListFunc(ctx, service.ID)
	if err != nil {
		err = fmt.Errorf("error fetching tms list for service: %w", err)
		log.ErrorNoSentry(ctx, "db error", zap.Error(err))

		return 0, err
	}

	var allFreightIDs []TMSFreightIDForIntegration

	for _, tms := range tmsIntegrations {
		switch tms.Name {
		case models.Relay:
			allFreightIDs = append(allFreightIDs, getRelayIDsFromEmail(email, tms)...)

		case models.Aljex:
			allFreightIDs = append(allFreightIDs, getAljexIDsFromEmail(email, tms)...)

		case models.Turvo:
			allFreightIDs = append(allFreightIDs, getTurvoIDsFromEmail(email, tms)...)

		case models.Ascend:
			allFreightIDs = append(allFreightIDs, getAscendIDsFromEmail(email, tms)...)

		case models.Tai:
			allFreightIDs = append(allFreightIDs, getTaiIDsFromEmail(email, tms)...)

		case models.McleodEnterprise:
			allFreightIDs = append(allFreightIDs, getMcleodIDsFromEmail(email, tms)...)

		case models.GlobalTranzTMS:
			allFreightIDs = append(allFreightIDs, getGlobalTranzTMSFromEmail(email, tms)...)

		case models.Revenova:
			allFreightIDs = append(allFreightIDs, getRevenovaIDsFromEmail(email, tms)...)

		case models.ThreeG:
			allFreightIDs = append(allFreightIDs, getThreeGTMSFromEmail(email, tms)...)

		case models.FreightFlow:
			allFreightIDs = append(allFreightIDs, getFreightFlowIDsFromEmail(email, tms)...)

		case models.QuantumEdge:
			allFreightIDs = append(allFreightIDs, getQuantumEdgeIDsFromEmail(email, tms)...)

		case models.Stark:
			allFreightIDs = append(allFreightIDs, getStarkIDsFromEmail(email, tms)...)

		// Mock TMS in order to support PLS QQ customer assignment
		case models.PLS:
			continue

		default:
			log.Error(ctx,
				"unable to parse IDs from email for unrecognized TMS",
				zap.String("tms", string(tms.Name)),
			)
		}
	}

	if len(allFreightIDs) == 0 {
		log.Info(ctx, "no freight IDs found for email")
	} else {
		log.Info(ctx, "parsed freight IDs", zap.Any("freightIDs", allFreightIDs), zap.Int("count", len(allFreightIDs)))
	}

	emailLoads := []models.Load{}

	for _, freightID := range allFreightIDs {
		var loads []models.Load
		log.With(ctx, zap.Any("curFreightID", freightID))

		// If duplicate, don't re-fetch from TMS. Drumkit API will live lookup. But associate it with this email
		if dbLoad, err := getLoadFromDBByIDType(ctx, freightID.FreightID, service); err == nil && dbLoad != nil {
			log.Info(ctx, "load already in DB", zap.Any("curFreightID", freightID), zap.Uint("loadID", dbLoad.ID))
			loads = []models.Load{*dbLoad}
		} else {
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				log.ErrorNoSentry(ctx, "error searching for duplicate loads in DB", zap.Error(err))
			}

			if slices.Contains(suppressProcessorLookupTMS, freightID.integration.Name) {
				log.Info(
					ctx,
					"suppressing processor lookup for TMS",
					zap.String("tms", string(freightID.integration.Name)),
				)

				continue
			}

			loads, err = getLoadsFromTMSFunc(ctx, freightID, service)

			switch {
			case err == nil:
				if len(loads) > 0 {
					log.Info(ctx, "successfully got load(s) from TMS", zap.Int("count", len(loads)))
				}

				for j := range loads {
					err = upsertLoadFunc(ctx, &loads[j], &service)
					if err != nil {
						log.WarnNoSentry(ctx, "error upserting load", zap.Error(err))
					}
				}

			case errtypes.IsEntityNotFoundError(err):
				log.Info(ctx, "load not found")

			case isTransientHTTPError(ctx, err, freightID.FreightID):
				// Fail-open so we can add email and associated load to DB
				log.WarnNoSentry(ctx, "transient error getting load from TMS, creating placeholder load",
					zap.Error(err))

				// NOTE: Placeholder load logic depends on API looking up by multiple ID types if first
				// lookup fails.
				//
				// EX: If we parsed a PO # and there's a transient error while getting from the TMS,
				// then we don't know what the Turvo customId aka freightTrackingID is, but we need to
				// adhere to the upsert constraint to create a placeholder load. This is why regardless
				// of the ID type, we set load.FreightTrackingID = freightID.ID. The API will fallback
				// to searching by PO/Customer # if customId fails.
				loads = []models.Load{{
					ServiceID:         service.ID,
					TMSID:             freightID.integration.ID,
					FreightTrackingID: freightID.FreightID.ID,
					IsPlaceholder:     true,
				}}

				for j := range loads {
					err = upsertLoadFunc(ctx, &loads[j], &service)
					if err != nil {
						log.WarnNoSentry(ctx, "error upserting placeholder load", zap.Error(err))
					}
				}

			default:
				log.Error(
					ctx,
					"error getting load from TMS",
					zap.Error(err),
					zap.String("tms", string(freightID.integration.Name)),
				)

				continue
			}
		}

		if loads != nil {
			emailLoads = append(emailLoads, loads...)
		}
	}

	// Delete stale placeholder loads every so often
	if time.Now().Minute()%35 == 0 {
		count, err := dbDeletePlaceholderLoadsFunc(ctx, service.ID)
		if err != nil {
			log.Warn(ctx, "error deleting placeholder loads", zap.Error(err))
		}

		if count > 0 {
			log.Info(ctx, "deleted stale placeholder loads", zap.Int("count", count))
		}
	}

	// Use the full email body (including inline replies) when classifying
	processedEmail := models.Email{
		Account:            email.Account,
		UserID:             email.UserID,
		ServiceID:          service.ID,
		RFCMessageID:       email.RFCMessageID,
		ExternalID:         email.ExternalID,
		ThreadID:           email.ThreadID,
		Body:               CleanEmail(ctx, email.Body, email.Subject),
		BodyType:           email.BodyType,
		CC:                 email.CC,
		SentAt:             email.SentAt,
		Sender:             email.Sender,
		Subject:            email.Subject,
		S3URL:              email.S3URL,
		Recipients:         email.Recipients,
		ThreadReferences:   email.ThreadReferences,
		InReplyTo:          email.InReplyTo,
		Loads:              emailLoads,
		Attachments:        email.Attachments,
		HasPDFs:            email.HasPDFs,
		ProcessingMetadata: email.ProcessingMetadata,
	}

	bodyWithoutSignature, signature, err := SegmentEmail(ctx, processedEmail)
	if err != nil {
		// Fail-open; email should still be processed if signature stripping fails
		log.WarnNoSentry(ctx, "failed to remove signature from email", zap.Error(err))

		// If signature stripping fails, default to full email body
		email.BodyWithoutSignature = email.Body
	} else {
		email.Signature = signature
		email.BodyWithoutSignature = bodyWithoutSignature
	}

	// Use the full email body (including inline replies) when classifying
	labels, approach, categoryReasoning, labelReasoning := Classify(
		ctx,
		*email,
		WithAppTmpDir(options.AppTmpDir),
		WithService(&service),
		WithLoads(emailLoads),
	)

	labels = append(labels, email.Label)

	processedEmail.Signature = email.Signature
	processedEmail.BodyWithoutSignature = email.BodyWithoutSignature

	processedEmail.Labels = strings.Join(labels, ",")
	processedEmail.LabelReasoning = labelReasoning
	processedEmail.CategoryReasoning = categoryReasoning
	processedEmail.ClassificationMethod = approach

	// TODO: notify email frontend with labels for corresponding email
	log.Info(ctx, "email categorized", zap.Strings("labels", labels), zap.String("label_reasoning", labelReasoning))

	// Generate embedding before starting the transaction
	embedding, err := generateEmailEmbedding(ctx, &processedEmail)
	if err != nil {
		var embeddingErr *openai.ErrEmbeddingDisabled
		if errors.As(err, &embeddingErr) {
			log.WarnNoSentry(ctx, embeddingErr.Error(), zap.Uint("emailID", processedEmail.ID))
		} else {
			// Fail-open, but log the error
			log.Warn(ctx, "failed to generate email embedding, proceeding without it", zap.Error(err))
		}
	}

	// Wrap email and vector upserts in a single transaction
	err = rds.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if err := upsertEmailWithTxFunc(ctx, tx, &processedEmail); err != nil {
			return fmt.Errorf("failed to store processed email: %w", err)
		}

		// If embedding was generated successfully, store it
		if embedding != nil {
			if err := storeEmailEmbeddingWithTx(ctx, tx, &processedEmail, embedding); err != nil {
				// Failing to store the embedding should not fail the transaction,
				// but we should log it.
				log.Warn(
					ctx,
					"failed to store email embedding in transaction",
					zap.Uint("emailID", processedEmail.ID),
					zap.Error(err),
				)
			}
		}
		return nil
	})
	if err != nil {
		// The transaction failed (and rolled back). We will have logged the error already.
		// Stop here to avoid continuing with the email processing.
		return 0, err
	}

	bouncedMsg, err := handleOutlookBouncedEmail(ctx, &processedEmail)
	if err != nil {
		// Fail-open; error happens only when a message bounced
		log.ErrorNoSentry(ctx, "error handling bounced email", zap.Error(err))
	}

	if bouncedMsg != nil {
		log.Error(
			ctx,
			"drumkit-generated email bounced",
			zap.Uint("origEmailID", bouncedMsg.ID),
			zap.Uint("bouncedNotifID", processedEmail.ID),
			zap.String("serviceName", service.Name),
		)
	}

	RunActions(ctx, processedEmail, WithService(&service), WithSQSClient(sqsClient))

	return processedEmail.ID, nil
}

// AnalyzeOnPrem allows us to skip TMS matching queries, freight ID matching and relabelling the email.

// All we do on AnalyzeOnPrem is upsert the email, log errors in case it's bounced and run actions to
// extract its content.

func AnalyzeOnPrem(ctx context.Context, email *models.IngestedEmail, service models.Service) error {
	receivedOnPremEmail := models.Email{
		Account:   email.Account,
		UserID:    email.UserID,
		ServiceID: service.ID,

		RFCMessageID:         email.RFCMessageID,
		ExternalID:           email.ExternalID,
		ThreadID:             email.ThreadID,
		Body:                 CleanEmail(ctx, email.Body, email.Subject),
		BodyType:             email.BodyType,
		CC:                   email.CC,
		ClassificationMethod: email.ClassificationMethod,
		SentAt:               email.SentAt,
		Sender:               email.Sender,
		Labels:               email.Label,
		Subject:              email.Subject,
		S3URL:                email.S3URL,
		Recipients:           email.Recipients,
		ThreadReferences:     email.ThreadReferences,
		InReplyTo:            email.InReplyTo,
		Attachments:          email.Attachments,
	}

	if err := upsertEmailFunc(ctx, &receivedOnPremEmail); err != nil {
		return fmt.Errorf("failed to store received onprem email: %w", err)
	}

	bouncedMsg, err := handleOutlookBouncedEmail(ctx, &receivedOnPremEmail)
	if err != nil {
		// Fail-open; error happens only when a message bounced
		log.Error(ctx, "error handling bounced email", zap.Error(err))
	}

	if bouncedMsg != nil {
		log.Error(
			ctx,
			"drumkit-generated email bounced",
			zap.Uint("origEmailID", bouncedMsg.ID),
			zap.Uint("bouncedNotifID", receivedOnPremEmail.ID),
			zap.String("serviceName", service.Name),
		)
	}

	RunActions(ctx, receivedOnPremEmail, WithService(&service))

	return nil
}

func ReprocessContent(ctx context.Context, email *models.Email, labelsToReprocess []string) error {
	if len(labelsToReprocess) > 0 {
		email.Labels = strings.Join(labelsToReprocess, ",")

		// Update email with new labels
		if err := emailDB.Update(ctx, email); err != nil {
			log.Error(ctx, "error updating email labels while reprocessing", zap.Error(err))
		}
	}

	service, err := rds.GetServiceByID(ctx, email.ServiceID)
	if err != nil {
		return fmt.Errorf("error fetching service: %w", err)
	}

	RunActions(ctx, *email, WithService(&service), WithEmailReprocess(true))
	return nil
}

// GetRFCIDRedisKey returns a Redis key for a given email RFC ID; used to check if email is/has already been processed
func GetRFCIDRedisKey(serviceID uint, rfcID string) string {
	return fmt.Sprintf("message-service-%d-rfc-%s", serviceID, rfcID)
}

// setRedisWithWarn sets a Redis key with a Sentry warning if it fails
func SetRedisWithWarn(ctx context.Context, key string, value string, ttl time.Duration) {
	if err := redis.SetKey(ctx, key, value, ttl); err != nil {
		log.Warn(ctx, "failed to set redis key", zap.Error(err), zap.String("key", key))
	}
}

// Wrapper for TMSInteface.GetLoadByIDType. If a certain TMS doesn't implement it, then it fallsback to GetLoad()
func getLoadFromTMSByIDType(
	ctx context.Context,
	freightID TMSFreightIDForIntegration,
	service models.Service,
) (_ []models.Load, err error) {

	ctx, metaSpan := otel.StartSpan(ctx, "getLoadFromTMSByIDType", nil)
	defer func() { metaSpan.End(err) }()

	tmsIntegration := freightID.integration
	tmsFreightID := freightID.FreightID

	if freightID.FreightID.ID == "" {
		return nil, nil
	}

	// Handle order IDs for 3G TMS
	if tmsIntegration.Name == models.ThreeG && tmsFreightID.IDType == threeg.OrderNum {
		// If not in DB, try to get from TMS
		client, err := tms.New(ctx, tmsIntegration)
		if err != nil {
			return nil, fmt.Errorf("error creating new TMS client: %w", err)
		}

		// Try to get order from TMS
		order, _, err := client.GetOrder(ctx, tmsFreightID.ID)
		if err != nil {
			if errtypes.IsEntityNotFoundError(err) {
				log.Info(ctx, "order not found", zap.Any("curFreightID", freightID))
				return nil, nil
			}
			return nil, fmt.Errorf("error getting order from tms: %w", err)
		}

		// If order exists in TMS, get its associated load
		if order != nil && order.LoadID != 0 {
			load, err := dbGetLoadByFreightTrackingIDFunc(ctx, service.ID, fmt.Sprintf("%d", order.LoadID))
			if err == nil {
				return []models.Load{load}, nil
			}
		}
		// TODO: If order exists in TMS but has no associated load, we should handle this case
		// For now, return empty as we don't want to create a load from an order
		return nil, nil
	}

	// Aljex customer ref lookups take too long, so we instead only support searching the DB by customer ref
	// in the DB until we switch to polling architecture.
	if tmsIntegration.Name == models.Aljex && tmsFreightID.IDType == aljex.CustomerRefIDType {
		return nil, nil
	}

	// Turvo PO num regex is permissive and causing too many lookups, so disable Turvo PONum lookups for now.
	// Broader refactor coming by 1/31/2025.
	if tmsIntegration.Name == models.Turvo && tmsFreightID.IDType == turvo.PONumIDType {
		return nil, nil
	}

	if tmsIntegration.Name == models.Relay {
		// Staging load #'s (aka ExternalTMSID) start with 2, booking IDs (aka FreightTrackingID) with 7s But
		// regex has to be generalized to both since we don't know which kind of TMS we have until after the
		// regex
		if strings.EqualFold(tmsIntegration.Tenant, "training.relaytms.com") &&
			(strings.HasPrefix(tmsFreightID.ID, "3") || strings.HasPrefix(tmsFreightID.ID, "8")) {

			log.Info(
				ctx,
				"skipping lookup of prod ID in Relay staging",
				zap.String("tenant", tmsIntegration.Tenant),
				zap.Any("curFreightID", tmsFreightID),
			)

			return nil, nil

			// If Relay Prod, skip Aljex/Relay staging IDs (most common case)
		} else if !strings.EqualFold(tmsIntegration.Tenant, "training.relaytms.com") &&
			(strings.HasPrefix(tmsFreightID.ID, "2") || strings.HasPrefix(tmsFreightID.ID, "7")) {

			log.Info(
				ctx,
				"skipping lookup of staging ID in Relay prod",
				zap.String("tenant", tmsIntegration.Tenant),
				zap.Any("curFreightID", tmsFreightID),
			)

			return nil, nil
		}
	}

	if tmsIntegration.Name == models.Ascend && tmsFreightID.IDType == ascend.LoadIDType {
		redisKey := redisIDTypeKey(service, models.Ascend, tmsFreightID)

		str, found, err := redis.GetKey[string](ctx, redisKey)
		if err == nil && found && str != "" {
			// If we found the ID in Redis, that means we recently looked it up.
			// We have an Ascend poller and already checked the DB earlier so if the ref isn't there,
			// that means it's a 404.
			log.Info(
				ctx,
				"found ascend load ID in Redis, skipping TMS lookup",
				zap.Any("freightID", freightID),
			)

			return nil, nil
		}

		if err := redis.SetKey(ctx, redisKey, tmsFreightID.ID, RedisTMSIdentifierTTL); err != nil {
			log.WarnNoSentry(
				ctx,
				"error setting freightID in redis",
				zap.Any("freightID", freightID),
				zap.Error(err),
			)
		}
	}

	isMcleodRef := tmsIntegration.Name == models.McleodEnterprise &&
		tmsFreightID.IDType == mcleodenterprise.RefNumberIDType
	mcleodRefRedisKey := redisIDTypeKey(service, models.McleodEnterprise, tmsFreightID)

	isGlobalTranzTMSPONum := tmsIntegration.Name == models.GlobalTranzTMS &&
		tmsFreightID.IDType == globaltranztms.PoNumberType
	globalTranzTMSPONumRedisKey := redisIDTypeKey(service, models.GlobalTranzTMS, tmsFreightID)

	if isMcleodRef {
		str, found, err := redis.GetKey[string](ctx, mcleodRefRedisKey)
		if err == nil && found && str != "" {
			// If we found the ID in Redis, that means we recently looked it up and got a 404.
			log.Info(ctx, "found mcleod ref in Redis, skipping TMS lookup", zap.Any("freightID", freightID))

			return nil, nil
		}
	}

	if isGlobalTranzTMSPONum {
		str, found, err := redis.GetKey[string](ctx, globalTranzTMSPONumRedisKey)
		if err == nil && found && str != "" {
			log.Info(ctx, "found GlobalTranz TMS PONum in Redis, skipping TMS lookup", zap.Any("freightID", freightID))
			return nil, nil
		}
	}

	client, err := tms.New(ctx, tmsIntegration)
	if err != nil {
		return nil, fmt.Errorf("error creating new TMS client: %w", err)
	}

	loads, _, err := client.GetLoadsByIDType(ctx, tmsFreightID.ID, tmsFreightID.IDType)
	if err != nil {
		// Fallback in case GetLoadByIDType is not implemented
		if errtypes.IsNotImplementedError(err) {
			log.WarnNoSentry(ctx, err.Error()+", trying GetLoad instead")

			var load models.Load
			load, _, err = client.GetLoad(ctx, tmsFreightID.ID)
			if err == nil {
				return []models.Load{load}, nil
			}
		}

		// If we got a not found error from either GetLoadsByIDType or the GetLoad fallback, return it unwrapped.
		if errtypes.IsEntityNotFoundError(err) {
			// TODO: Remove McLeod when poller created. Then we only have to check the DB instead of checking TMS.
			if isMcleodRef || isGlobalTranzTMSPONum {
				redisKey := mcleodRefRedisKey
				errMsg := "error setting freightID in redis"

				if isGlobalTranzTMSPONum {
					redisKey = globalTranzTMSPONumRedisKey
					errMsg = "error setting GlobalTranz TMS PONum in redis"
				}

				if err := redis.SetKey(ctx, redisKey, tmsFreightID.ID, RedisTMSIdentifierTTL); err != nil {

					log.WarnNoSentry(
						ctx,
						"error setting freightID in redis",
						zap.Any("freightID", freightID),
						zap.Error(err),
					)
				} else {
					log.Debug(ctx, errMsg, zap.Any("freightID", freightID))
				}
			}
			return nil, err
		}

		return nil, fmt.Errorf(
			"error getting load from tms for idType: %s id: %s: %w",
			tmsFreightID.IDType,
			tmsFreightID.ID,
			err,
		)
	}

	return loads, nil
}

func getLoadFromDBByIDType(ctx context.Context, freightID tmsFreightID, service models.Service) (*models.Load, error) {
	switch freightID.IDType {
	// CustomerRefID and PROID are the same for Aljex
	case aljex.CustomerRefIDType, aljex.PROIDType:
		// Check if the customer ref is in Redis
		redisKey := redisIDTypeKey(service, models.Aljex, freightID)

		str, found, err := redis.GetKey[string](ctx, redisKey)
		if err == nil && found && str != "" {
			// If we found the ID in Redis, that means we recently looked it up in the DB and it wasn't there.
			log.Info(ctx, "found customer ref in Redis, skipping DB lookup", zap.Any("freightID", freightID))
			return nil, nil
		}

		load, err := dbGetLoadByCustomerRefFunc(ctx, freightID.ID, service.ID)
		if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
			err := redis.SetKey(ctx, redisKey, freightID.ID, RedisTMSIdentifierTTL)
			if err != nil {
				log.WarnNoSentry(
					ctx,
					"error setting freightID in redis",
					zap.Any("freightID", freightID),
					zap.Error(err),
				)
			}
		}

		return load, err

	case threeg.OrderNum:
		// First check if order exists in DB
		order, err := dbGetOrderByReferenceFunc(ctx, "ID", freightID.ID)
		if err == nil && order != nil {
			// If order exists, get its associated load
			if order.LoadID != 0 {
				load, err := dbGetLoadByFreightTrackingIDFunc(ctx, service.ID, fmt.Sprintf("%d", order.LoadID))
				if err == nil {
					return &load, nil
				}
			}
			// TODO: If order exists but has no associated load, we should handle this case
			// For now, return empty as we don't want to create a load from an order
			return nil, nil
		}
		return nil, err

	case turvo.PONumIDType:
		return dbGetLoadByPONumFunc(ctx, freightID.ID, service.ID)

	case quantumedge.FreightTrackingIDType:
		// For FreightTrackingID, check DB only (poller keeps DB updated)
		load, err := dbGetLoadByFreightTrackingIDFunc(ctx, service.ID, freightID.ID)
		return &load, err

	case quantumedge.RefNumberIDType:
		// For refNumber, check DB only (poller keeps DB updated)
		return dbGetLoadByCustomerRefFunc(ctx, freightID.ID, service.ID)

	case revenova.LoadIDType:
		// For Revenova LoadID, only check DB instead of TMS
		load, err := dbGetLoadByFreightTrackingIDFunc(ctx, service.ID, freightID.ID)
		return &load, err

	default:
		load, err := dbGetLoadByFreightTrackingIDFunc(ctx, service.ID, freightID.ID)
		if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
			log.WarnNoSentry(ctx, "load not found in DB, trying externalTMSID lookup", zap.Any("freightID", freightID))

			load, err = dbGetLoadByExternalTMSIDFunc(ctx, service.ID, freightID.ID)
		}

		return &load, err
	}

}

func redisIDTypeKey(service models.Service, tmsName models.IntegrationName, freightID tmsFreightID) string {
	return fmt.Sprintf("service-%d-tms-%s-idType-%s-id-%s", service.ID, tmsName, freightID.IDType, freightID.ID)
}

// Check if TMS error returned 404/Not Found, or a transient error like 429 or 5XX. If latter, create a placeholder
// load so that API can try again later when user opens email.
// Caller should explicitly handle "Not Found" case as that is neither transient nor an internal error.
func isTransientHTTPError(ctx context.Context, err error, freightID tmsFreightID) bool {
	ctx, metaSpan := otel.StartSpan(ctx, "isTransientHTTPError", nil)
	defer func() { metaSpan.End(nil) }()

	if err == nil {
		return false
	}

	if errtypes.IsEntityNotFoundError(err) {
		log.Info(ctx, "load not found", zap.Any("curFreightID", freightID))
		return false
	}

	var httpErr errtypes.HTTPResponseError
	if errors.As(err, &httpErr) &&
		(httpErr.StatusCode == http.StatusTooManyRequests || httpErr.StatusCode >= 500) {
		return true
	}

	return false
}

var (
	dbGetGeneratedEmailByRFCIDFunc = emailDB.GetBeaconGeneratedEmailByRFCID
	dbGetGeneratedEmailByThread    = emailDB.GetBeaconGeneratedEmailByThreadID
	dbUpsertEmailFunc              = emailDB.UpsertEmailAndLoads
)

// Checks if an email Drumkit sent via Outlook API bounced.
//   - EX 1: https://shorturl.at/chnpX
//   - EX 2 - recipient not found (requires admin access):
//
// TODO: add Gmail bounced logic
func handleOutlookBouncedEmail(
	ctx context.Context,
	processedMsg *models.Email,
) (res *models.Email, err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "handleOutlookBouncedEmail", nil)
	defer func() { metaSpan.End(err) }()

	didBounce := (strings.Contains(strings.ToLower(processedMsg.Subject), "undeliverable") &&
		strings.HasPrefix(strings.ToLower(processedMsg.Sender), "microsoftexchange"))

	if !didBounce {
		return nil, nil
	}

	var bouncedEmail *models.Email
	// Invalid recipient address(es). If the recipient is external
	// (i.e. address provided in Quick Quote form) then that's likely user error.
	// But customers' emails that are backend configured should not generate this issue and need to be addressed.
	switch {
	case strings.Contains(processedMsg.Body, "5.1.10"):
		bouncedEmail, err = dbGetGeneratedEmailByThread(ctx, processedMsg.ThreadID)
		if err != nil {
			return nil, fmt.Errorf("error getting drumkit-generated email by threadID: %w", err)
		}

	case (strings.Contains(processedMsg.Body, "5.7.703") ||
		strings.Contains(processedMsg.Body, "5.7.705") ||
		strings.Contains(processedMsg.Body, "5.7.708") ||
		strings.Contains(processedMsg.Body, "5.7.750")):
		// Docs: https://shorturl.at/pAIY6

		// Because the message was never sent, there's no threadID so instead we parse provided RFC ID
		rfcID := parseRFCMessageID(processedMsg.Body)
		if rfcID == "" {
			return nil, errors.New("parsed empty RFC Message ID")
		}
		log.Debug(ctx, "parsed RFC ID: "+rfcID)

		bouncedEmail, err = dbGetGeneratedEmailByRFCIDFunc(ctx, processedMsg.UserID, rfcID)
		if err != nil {
			// gorm.ErrRecordNotFound not expected behavior for an email we created
			return nil, fmt.Errorf("error getting email by RFC ID: %w", err)
		}

	default:
		return nil, errors.New("unable to identify bounced email, view email/logs for more information")
	}

	if !bouncedEmail.BeaconGenerated {
		return nil, nil
	}
	bouncedEmail.Bounced = true
	bouncedEmail.BouncedMessageID = &processedMsg.ID

	return bouncedEmail, dbUpsertEmailFunc(ctx, bouncedEmail)
}

func parseRFCMessageID(input string) string {
	re := regexp.MustCompile(`(<[^>]+prod.outlook.com>)`)

	match := re.FindStringSubmatch(input)

	if len(match) > 0 {
		return match[0]
	}

	return ""
}

// generateEmailEmbedding creates an embedding for the email subject and body.
func generateEmailEmbedding(ctx context.Context, email *models.Email) (models.VectorEmbedding, error) {
	ctx, metaSpan := otel.StartSpan(ctx, "generateEmailEmbedding", nil)
	defer func() { metaSpan.End(nil) }()

	openaiService, err := openai.NewService(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to create OpenAI service: %w", err)
	}

	embedding, err := openaiService.GetEmbedding(
		ctx,
		email.Subject+" "+email.BodyWithoutSignature,
	)
	if err != nil {
		return nil, err
	}

	return embedding, nil
}

// storeEmailEmbeddingWithTx stores an embedding for the email subject and body.
func storeEmailEmbeddingWithTx(
	ctx context.Context,
	tx *gorm.DB,
	email *models.Email,
	embedding models.VectorEmbedding,
) error {
	vectorRepo := rds.GetVectorRepository(ctx)
	if vectorRepo == nil {
		return errors.New("vector repository is nil")
	}

	err := vectorRepo.StoreEmailEmbeddingWithTx(ctx, tx, email, embedding)
	if err != nil {
		return fmt.Errorf("failed to store email embedding: %w", err)
	}

	log.Info(ctx, "Successfully stored email body embedding",
		zap.Uint("emailID", email.ID),
		zap.String("subject", email.Subject))

	return nil
}
