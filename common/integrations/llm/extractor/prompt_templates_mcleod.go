package extractor

//nolint:lll
const (
	mcleodPickupTimeInstructions = `* **readyTime**: Set to the date (e.g., "mm/dd/yyyy") of the primary pickup appointment. Do not include the time component, even if one is specified.
        * If only a date is visible (e.g., "Pickup: 09/12/2025"), set this to "09/12/2025".
        * If a date and time are visible (e.g., "Pickup: 09/12/2025, 9:00AM"), set this to "09/12/2025".

    * **apptType**: "By appointment", "FCFS", or appointment type. Infer "By appointment" if a specific time or window is provided.

    * **apptStartTime**: Set to the date AND start time (e.g., "mm/dd/yyyy, hh:MMam/pm") of the pickup appointment.
        * If only a date is provided (no time), set this field to just the date (e.g., "09/12/2025").
        * If a date and a single time are provided, set this field to that date and time (e.g., "09/12/2025, 09:00AM").
        * If a time window is provided, use the start of that window (e.g., "09/12/2025, 09:00AM").

    * **apptEndTime**: Set to the date AND end time (e.g., "mm/dd/yyyy, hh:MMam/pm") of the pickup appointment window.
	 	* If only a date is provided (no time), set this field to just the date (e.g., "09/12/2025").
        * If no end time is provided (e.g., only a date or a single start time), set this field to be **identical** to *apptStartTime*.
        * If a time window is provided, use the end of that window (e.g., "09/12/2025, 11:00AM").`

	mcleodPickupInstructions = `Here are some additional instructions for this pickup section. 
		- Pickup dates can be labeled as "Ship Date", "Ready Time", "Pickup Date", "Pickup Time", "Pickup Window", "Pickup Window Start", "Pickup Window End", etc. So make sure to 
		look for any pickup related keywords to grab at least the shipping date, since documents usually include at least the date. It is usually rare to not have a shipment pickup date..
		- *readyTime*, *apptStartTime*, and *apptEndTime* fields can be either in MM/DD/YYYY format or in MM/DD/YYYY, hh:MMam/pm format.`

	mcleodPickupExamples = `**Examples:**

### Example 1: Standard Pickup with Appointment
**Input:**
A shipping document contains:
SHIP FROM:
Mohawk Industries
1405 HWY 41 S
CALHOUN, GA 30701
Contact: Sarah Johnson
Phone: ************
Email: <EMAIL>
Ready Time: 08/26/2025, 8:00AM
Appointment Required: 9:00AM-10:00AM EDT
Special Instructions: Driver must check in at front desk

**Output:**
{
    "pickup": {
        "name": "Mohawk Industries",
        "addressLine1": "1405 HWY 41 S",
        "addressLine2": "",
        "city": "CALHOUN",
        "state": "GA",
        "zipCode": "30701",
        "country": "US",
        "contact": "Sarah Johnson",
        "phone": "************",
        "email": "<EMAIL>",
        "businessHours": "",
        "refNumber": "",
        "readyTime": "08/26/2025",
        "apptType": "By appointment",
        "apptStartTime": "08/26/2025, 09:00AM",
        "apptEndTime": "08/26/2025, 10:00AM",
        "apptNote": "Driver must check in at front desk",
        "timezone": "EDT",
        "refNumberCandidates": []
    }
}

### Example 2: Multiple Reference Numbers
**Input:**
Stop 1 (pickup)
Mobil, 1001 Billingsport Rd., Paulsboro, NJ 08066
Leah Scalise Phone: (*************
Pickup: 09/12/2025 07:00AM - 09/12/2025 03:00PM

- SN834918 (BOL)
- 2608089680 (Delivery/Order Number)  
- 4700829 (PO Number)
- LD385193 (Load ID)
- LD385193 (PRO)

**Output:**
{
    "pickup": {
        "name": "Mobil",
        "addressLine1": "1001 Billingsport Rd.",
        "addressLine2": "",
        "city": "Paulsboro",
        "state": "NJ",
        "zipCode": "08066",
        "country": "US",
        "contact": "Leah Scalise",
        "phone": "************",
        "email": "",
        "businessHours": "",
        "refNumber": "2608089680",
        "readyTime": "09/12/2025",
        "apptType": "By appointment",
        "apptStartTime": "09/12/2025, 07:00AM",
        "apptEndTime": "09/12/2025, 03:00PM",
        "apptNote": "",
        "timezone": "",
        "refNumberCandidates": [
            "2608089680",
            "4700829",
            "LD385193"
        ]
    }
}

### Example 3: Multiple Locations / Single Time
**Input:**
SHIP FROM:
Origin Warehouse LLC
500 Commerce Dr
Atlanta, GA 30309
Ready: 08/28/2025, 2:00PM

SHIP TO:
Destination Corp
789 Delivery St
Miami, FL 33101
Deliver by: 08/30/2025

**Output:**
{
    "pickup": {
        "name": "Origin Warehouse LLC",
        "addressLine1": "500 Commerce Dr",
        "addressLine2": "",
        "city": "Atlanta",
        "state": "GA",
        "zipCode": "30309",
        "country": "US",
        "contact": "",
        "phone": "",
        "email": "",
        "businessHours": "",
        "refNumber": "",
        "readyTime": "08/28/2025",
        "apptType": "By appointment",
        "apptStartTime": "08/28/2025, 02:00PM",
        "apptEndTime": "08/28/2025, 02:00PM",
        "apptNote": "",
        "timezone": "",
        "refNumberCandidates": []
    }
}

### Example 4: Ship Date Only
**Input:**
Origin:
Global Foods Inc
4500 Industrial Pkwy
Chicago, IL 60639
Ship Date: 09/30/2025
Pickup Ref: P-456123

**Output:**
{
    "pickup": {
        "name": "Global Foods Inc",
        "addressLine1": "4500 Industrial Pkwy",
        "addressLine2": "",
        "city": "Chicago",
        "state": "IL",
        "zipCode": "60639",
        "country": "US",
        "contact": "",
        "phone": "",
        "email": "",
        "businessHours": "",
        "refNumber": "P-456123",
        "readyTime": "09/30/2025",
        "apptType": "",
        "apptStartTime": "09/30/2025",
        "apptEndTime": "09/30/2025",
        "apptNote": "",
        "timezone": "",
        "refNumberCandidates": ["P-456123"]
    }
}`

	mcleodConsigneeTimeInstructions = `* **mustDeliver**: Set to the date (e.g., "mm/dd/yyyy") of the primary delivery appointment. Do not include the time component, even if one is specified.
        * If only a date is visible (e.g., "Delivery: 09/12/2025"), set this to "09/12/2025".
        * If a date and time are visible (e.g., "Appt: 09/12/2025, 9:00AM"), set this to "09/12/2025".

    * **apptType**: "By appointment", "FCFS", or appointment type. Infer "By appointment" if a specific time or window is provided.

    * **apptStartTime**: Set to the date AND start time (e.g., "mm/dd/yyyy, hh:MMam/pm") of the delivery appointment.
        * If only a date is provided (no time), set this field to just the date (e.g., "09/12/2025").
        * If a date and a single time are provided, set this field to that date and time (e.g., "09/12/2025, 09:00AM").
        * If a time window is provided, use the start of that window (e.g., "09/12/2025, 09:00AM").

    * **apptEndTime**: Set to the date AND end time (e.g., "mm/dd/yyyy, hh:MMam/pm") of the delivery appointment window.
	 	* If only a date is provided (no time), set this field to just the date (e.g., "09/12/2025").
        * If no end time is provided (e.g., only a date or a single start time), set this field to be **identical** to *apptStartTime*.
        * If a time window is provided, use the end of that window (e.g., "09/12/2025, 11:00AM").`

	mcleodConsigneeInstructions = `Here are some additional instructions for this consignee section. 
		- Delivery dates can be labeled as "Arrival Date", "Delivery Date", "Delivery Time", "Delivery Window", "Delivery Window Start", "Delivery Window End", etc. So make sure to 
		look for any delivery related keywords to grab at least the shipping date, since documents usually include at least the date. It is usually rare to not have a shipment delivery date..
		- *mustDeliver*, *apptStartTime*, and *apptEndTime* fields can be either in MM/DD/YYYY format or in MM/DD/YYYY, hh:MMam/pm format.`

	mcleodConsigneeExamples = `**Examples:**

	### Example 1: Standard Delivery with Appointment
**Input:**
A shipping document contains:
SHIP TO:
Acme Distribution Center
123 Main St, Suite 400
Springfield, IL 62704
Contact: John Doe
Phone: ************
Email: <EMAIL>
Business Hours: Mon-Fri, 8:00 AM - 5:00 PM
Delivery Appointment: 08/30/2025, 1:00PM-3:00PM CST
Special Instructions: Call 30 minutes before arrival
Delivery PO: DEL-987654
Main Shipment BOL: BOL-555123

**Output:**
{
    "consignee": {
        "name": "Acme Distribution Center",
        "addressLine1": "123 Main St",
        "addressLine2": "Suite 400",
        "city": "Springfield",
        "state": "IL",
        "zipCode": "62704",
        "country": "US",
        "contact": "John Doe",
        "phone": "************",
        "email": "<EMAIL>",
        "businessHours": "Mon-Fri, 8:00 AM - 5:00 PM",
        "refNumber": "DEL-987654",
        "mustDeliver": "08/30/2025",
        "apptType": "By appointment",
        "apptStartTime": "08/30/2025, 01:00PM",
        "apptEndTime": "08/30/2025, 03:00PM",
        "apptNote": "Call 30 minutes before arrival",
        "timezone": "CST",
        "externalTMSID": null,
        "refNumberCandidates": [
            "DEL-987654"
        ]
    }
}

### Example 2: Multiple Reference Numbers
**Input:**
Stop 2 (drop)
Moove, 8120 S. Orange Avenue, Orlando, FL 32809
Mike Dvorak Phone: ************
Delivery: 09/15/2025 07:00AM - 09/15/2025 03:00PM

- SN834918 (BOL)
- 2608089680 (Delivery/Order Number)
- 4700829 (PO Number)
- LD385193 (Load ID)
- LD385193 (PRO)

**Output:**
{
    "consignee": {
        "name": "Moove",
        "addressLine1": "8120 S. Orange Avenue",
        "addressLine2": "",
        "city": "Orlando",
        "state": "FL",
        "zipCode": "32809",
        "country": "US",
        "contact": "Mike Dvorak",
        "phone": "************",
        "email": "",
        "businessHours": "",
        "refNumber": "2608089680",
        "mustDeliver": "09/15/2025",
        "apptType": "By appointment",
        "apptStartTime": "09/15/2025, 07:00AM",
        "apptEndTime": "09/15/2025, 03:00PM",
        "apptNote": "",
        "timezone": "",
        "externalTMSID": null,
        "refNumberCandidates": [
            "2608089680",
            "4700829",
            "LD385193"
        ]
    }
}

### Example 3: Multiple Stops - Extract Final Destination
**Input:**
Stop 1 - Intermediate Stop:
Gamma Logistics Hub
789 Transfer Ave
Denver, CO 80202

Final Destination:
Delta Manufacturing
321 Factory Road
Salt Lake City, UT 84101
Contact: Robert Kim
Phone: ************
Delivery Window: 09/02/2025, 10:00AM-12:00PM MST

**Output:**
{
    "consignee": {
        "name": "Delta Manufacturing",
        "addressLine1": "321 Factory Road",
        "addressLine2": "",
        "city": "Salt Lake City",
        "state": "UT",
        "zipCode": "84101",
        "country": "US",
        "contact": "Robert Kim",
        "phone": "************",
        "email": "",
        "businessHours": "",
        "refNumber": "",
        "mustDeliver": "09/02/2025",
        "apptType": "By appointment",
        "apptStartTime": "09/02/2025, 10:00AM",
        "apptEndTime": "09/02/2025, 12:00PM",
        "apptNote": "",
        "timezone": "MST",
        "externalTMSID": null,
        "refNumberCandidates": []
    }
}

### Example 4: Arrival Date Only
**Input:**
DELIVER TO:
Target RDC
456 Distribution Way
Phoenix, AZ 85001
Contact: Maria Garcia
Phone: ************
Arrival Date: 09/20/2025
Ref: PO-78910

**Output:**
{
    "consignee": {
        "name": "Target RDC",
        "addressLine1": "456 Distribution Way",
        "addressLine2": "",
        "city": "Phoenix",
        "state": "AZ",
        "zipCode": "85001",
        "country": "US",
        "contact": "Maria Garcia",
        "phone": "************",
        "email": "",
        "businessHours": "",
        "refNumber": "PO-78910",
        "mustDeliver": "09/20/2025",
        "apptType": "By appointment",
        "apptStartTime": "09/20/2025",
        "apptEndTime": "09/20/2025",
        "apptNote": "",
        "timezone": "",
        "externalTMSID": null,
        "refNumberCandidates": [
            "PO-78910"
        ]
    }
}`
)
