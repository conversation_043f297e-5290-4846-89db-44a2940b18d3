package extractor

//nolint:lll
const (
	aljexBusinessHoursInstructions = `**Important Note**: This is a definition override of the *businessHours* field. businessHours should strictly be in 24 hour time format of (0800-1600) Extract the business hours if specified, for example, 8:30am - 4:20pm should be represented as (0830-1620), or 24/7 should be represented as (0000-2400). The number of characters in this string should not be over 9 characters long. Do not include anything in this field that is not a 24 hour time (0230) or a separating dash (-).`

	aljexPickupTimeInstructions = `* **readyTime**: This field should represent the earliest time the pickup can begin. This can come from any field indicating the start of pickup, such as "ready time," "pickup date," "start date," or anything referencing the appointment window.
    If the pickup section only has a date associated with it, set this field to only the date. If a date AND start time is provided for the pickup section (Ex: "Pickup Date: 09/15/2025, 9:00AM"), set it as the date and time. Format as "mm/dd/yyyy, hh:MMam/pm" or just "mm/dd/yyyy". Do NOT infer from appointment times.
    * **apptType**: "By appointment", "FCFS", or appointment type
    * **apptStartTime**: ONLY set when there is an explicit appointment or scheduled window start time. Format as "mm/dd/yyyy, hh:MMam/pm" or just "mm/dd/yyyy".
    * **apptEndTime**: ONLY set when there is an explicit appointment or scheduled window end time. Format as "mm/dd/yyyy, hh:MMam/pm" or just "mm/dd/yyyy".`

	aljexPickupExamples = `**Examples:**

### Example 1: Standard Pickup with Appointment
**Input:**
A shipping document contains:
SHIP FROM:
Mohawk Industries
1405 HWY 41 S
CALHOUN, GA 30701
Contact: Sarah Johnson
Phone: ************
Email: <EMAIL>
Ready Time: 08/26/2025, 8:00AM
Appointment Required: 9:00AM-10:00AM EDT
Special Instructions: Driver must check in at front desk

**Output:**
{
    "pickup": {
        "name": "Mohawk Industries",
        "addressLine1": "1405 HWY 41 S",
        "addressLine2": "",
        "city": "CALHOUN",
        "state": "GA",
        "zipCode": "30701",
        "country": "US",
        "contact": "Sarah Johnson",
        "phone": "************",
        "email": "<EMAIL>",
        "businessHours": "",
        "refNumber": "",
        "readyTime": "08/26/2025, 08:00AM",
        "apptType": "By appointment",
        "apptStartTime": "08/26/2025, 09:00AM",
        "apptEndTime": "08/26/2025, 10:00AM",
        "apptNote": "Driver must check in at front desk",
        "timezone": "EDT",
        "refNumberCandidates": []
    }
}

### Example 2: Multiple Reference Numbers
**Input:**
Stop 1 (pickup)
Mobil, 1001 Billingsport Rd., Paulsboro, NJ 08066
Leah Scalise Phone: (*************
Pickup: 09/12/2025 07:00AM - 09/12/2025 03:00PM

- SN834918 (BOL)
- 2608089680 (Delivery/Order Number)  
- 4700829 (PO Number)
- LD385193 (Load ID)
- LD385193 (PRO)

**Output:**
{
    "pickup": {
        "name": "Mobil",
        "addressLine1": "1001 Billingsport Rd.",
        "addressLine2": "",
        "city": "Paulsboro",
        "state": "NJ",
        "zipCode": "08066",
        "country": "US",
        "contact": "Leah Scalise",
        "phone": "************",
        "email": "",
        "businessHours": "",
        "refNumber": "2608089680",
        "readyTime": "09/12/2025, 07:00AM",
        "apptType": "By appointment",
        "apptStartTime": "09/12/2025, 07:00AM",
        "apptEndTime": "09/12/2025, 03:00PM",
        "apptNote": "",
        "timezone": "",
        "refNumberCandidates": [
            "2608089680",
            "4700829",
            "LD385193"
        ]
    }
}

### Example 3: Multiple Locations - Extract Origin Only
**Input:**
SHIP FROM:
Origin Warehouse LLC
500 Commerce Dr
Atlanta, GA 30309
Ready: 08/28/2025, 2:00PM

SHIP TO:
Destination Corp
789 Delivery St
Miami, FL 33101
Deliver by: 08/30/2025

**Output:**
{
    "pickup": {
        "name": "Origin Warehouse LLC",
        "addressLine1": "500 Commerce Dr",
        "addressLine2": "",
        "city": "Atlanta",
        "state": "GA",
        "zipCode": "30309",
        "country": "US",
        "contact": "",
        "phone": "",
        "email": "",
        "businessHours": "",
        "refNumber": "",
        "readyTime": "08/28/2025, 02:00PM",
        "apptType": "",
        "apptStartTime": "08/28/2025, 02:00PM",
        "apptEndTime": "",
        "apptNote": "",
        "timezone": "",
        "refNumberCandidates": []
    }
}

### Example 4: Discrete Date and Time Fields
**Input:**
A document structured like a form:

SHIPPER:
Acme Logistics
123 Industrial Way
Dallas, TX 75201
Contact: Tom Smith (************)
PO: 998-A7

PICKUP DETAILS
Start Date: 09/15/2025
Start Time: 13:00
End Time: 17:00 CST
Notes: Must use dock 5.

**Output:**
{
    "pickup": {
        "name": "Acme Logistics",
        "addressLine1": "123 Industrial Way",
        "addressLine2": "",
        "city": "Dallas",
        "state": "TX",
        "zipCode": "75201",
        "country": "US",
        "contact": "Tom Smith",
        "phone": "************",
        "email": "",
        "businessHours": "",
        "refNumber": "998-A7",
        "readyTime": "09/15/2025, 01:00PM",
        "apptType": "By appointment",
        "apptStartTime": "09/15/2025, 01:00PM",
        "apptEndTime": "09/15/2025, 05:00PM",
        "apptNote": "Must use dock 5.",
        "timezone": "CST",
        "refNumberCandidates": [
            "998-A7"
        ]
    }
}`

	aljexConsigneeTimeInstructions = `* **mustDeliver**: Set this to the date of the delivery. This date can be taken from any field indicating the delivery day, such as an appointment window, a "deliver by" deadline, or a general "delivery date." Format as "mm/dd/yyyy" ONLY. Do not include the time, even if it is available.
	    * **apptType**: "By appointment", "FCFS", or appointment type
	    * **apptStartTime**: ONLY set when there is an explicit delivery appointment or scheduled window start time. Format as "mm/dd/yyyy, hh:MMam/pm" or just "mm/dd/yyyy".
	    * **apptEndTime**: ONLY set when there is an explicit delivery appointment or scheduled window end time. Format as "mm/dd/yyyy, hh:MMam/pm" or just "mm/dd/yyyy".`

	aljexConsigneeExamples = `**Examples:**

    ### Example 1: Standard Delivery with Appointment
    **Input:**
    A shipping document contains:
    SHIP TO:
    Acme Distribution Center
    123 Main St, Suite 400
    Springfield, IL 62704
    Contact: John Doe
    Phone: ************
    Email: <EMAIL>
    Business Hours: Mon-Fri, 8:00 AM - 5:00 PM
    Delivery Appointment: 08/30/2025, 1:00PM-3:00PM CST
    Special Instructions: Call 30 minutes before arrival
    Delivery PO: DEL-987654
    Main Shipment BOL: BOL-555123

    **Output:**
    {
        "consignee": {
            "name": "Acme Distribution Center",
            "addressLine1": "123 Main St",
            "addressLine2": "Suite 400",
            "city": "Springfield",
            "state": "IL",
            "zipCode": "62704",
            "country": "US",
            "contact": "John Doe",
            "phone": "************",
            "email": "<EMAIL>",
            "businessHours": "Mon-Fri, 8:00 AM - 5:00 PM",
            "refNumber": "DEL-987654",
            "mustDeliver": "08/30/2025",
            "apptType": "By appointment",
            "apptStartTime": "08/30/2025, 01:00PM",
            "apptEndTime": "08/30/2025, 03:00PM",
            "apptNote": "Call 30 minutes before arrival",
            "timezone": "CST",
            "externalTMSID": null,
            "refNumberCandidates": [
                "DEL-987654"
            ]
        }
    }

    ### Example 2: Multiple Reference Numbers
    **Input:**
    Stop 2 (drop)
    Moove, 8120 S. Orange Avenue, Orlando, FL 32809
    Mike Dvorak Phone: ************
    Delivery: 09/15/2025 07:00AM - 09/15/2025 03:00PM

    - SN834918 (BOL)
    - 2608089680 (Delivery/Order Number)
    - 4700829 (PO Number)
    - LD385193 (Load ID)
    - LD385193 (PRO)

    **Output:**
    {
        "consignee": {
            "name": "Moove",
            "addressLine1": "8120 S. Orange Avenue",
            "addressLine2": "",
            "city": "Orlando",
            "state": "FL",
            "zipCode": "32809",
            "country": "US",
            "contact": "Mike Dvorak",
            "phone": "************",
            "email": "",
            "businessHours": "",
            "refNumber": "2608089680",
            "mustDeliver": "09/15/2025",
            "apptType": "By appointment",
            "apptStartTime": "09/15/2025, 07:00AM",
            "apptEndTime": "09/15/2025, 03:00PM",
            "apptNote": "",
            "timezone": "",
            "externalTMSID": null,
            "refNumberCandidates": [
                "2608089680",
                "4700829",
                "LD385193"
            ]
        }
    }

    ### Example 3: Multiple Stops - Extract Final Destination
    **Input:**
    Stop 1 - Intermediate Stop:
    Gamma Logistics Hub
    789 Transfer Ave
    Denver, CO 80202

    Final Destination:
    Delta Manufacturing
    321 Factory Road
    Salt Lake City, UT 84101
    Contact: Robert Kim
    Phone: ************
    Delivery Window: 09/02/2025, 10:00AM-12:00PM MST

    **Output:**
    {
        "consignee": {
            "name": "Delta Manufacturing",
            "addressLine1": "321 Factory Road",
            "addressLine2": "",
            "city": "Salt Lake City",
            "state": "UT",
            "zipCode": "84101",
            "country": "US",
            "contact": "Robert Kim",
            "phone": "************",
            "email": "",
            "businessHours": "",
            "refNumber": "",
            "mustDeliver": "09/02/2025",
            "apptType": "By appointment",
            "apptStartTime": "09/02/2025, 10:00AM",
            "apptEndTime": "09/02/2025, 12:00PM",
            "apptNote": "",
            "timezone": "MST",
            "externalTMSID": null,
            "refNumberCandidates": []
        }
    }

    ### Example 4: Discrete Date and Time Fields (Form)
    **Input:**
    A document structured like a form:

    CONSIGNEE:
    Global Widgets Inc.
    789 Destination Blvd
    Miami, FL 33101
    Contact: Maria Garcia (************)
    Order #: 772-B1

    FINAL STOP DETAILS (DELIVERY)
    Date: 10/25/2025
    Start Time: 14:00
    End Time: 16:30 EST
    Notes: Deliver to receiving dock 7.

    **Output:**
    {
        "consignee": {
            "name": "Global Widgets Inc.",
            "addressLine1": "789 Destination Blvd",
            "addressLine2": "",
            "city": "Miami",
            "state": "FL",
            "zipCode": "33101",
            "country": "US",
            "contact": "Maria Garcia",
            "phone": "************",
            "email": "",
            "businessHours": "",
            "refNumber": "772-B1",
            "mustDeliver": "10/25/2025",
            "apptType": "By appointment",
            "apptStartTime": "10/25/2025, 02:00PM",
            "apptEndTime": "10/25/2025, 04:30PM",
            "apptNote": "Deliver to receiving dock 7.",
            "timezone": "EST",
            "externalTMSID": null,
            "refNumberCandidates": [
                "772-B1"
            ]
        }
    }`
)
