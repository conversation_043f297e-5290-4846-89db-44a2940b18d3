# Stark TMS Integration - Frontend Documentation

## 1. Load Creation

### Yes, load creation is supported for Stark

**API Endpoint:**
- GraphQL mutation: `CreateShipment`
- Endpoint: `POST /marketplace?op=CreateShipment`

### Required Fields

**Top-level required fields:**
- `shipperApplicationId` (string) - Maps from `Load.Customer.ExternalTMSID`
- `equipmentType` (string) - Maps from `Load.Specifications.TransportType` or `TransportTypeEnum`
  - Supported values: `"_53_van"`, `"_53_reefer"`, `"flatbed"`
  - Default: `"_53_van"` if not specified
- `stops` (array) - At least one stop (pickup or delivery) is required
- `totalWeight` (integer) - Maps from `Load.Specifications.TotalWeight.Val` (in lbs)
- `totalPalletCount` (integer) - Maps from `Load.Specifications.TotalInPalletCount`

**Stop required fields (each stop in `stops` array):**
- `stopType` (string) - `"pickup"` or `"delivery"`
- `stopSequence` (integer) - Order of stops, starting from 1
- `shipperFacilityUuid` (string) - Maps from `Pickup.ExternalTMSID` or `Consignee.ExternalTMSID` or `Stop.ExternalTMSID`
- `commodity` (string) - Maps from first item in `Load.Commodities[0].Description` or can be empty string
- `dropTrailer` (boolean) - Default: `false`

### Optional Fields

**Top-level optional fields:**
- `equipmentAttributes` (array) - Equipment-specific attributes (currently empty, TODO for temp control)
- `hot` (boolean) - Currently hardcoded to `false` (TODO: map from load model)
- `team` (boolean) - Currently hardcoded to `false` (TODO: map from load if available)
- `opsRepId` (string) - Maps from `TMSUser.ExternalTMSID` or uses integration's `userID`
- `shipperRate` (float) - Maps from `Load.RateData.CustomerLineHaulCharge.Val` or `CustomerTotalCharge.Val`
- `shipperRateType` (string) - Maps from `Load.RateData.CustomerRateType`, defaults to `"spot"`
  - Supported values: `"spot"`, `"contract_primary"` (TODO: add more mappings)
- `loadingType` (string) - Maps from `Load.Specifications.LoadingType` (e.g., `"palletized"`)

**Stop optional fields (in `appointmentInput` object):**
- `appointmentType` (string) - `"appointment"` or `"range"` (maps from `ApptType`)
- `startAtDate` (string) - Format: `"YYYY-MM-DD"` (maps from `ApptStartTime`)
- `startAtTime` (string) - Format: `"HH:MM"` (optional, only included if time is not 00:00)
- `status` (string) - `"confirmed"` or `"unconfirmed"` (maps from `ApptConfirmed`)

### Character Limits & Validation Rules

**No explicit character limits documented**, but based on typical GraphQL/API practices:
- String fields should follow reasonable limits (typically 255-1000 chars)
- `commodity` field can be empty string
- `shipperFacilityUuid` must be a valid UUID format
- `totalWeight` must be a positive integer
- `totalPalletCount` must be a non-negative integer
- `stopSequence` must be a positive integer

**Date/Time Validation:**
- `startAtDate` must be in `YYYY-MM-DD` format
- `startAtTime` must be in `HH:MM` format (24-hour)
- If `startAtTime` is omitted, only date is used

### Multi-stop Support

- Supports multiple stops via `Load.Stops` array
- Stops are ordered by `stopSequence` (1, 2, 3, ...)
- Each stop must have a unique `shipperFacilityUuid`

---

## 2. Load Viewing/Editing

### Default Form

**You can use the default form** - Stark follows the standard Load model structure.

### Stark-Specific Field Requirements

**Read-Only Fields** (from `LoadAttributes`):
- `ExternalTMSID` - Set by Stark after creation
- `FreightTrackingID` - Set by Stark after creation
- `Status` - Managed by Stark
- `Mode` - Managed by Stark
- `MoreThanTwoStops` - Calculated from stops array
- All `Customer` fields (name, address, contact info)
- All `Pickup`/`Consignee` location fields (name, address, contact info)
- All `Carrier` fields
- `Specifications.ServiceType`, `TransportSize`
- `Specifications.TotalOutPalletCount`, `TotalPieces`, `Commodities`, `NumCommodities`

**Editable Fields** (from `LoadAttributes`):
- `PONums` - Purchase order numbers
- `Operator` - Operator assignment
- `Pickup.RefNumber` - PO reference for pickup stop
- `Pickup.ReadyTime` - Ready time
- `Pickup.ApptStartTime` - Appointment start time
- `Pickup.ApptEndTime` - Appointment end time
- `Pickup.ApptType` - Appointment type (`"appointment"`, `"range"`, `"window"`)
- `Pickup.ApptNote` - Appointment notes/special instructions
- `Consignee.RefNumber` - PO reference for delivery stop
- `Consignee.MustDeliver` - Must deliver by date
- `Consignee.ApptStartTime` - Appointment start time
- `Consignee.ApptEndTime` - Appointment end time
- `Consignee.ApptType` - Appointment type
- `Consignee.ApptNote` - Appointment notes/special instructions
- `Specifications.TotalWeight` - Total weight (lbs)
- `Specifications.TotalInPalletCount` - Pallet count
- `Specifications.TotalDistance` - Miles (maps to `shipperMiles` in Stark)
- `Specifications.TransportType` - Equipment type
- `Specifications.TransportTypeEnum` - Equipment type enum

**Not Supported Fields**:
- `BillTo` - All fields not supported
- `Customer.RefNumberCandidates` - Not supported
- `Pickup.BusinessHours` - Not supported
- `Pickup.ApptRequired` - Not supported (use `ApptType` instead)
- `Consignee.BusinessHours` - Not supported
- `Consignee.ApptRequired` - Not supported
- All `Carrier` fields - Not supported for updates
- `Specifications.OrderType`, `BillableWeight`, `IsRefrigerated`, `MinTempFahrenheit`, `MaxTempFahrenheit`
- `Specifications.LiftgatePickup`, `LiftgateDelivery`, `InsidePickup`, `InsideDelivery`
- `Specifications.Tarps`, `Oversized`, `Straps`, `Permits`, `Escorts`, `Seal`, `CustomBonded`, `Labor`

### Character Limits

**No explicit limits documented**, but follow standard practices:
- Text fields: reasonable limits (255-1000 chars)
- `ApptNote` (special instructions): Can be long text (based on examples, supports multi-line)

### Validation Rules

**Appointment Updates:**
- `ApptStartTime` must be valid datetime
- `ApptType` must be one of: `"appointment"`, `"range"`, `"window"`
- If `ApptType` is `"range"` or `"window"`, both `ApptStartTime` and `ApptEndTime` should be provided

**Stop Updates:**
- `ExternalTMSStopID` must be set for stop updates (from `Pickup.ExternalTMSStopID` or `Consignee.ExternalTMSStopID`)
- `RefNumber` (PO reference) can be updated via `UpdateShipmentStops`

**Basic Info Updates:**
- `TotalWeight` must be positive integer
- `TotalInPalletCount` must be non-negative integer
- `TotalDistance` (miles) must be positive number

**Equipment Updates:**
- `TransportType` must map to valid Stark equipment type: `"_53_van"`, `"_53_reefer"`, `"flatbed"`

### Date/Time Handling

**No special timezone handling** - Stark uses IANA timezones:
- Appointment times are stored with timezone information
- When updating appointments, times should be in the stop's timezone
- Default timezone: `"America/New_York"` (if not specified)

**Date Formats:**
- For creation: `startAtDate` uses `YYYY-MM-DD` format
- For creation: `startAtTime` uses `HH:MM` format (24-hour)
- For updates: Appointment times use standard datetime format

---

## 3. API Integration

### Endpoints

**Creating Loads:**
- Method: `POST`
- Endpoint: `https://staging.transfix.io/marketplace` (staging) or `https://transfix.io/marketplace` (production)
- Query param: `?op=CreateShipment`
- Type: GraphQL mutation
- Response: Returns `shipment.id` (the new shipment ID)

**Updating Loads:**
- Method: `POST`
- Endpoint: Same as above
- Query params:
  - `?op=UpdateShipmentBasicInfo` - For weight, pallets, miles, hot, mustGo
  - `?op=UpdateShipmentEquipment` - For equipment type
  - `?op=UpdateShipmentStops` - For appointments, special instructions, PO references
- Type: GraphQL mutations
- Response: Returns updated shipment data or errors

**Fetching Load Data:**
- Method: `POST`
- Endpoint: Same as above
- Query param: `?op=ShipmentDetailsStatus`
- Type: GraphQL query
- Variables: `{"id": "<shipment_id>"}`
- Response: Full shipment details including stops, equipment, characteristics, etc.

**Fetching Load IDs (Search):**
- Method: `GET`
- Endpoint: `https://staging.transfix.io/stark/v8/shipments` (staging) or `https://app.transfix.io/stark/v8/shipments` (production)
- Query params: `filters` (JSON-encoded), `sort_field`, `sort_order`, `page_count`
- Type: REST API
- Response: Array of shipment IDs

### Authentication/Authorization

**Required Headers:**
- `x-user-token` (string) - User's API token
- `x-user-email` (string) - User's email
- `x-user-id` (string) - User's ID
- `x-rogers-revision` (string) - API revision/version
- `content-type: application/json`
- `accept: */*`

**Note:** Currently, authentication requires manual setup of these headers. Okta authentication flow is not yet implemented. Headers must be stored in the integration's `Note` field during onboarding in the format:
```
user_id: <user_id>, rogers_revision: <revision>
```

**Environment Detection:**
- Staging: `AppID == "staging"` or `Tenant` contains "staging"
- Production: Otherwise

---

## 4. Field Mappings

### Standard Load → Stark Mappings

**Customer:**
- `Load.Customer.ExternalTMSID` → `shipperApplicationId` (required for creation)

**Equipment:**
- `Load.Specifications.TransportType` or `TransportTypeEnum` → `equipmentType`
  - `VanTransportType` → `"_53_van"`
  - `ReeferTransportType` → `"_53_reefer"`
  - `FlatbedTransportType` → `"flatbed"`
  - Default: `"_53_van"`

**Weight & Dimensions:**
- `Load.Specifications.TotalWeight.Val` → `totalWeight` (integer, lbs)
- `Load.Specifications.TotalInPalletCount` → `totalPalletCount` (integer)

**Distance:**
- `Load.Specifications.TotalDistance.Val` → `shipperMiles` (for updates)

**Rate:**
- `Load.RateData.CustomerLineHaulCharge.Val` or `CustomerTotalCharge.Val` → `shipperRate` (float)
- `Load.RateData.CustomerRateType` → `shipperRateType` (defaults to `"spot"`)

**Stops:**
- `Load.Pickup.ExternalTMSID` → `stops[].shipperFacilityUuid` (for pickup)
- `Load.Consignee.ExternalTMSID` → `stops[].shipperFacilityUuid` (for delivery)
- `Load.Stops[].ExternalTMSID` → `stops[].shipperFacilityUuid` (for additional stops)
- `Load.Pickup.ExternalTMSStopID` → `stops[].id` (for updates)
- `Load.Consignee.ExternalTMSStopID` → `stops[].id` (for updates)

**Appointments:**
- `Load.Pickup.ApptStartTime` → `appointmentInput.startAtDate` and `startAtTime`
- `Load.Pickup.ApptType` → `appointmentInput.appointmentType` (`"appointment"` or `"range"`)
- `Load.Pickup.ApptConfirmed` → `appointmentInput.status` (`"confirmed"` or `"unconfirmed"`)
- Same mapping for `Consignee`

**Commodity:**
- `Load.Commodities[0].Description` → `stops[].commodity` (first commodity description)

**Special Instructions:**
- `Load.Pickup.ApptNote` → `stops[].specialInstruction` (for updates)
- `Load.Consignee.ApptNote` → `stops[].specialInstruction` (for updates)

**PO References:**
- `Load.Pickup.RefNumber` → `stops[].poReference` (for updates)
- `Load.Consignee.RefNumber` → `stops[].poReference` (for updates)

**Characteristics:**
- `Load.LoadCoreInfo.AdditionalReferences` with `Qualifier == "HOT"` → `hot` (boolean)
- `Load.LoadCoreInfo.AdditionalReferences` with `Qualifier == "MUST_GO"` → `mustGo` (boolean)

### Stark → Standard Load Mappings

**Shipment ID:**
- `shipment.id` → `Load.ExternalTMSID` and `Load.FreightTrackingID`

**Status:**
- `shipment.state` → `Load.Status`

**Equipment:**
- `shipment.equipment.name` → `Load.Specifications.TransportType`
- `shipment.equipment.parentKey` → Used to map to `TransportTypeEnum`

**Weight & Dimensions:**
- `shipment.totalWeight` → `Load.Specifications.TotalWeight.Val`
- `shipment.totalPalletCount` → `Load.Specifications.TotalInPalletCount`

**Distance:**
- `shipment.calculatedMiles` or `shipment.shipperMiles` → `Load.Specifications.TotalDistance.Val`

**Stops:**
- `shipment.stops[]` → `Load.Stops[]` (all stops)
- `shipment.firstPickup` → `Load.Pickup`
- `shipment.lastDelivery` → `Load.Consignee`
- `stop.id` → `Pickup.ExternalTMSStopID` or `Consignee.ExternalTMSStopID`
- `stop.specialInstruction` → `Pickup.ApptNote` or `Consignee.ApptNote`
- `stop.note` → Additional note
- `stop.lateReason` → Additional note

**Appointments:**
- `stop.appointment.startAt` → `Pickup.ApptStartTime` or `Consignee.ApptStartTime`
- `stop.appointment.endAt` → `Pickup.ApptEndTime` or `Consignee.ApptEndTime`
- `stop.appointment.appointmentType` → `Pickup.ApptType` or `Consignee.ApptType`
- `stop.appointment.timezone` → `Pickup.Timezone` or `Consignee.Timezone`

**BOL/PO References:**
- `shipment.bolReferences[]` → `Load.LoadCoreInfo.AdditionalReferences` with `Qualifier == "BOL"`
- `shipment.poReferences[]` → `Load.LoadCoreInfo.AdditionalReferences` with `Qualifier == "PO"`

**Characteristics:**
- `shipment.characteristics.hot` → `Load.LoadCoreInfo.AdditionalReferences` with `Qualifier == "HOT"`
- `shipment.characteristics.mustGo` → `Load.LoadCoreInfo.AdditionalReferences` with `Qualifier == "MUST_GO"`

**Customer:**
- `shipment.shipperApplication.id` → `Load.Customer.ExternalTMSID`
- `shipment.shipperApplication.name` → `Load.Customer.Name`

### Special Handling

**Multi-stop Loads:**
- All stops from `shipment.stops[]` are populated into `Load.Stops[]`
- If `len(stops) > 2`, `Load.MoreThanTwoStops` is set to `true`
- First pickup stop is also mapped to `Load.Pickup`
- Last delivery stop is also mapped to `Load.Consignee`

**Equipment Attributes:**
- Currently not fully mapped (TODO for temp control, liftgate, etc.)
- For reefer loads, temp control attributes can be added to `equipmentAttributes` array

**Rate Type Mapping:**
- Currently defaults to `"spot"` for all rate types
- TODO: Add more mappings for `"contract_primary"` and other rate types

**Appointment Status:**
- `ApptConfirmed == true` → `status: "confirmed"`
- `ApptConfirmed == false` → `status: "unconfirmed"`

**Appointment Type:**
- `ApptType == "range"` or `"window"` → `appointmentType: "range"`
- Otherwise → `appointmentType: "appointment"`

---

## Additional Notes

1. **Error Handling:** All GraphQL mutations return an `errors` array. Check for errors and display user-friendly messages.

2. **Pagination:** `GetLoadIDs` supports pagination via `page_count` parameter. Fetch all pages to get complete results.

3. **Stop Updates:** When updating stops, you must provide the `stop.id` (from `ExternalTMSStopID`). The system fetches the current load first to get these IDs.

4. **Partial Updates:** `UpdateLoad` only updates fields that have changed. It checks for changes in:
   - Basic info (weight, pallets, miles, hot, mustGo)
   - Equipment type
   - Stops/appointments

5. **Default Values:**
   - `hot`: Currently hardcoded to `false` in creation
   - `team`: Currently hardcoded to `false` in creation
   - `shipperRateType`: Defaults to `"spot"` if not specified
   - `equipmentType`: Defaults to `"_53_van"` if not specified

6. **Environment URLs:**
   - Staging: `https://staging.transfix.io`
   - Production: `https://app.transfix.io` (REST) or `https://transfix.io` (GraphQL)

