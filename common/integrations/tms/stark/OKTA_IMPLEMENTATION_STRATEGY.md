# Okta Authentication Implementation Strategy

## ✅ Recommended Approach: Use Okta API Directly

**Use the Okta Authentication API** - it's the standard, documented way to authenticate programmatically.

### Why This is Better Than Scraping:

1. **Reliability**: Okta API is stable and well-documented
2. **Maintainability**: Won't break if Stark changes their UI
3. **Security**: Follows OAuth2/OIDC best practices
4. **Support**: Official Okta documentation and support
5. **Standard**: Same approach used by other integrations (GlobalTranz, etc.)

### What You Need to Do:

1. **Scrape minimally** - Just to find configuration:
   - Okta tenant URL
   - Client ID (if OAuth)
   - Redirect URI
   - How Stark exchanges Okta session for API token

2. **Use standard Okta endpoints**:
   - `/api/v1/authn` - For username/password authentication
   - `/api/v1/authn/factors/{factorId}/verify` - For MFA
   - `/oauth2/v1/authorize` - If using OAuth flow
   - `/oauth2/v1/token` - For token exchange

## Implementation Options

### Option 1: Direct Authentication API (Simplest) ⭐ Recommended

**Best for**: Non-browser applications, programmatic access

```go
// Step 1: Authenticate with Okta
POST https://<tenant>.okta.com/api/v1/authn
{
  "username": "<EMAIL>",
  "password": "password"
}

// Response if MFA required:
{
  "status": "MFA_REQUIRED",
  "stateToken": "xxx",
  "_embedded": {
    "factors": [
      {
        "id": "sms123",
        "factorType": "sms",
        "provider": "OKTA"
      }
    ]
  }
}

// Step 2: Verify MFA (if required)
POST https://<tenant>.okta.com/api/v1/authn/factors/sms123/verify
{
  "stateToken": "xxx",
  "passCode": "123456"
}

// Response:
{
  "status": "SUCCESS",
  "sessionToken": "xxx"
}

// Step 3: Exchange sessionToken for Stark API token
// This is where you need to scrape - how does Stark do this?
// Options:
// - POST to Stark callback endpoint with sessionToken
// - Use sessionToken to get session cookie, then call Stark API
// - Exchange via OAuth token endpoint
```

### Option 2: OAuth2 Authorization Code Flow

**Best for**: If Stark requires OAuth flow with redirects

```go
// Step 1: Get authorization URL
GET https://<tenant>.okta.com/oauth2/v1/authorize?
  client_id=<client_id>&
  redirect_uri=<redirect_uri>&
  response_type=code&
  scope=openid+profile&
  state=<random_state>

// Step 2: User authenticates (redirects to Okta login)
// Step 3: Okta redirects back with code
// Step 4: Exchange code for token
POST https://<tenant>.okta.com/oauth2/v1/token
{
  "grant_type": "authorization_code",
  "code": "xxx",
  "redirect_uri": "xxx",
  "client_id": "xxx",
  "client_secret": "xxx" // if required
}

// Response:
{
  "access_token": "xxx",
  "token_type": "Bearer",
  "expires_in": 3600,
  "id_token": "xxx"
}
```

## What to Scrape (Quick Checklist)

When you open the browser network tab, look for:

- [x] **Okta tenant URL**: `https://nfi.okta.com` ✅ **FOUND**
- [x] **Client ID**: `okta.2b1959c8-bcc0-56eb-a589-cfcfb7422f26` ✅ **FOUND**
- [x] **Redirect URI**: `https://nfi.okta.com/enduser/callback` ✅ **FOUND**
- [x] **Token endpoint**: `/oauth2/v1/token` ✅ **FOUND**
- [x] **User info endpoint**: `/enduser/api/v1/home` ✅ **FOUND** (returns `userId` which is `x-user-id`)
- [x] **OAuth2 flow**: Authorization Code with PKCE ✅ **FOUND**
- [ ] **MFA flow**: How is MFA handled? (usually `/api/v1/authn/factors/{id}/verify`)
- [ ] **Stark token exchange**: After Okta auth, what endpoint gives you the Stark API token (`x-user-token`)?
- [ ] **Revision header**: Where does `x-rogers-revision` come from?

## Key Questions to Answer

1. **Does Stark use standard Okta endpoints?**
   - Check if requests go to `*.okta.com/api/v1/authn`
   - Or do they wrap it in their own endpoints?

2. **How does sessionToken become Stark API token?**
   - Is there a callback endpoint?
   - Does Stark's backend exchange it?
   - Or do we use the sessionToken directly?

3. **Can we call Okta API directly?**
   - Or does Stark require going through their frontend?

4. **Where are the headers set?**
   - `x-user-token`: From Okta token? From Stark API?
   - `x-user-id`: From Okta user profile? From Stark user API?
   - `x-rogers-revision`: Static? From app config? From API?

## Implementation Status

✅ **Completed:**
1. Okta Authentication API (`/api/v1/authn`) - implemented
2. OAuth2 Authorization Code flow with PKCE - implemented
3. Token exchange (`/oauth2/v1/token`) - implemented
4. User info endpoint (`/enduser/api/v1/home`) - implemented
5. User ID extraction (`userId` field) - implemented

❌ **Still Needed:**
1. **Stark token exchange** - This is the critical missing piece:
   - After getting Okta access token, how does Stark exchange it for their API token?
   - Look for requests to Stark endpoints that use the Okta token
   - Find where `x-user-token` is set in Stark API requests
2. **MFA handling** - If MFA is required
3. **Rogers revision** - Where does `x-rogers-revision` come from?

## Next Steps

1. **Scrape Stark token exchange**:
   - After logging into Okta and getting the access token
   - Look for the first request to Stark TMS API (`apps-staging.transfix.io` or `staging.transfix.io`)
   - Find where `x-user-token` header is set
   - This might be:
     - A callback endpoint that exchanges Okta token for Stark token
     - The Okta token itself (if Stark accepts it directly)
     - A session cookie that's set after Okta auth
     - A separate token exchange endpoint

2. **Find rogers revision**:
   - Look for where `x-rogers-revision` appears in requests
   - It might be:
     - In the initial page load HTML
     - In a config/version endpoint
     - In the user info response
     - Static value that changes with app version
   - This is the part you'll need to scrape

4. **Get user info**:
   - Call `/api/v1/users/me` with the token
   - Or call Stark's user API

5. **Implement in code**:
   - Use standard Okta API calls
   - Handle MFA
   - Exchange for Stark token
   - Extract headers

## Example Implementation Pattern

Looking at GlobalTranz (which uses OAuth2), here's the pattern:

```go
// 1. Authenticate with credentials
sessionToken, err := oktaAuthn(ctx, username, password)

// 2. Handle MFA if needed
if mfaRequired {
    sessionToken, err = oktaVerifyMFA(ctx, stateToken, factorId, passCode)
}

// 3. Exchange sessionToken for access token
accessToken, err := oktaExchangeToken(ctx, sessionToken)

// 4. Get user info
userInfo, err := oktaGetUserInfo(ctx, accessToken)

// 5. Get Stark-specific headers
starkToken, err := starkGetAPIToken(ctx, accessToken) // This part needs scraping
userID := userInfo.ID
rogersRevision := getRogersRevision(ctx) // This part needs scraping
```

## If Standard Okta API Doesn't Work

If Stark has wrapped Okta in custom endpoints, you may need to:
- Use their custom auth endpoints (but still try to use Okta API under the hood)
- Or scrape their specific flow (less ideal, but sometimes necessary)

**But always try standard Okta API first!**

