package stark

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

// GetLoad retrieves a single shipment by ID using GraphQL
func (s *Stark) GetLoad(
	ctx context.Context,
	externalTMSID string,
) (load models.Load, attrs models.LoadAttributes, err error) {
	spanAttrs := otel.IntegrationAttrs(s.tms)

	ctx, metaSpan := otel.StartSpan(ctx, "GetLoadStark", spanAttrs)
	defer func() { metaSpan.End(err) }()

	attrs = s.GetDefaultLoadAttributes()

	query := `query ShipmentDetailsStatus($id: ID!) {
		shipment(id: $id) {
			...ShipmentBasicInfo
			id
			tmsShipmentPublicUuid
			links {
				starkCopyFmsShipmentUrl
				__typename
			}
			state
			specialInstruction
			hasClaims
			hasOsd
			shipperApplication {
				id
				carrierInstructions
				__typename
			}
			trackingOverview {
				trackingState
				calculatedTrackingMethod
				__typename
			}
			rateConfirmationAgreement {
				id
				carrierSignature
				carrierSignedAt
				__typename
			}
			statusDetails {
				detailedState
				pickupStatus
				__typename
			}
			__typename
		}
	}

	fragment ShipmentBasicInfo on Shipment {
		id
		shipperShipmentReference
		shipperMiles
		totalWeight
		totalPalletCount
		calculatedMiles
		bolReferences
		poReferences
		loadingType
		isBackhaul
		team
		firstPickup {
			...ShipmentStop
			__typename
		}
		lastDelivery {
			...ShipmentStop
			__typename
		}
		stops {
			...ShipmentStop
			__typename
		}
		equipment {
			id
			name
			parentKey
			parentName
			__typename
		}
		characteristics {
			hot
			mustGo
			haltClaimsBilling
			haltClaimsInvoicing
			__typename
		}
		shipperTrackingSegment {
			id
			description
			level
			__typename
		}
		__typename
	}

	fragment ShipmentStop on ShipmentStop {
		id
		city
		stateCode
		specialInstruction
		commodity
		dropTrailer
		note
		lateReason
		project44Eta
		manualEta
		arrivedAt
		latitude
		longitude
		leftAt
		stopSequence
		stopType
		zipcode
		appointment {
			id
			timezone
			startAt
			endAt
			appointmentType
			__typename
		}
		__typename
	}`

	variables := map[string]interface{}{
		"id": externalTMSID,
	}

	var response ShipmentDetailsStatusGraphQLResponse
	err = s.graphQLQuery(ctx, "ShipmentDetailsStatus", variables, query, &response, s3backup.TypeLoads)
	if err != nil {
		return load, attrs, fmt.Errorf("failed to get shipment from GraphQL: %w", err)
	}

	if response.Data.Shipment.ID == "" {
		return load, attrs, errtypes.EntityNotFoundError(s.tms, externalTMSID, "shipment_id")
	}

	load, err = s.shipmentDetailsToLoad(ctx, response.Data.Shipment)
	if err != nil {
		return load, attrs, fmt.Errorf("failed to map shipment to load: %w", err)
	}

	return load, attrs, nil
}

// GetLoadIDs retrieves shipment IDs based on query parameters
func (s *Stark) GetLoadIDs(
	ctx context.Context,
	queryParams models.SearchLoadsQuery,
) ([]string, error) {
	ctx, metaSpan := otel.StartSpan(ctx, "GetLoadIDsStark", otel.IntegrationAttrs(s.tms))
	defer func() { metaSpan.End(nil) }()

	// Build filters based on query parameters
	filters := s.buildFilters(queryParams)

	filtersJSON, err := json.Marshal(filters)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal filters: %w", err)
	}

	allIDs := make([]string, 0)
	pageCount := 0

	for {
		apiQueryParams := url.Values{}
		apiQueryParams.Set("filters", string(filtersJSON))
		apiQueryParams.Set("sort_field", "pickups")
		apiQueryParams.Set("sort_order", "asc")
		apiQueryParams.Set("page_count", strconv.Itoa(pageCount))

		var response ShipmentResponse
		err = s.get(ctx, "/v8/shipments", apiQueryParams, &response, s3backup.TypeLoads)
		if err != nil {
			if pageCount == 0 {
				return nil, fmt.Errorf("failed to get shipments: %w", err)
			}
			// If we've already collected some results, return what we have
			log.WarnNoSentry(ctx, "failed to get next page of shipments, returning partial results",
				zap.Error(err),
				zap.Int("pageCount", pageCount),
				zap.Int("collectedSoFar", len(allIDs)))
			break
		}

		if len(response.Shipments) == 0 {
			break
		}

		for _, shipment := range response.Shipments {
			allIDs = append(allIDs, strconv.FormatInt(shipment.ID, 10))
		}

		pageCount++
	}

	return allIDs, nil
}

func (s *Stark) buildFilters(query models.SearchLoadsQuery) map[string]interface{} {
	filters := make(map[string]interface{})

	filters["pod_ids"] = []interface{}{}
	filters["includes"] = []string{"open_issues"}
	filters["users_id"] = nil
	filters["users_type"] = nil
	filters["equipment"] = map[string]interface{}{}
	filters["pickup_city"] = ""
	filters["pickup_state_code"] = ""
	filters["pickup_regions"] = ""
	filters["delivery_city"] = ""
	filters["delivery_state_code"] = ""
	filters["delivery_regions"] = ""
	filters["pickup_date"] = ""
	filters["pickup_end_date"] = ""
	filters["pricing_type"] = ""
	filters["delivery_date"] = ""
	filters["delivery_end_date"] = ""
	filters["query"] = ""
	filters["pickup_miles"] = ""
	filters["delivery_miles"] = ""
	filters["has_hot"] = false
	filters["has_rolled"] = false
	filters["no_appointment_time"] = false
	filters["unconfirmed_appointment"] = false
	filters["has_claims"] = false
	filters["multi_day"] = false
	filters["state_options"] = ""
	filters["after_hours"] = false
	filters["drop_trailer"] = false
	filters["broker_pod_key"] = nil
	filters["needs_tracking"] = false
	filters["needs_compliance_review"] = false
	filters["in_compliance_review"] = false
	filters["shipper_rate_type"] = []interface{}{}
	filters["must_go"] = false

	if query.FromFreightTrackingID != "" {
		filters["query"] = query.FromFreightTrackingID
	}

	if query.Status != "" {
		switch strings.ToLower(query.Status) {
		case "tendered":
			filters["status"] = []string{"tendered"}
		case "waterfall_offered":
			filters["status"] = []string{"waterfall_offered"}
		case "alerted":
			filters["status"] = []string{"alerted"}
		case "reserved":
			filters["status"] = []string{"reserved"}
		default:
			filters["status"] = []string{query.Status}
		}
	}

	if query.FromDate.Valid {
		filters["pickup_date"] = query.FromDate.Time.Format("2006-01-02")
	}
	if query.ToDate.Valid {
		filters["pickup_end_date"] = query.ToDate.Time.Format("2006-01-02")
	}

	if query.Pickup.City != "" {
		filters["pickup_city"] = query.Pickup.City
	}
	if query.Pickup.State != "" {
		filters["pickup_state_code"] = query.Pickup.State
	}
	if query.Dropoff.City != "" {
		filters["delivery_city"] = query.Dropoff.City
	}
	if query.Dropoff.State != "" {
		filters["delivery_state_code"] = query.Dropoff.State
	}

	return filters
}

func (s *Stark) shipmentToLoad(ctx context.Context, shipment Shipment) (models.Load, error) {
	load := models.Load{
		TMSID:             s.tms.ID,
		ServiceID:         s.tms.ServiceID,
		ExternalTMSID:     strconv.FormatInt(shipment.ID, 10),
		FreightTrackingID: strconv.FormatInt(shipment.ID, 10),
		LoadCoreInfo: models.LoadCoreInfo{
			Status: shipment.Status.Key,
			PONums: "",
		},
	}

	if len(shipment.Pickups) > 0 {
		pickup := shipment.Pickups[0]
		load.Pickup = s.stopToPickup(pickup)
		if load.PONums == "" {
			load.PONums = pickup.PurchaseOrder
		}
	}

	if len(shipment.Deliveries) > 0 {
		delivery := shipment.Deliveries[0]
		load.Consignee = s.stopToConsignee(delivery)
	}

	load.Specifications.TotalWeight = models.ValueUnit{
		Val:  float32(shipment.Info.Weight),
		Unit: models.LbsUnit,
	}
	load.Specifications.TotalOutPalletCount = shipment.Info.PalletCount
	load.Specifications.Commodities = shipment.Info.Commodity

	if shipment.Rates.ShipperRate != "" {
		if rate, err := parseFloat(shipment.Rates.ShipperRate); err == nil {
			load.RateData.CustomerLineHaulRate = float32(rate)
			load.RateData.CustomerLineHaulCharge = models.ValueUnit{
				Val:  float32(rate),
				Unit: "USD",
			}
		}
	}
	if shipment.Rates.TotalMiles != "" {
		if miles, err := parseFloat(shipment.Rates.TotalMiles); err == nil {
			load.Specifications.TotalDistance = models.ValueUnit{
				Val:  float32(miles),
				Unit: models.MilesUnit,
			}
		}
	}

	// Map equipment
	if shipment.Equipment.Name != "" {
		load.Specifications.TransportType = shipment.Equipment.Name
		transportType, err := s.MapTransportTypeEnum(shipment.Equipment.Name)
		if err == nil {
			load.Specifications.TransportTypeEnum = &transportType
		}
	}

	if shipment.Users.ShipperName != "" {
		load.Customer = models.Customer{
			CompanyCoreInfo: models.CompanyCoreInfo{
				Name: shipment.Users.ShipperName,
			},
		}
	}

	return load, nil
}

func (s *Stark) stopToPickup(stop Stop) models.Pickup {
	pickup := models.Pickup{
		CompanyCoreInfo: models.CompanyCoreInfo{
			Name:    stop.FacilityName,
			City:    stop.City,
			State:   stop.StateCode,
			Zipcode: stop.Zipcode,
		},
		RefNumber:     stop.PurchaseOrder,
		ReadyTime:     parseTimeNullable(stop.StartAt),
		ApptStartTime: parseTimeNullable(stop.StartAt),
		ApptType:      mapAppointmentType(stop.AppointmentType),
		Timezone:      stop.Timezone,
	}

	if stop.EndAt != nil {
		pickup.ApptEndTime = parseTimeNullable(*stop.EndAt)
	}

	if pickup.Timezone == "" {
		pickup.Timezone = "America/New_York"
	}

	return pickup
}

func (s *Stark) stopToConsignee(stop Stop) models.Consignee {
	consignee := models.Consignee{
		CompanyCoreInfo: models.CompanyCoreInfo{
			Name:    stop.FacilityName,
			City:    stop.City,
			State:   stop.StateCode,
			Zipcode: stop.Zipcode,
		},
		RefNumber:     stop.PurchaseOrder,
		MustDeliver:   parseTimeNullable(stop.StartAt),
		ApptStartTime: parseTimeNullable(stop.StartAt),
		ApptType:      mapAppointmentType(stop.AppointmentType),
		Timezone:      stop.Timezone,
	}

	if stop.EndAt != nil {
		consignee.ApptEndTime = parseTimeNullable(*stop.EndAt)
	}

	if consignee.Timezone == "" {
		consignee.Timezone = "America/New_York"
	}

	return consignee
}

func parseTime(timeStr string) time.Time {
	if timeStr == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC3339, timeStr)
	if err != nil {
		return time.Time{}
	}
	return t
}

func parseTimeNullable(timeStr string) models.NullTime {
	if timeStr == "" {
		return models.NullTime{Valid: false}
	}
	t, err := time.Parse(time.RFC3339, timeStr)
	if err != nil {
		return models.NullTime{Valid: false}
	}
	return models.NullTime{Time: t, Valid: true}
}

func parseFloat(s string) (float64, error) {
	return strconv.ParseFloat(s, 64)
}

func mapAppointmentType(apptType string) string {
	switch strings.ToLower(apptType) {
	case "appt", "appointment":
		return "appointment"
	case "window":
		return "window"
	default:
		return ""
	}
}

func (s *Stark) shipmentDetailsToLoad(ctx context.Context, shipment ShipmentDetailsGraphQL) (models.Load, error) {
	load := models.Load{
		TMSID:             s.tms.ID,
		ServiceID:         s.tms.ServiceID,
		ExternalTMSID:     shipment.ID,
		FreightTrackingID: shipment.ID,
		LoadCoreInfo: models.LoadCoreInfo{
			Status: shipment.StatusDetails.DetailedState,
		},
	}

	if len(shipment.BOLReferences) > 0 {
		for _, bol := range shipment.BOLReferences {
			load.LoadCoreInfo.AdditionalReferences = append(load.LoadCoreInfo.AdditionalReferences, models.AdditionalReference{
				Qualifier: "BOL",
				Number:    bol,
			})
		}
	}
	if len(shipment.POReferences) > 0 {
		load.LoadCoreInfo.PONums = strings.Join(shipment.POReferences, ",")
	}

	if shipment.FirstPickup != nil {
		load.Pickup = s.stopGraphQLToPickup(*shipment.FirstPickup)
	}

	if shipment.LastDelivery != nil {
		load.Consignee = s.stopGraphQLToConsignee(*shipment.LastDelivery)
	}

	load.Specifications.TotalWeight = models.ValueUnit{
		Val:  float32(shipment.TotalWeight),
		Unit: models.LbsUnit,
	}
	load.Specifications.TotalOutPalletCount = shipment.TotalPalletCount
	if shipment.CalculatedMiles > 0 {
		load.Specifications.TotalDistance = models.ValueUnit{
			Val:  float32(shipment.CalculatedMiles),
			Unit: models.MilesUnit,
		}
	} else if shipment.ShipperMiles > 0 {
		load.Specifications.TotalDistance = models.ValueUnit{
			Val:  float32(shipment.ShipperMiles),
			Unit: models.MilesUnit,
		}
	}

	// Map equipment
	if shipment.Equipment.Name != "" {
		load.Specifications.TransportType = shipment.Equipment.Name
		transportType, err := s.MapTransportTypeEnum(shipment.Equipment.Name)
		if err == nil {
			load.Specifications.TransportTypeEnum = &transportType
		}
	}

	if shipment.Characteristics.Hot {
		load.LoadCoreInfo.AdditionalReferences = append(load.LoadCoreInfo.AdditionalReferences, models.AdditionalReference{
			Qualifier: "HOT",
			Number:    "",
		})
	}
	if shipment.Characteristics.MustGo {
		load.LoadCoreInfo.AdditionalReferences = append(load.LoadCoreInfo.AdditionalReferences, models.AdditionalReference{
			Qualifier: "MUST_GO",
			Number:    "",
		})
	}

	if shipment.ShipperApplication.ID != "" {
		load.Customer = models.Customer{
			CompanyCoreInfo: models.CompanyCoreInfo{
				ExternalTMSID: shipment.ShipperApplication.ID,
			},
		}
		if shipment.ShipperApplication.CarrierInstructions != "" {
			load.LoadCoreInfo.Notes = append(load.LoadCoreInfo.Notes, models.Note{
				Text: shipment.ShipperApplication.CarrierInstructions,
			})
		}
	}

	if shipment.SpecialInstruction != nil && *shipment.SpecialInstruction != "" {
		load.LoadCoreInfo.Notes = append(load.LoadCoreInfo.Notes, models.Note{
			Text: *shipment.SpecialInstruction,
		})
	}

	// Populate stops array for multi-stop support
	if len(shipment.Stops) > 0 {
		load.Stops = make([]models.Stop, len(shipment.Stops))
		for i, stop := range shipment.Stops {
			load.Stops[i] = s.stopGraphQLToStop(stop)
		}
		if len(shipment.Stops) > 2 {
			load.LoadCoreInfo.MoreThanTwoStops = true
		}
	}

	return load, nil
}

func (s *Stark) stopGraphQLToPickup(stop ShipmentStopGraphQL) models.Pickup {
	pickup := models.Pickup{
		CompanyCoreInfo: models.CompanyCoreInfo{
			City:    stop.City,
			State:   stop.StateCode,
			Zipcode: stop.Zipcode,
		},
		ExternalTMSStopID: stop.ID,
		RefNumber:         "",
		Timezone:          "America/New_York",
	}

	if stop.Note != nil {
		pickup.RefNumber = *stop.Note
	}

	// Map appointment
	if stop.Appointment.ID != "" {
		pickup.Timezone = stop.Appointment.Timezone
		if stop.Appointment.Timezone == "" {
			pickup.Timezone = "America/New_York"
		}
		pickup.ApptStartTime = parseTimeNullable(stop.Appointment.StartAt)
		if stop.Appointment.EndAt != nil {
			pickup.ApptEndTime = parseTimeNullable(*stop.Appointment.EndAt)
		}
		pickup.ApptType = mapAppointmentType(stop.Appointment.AppointmentType)
	}

	if stop.SpecialInstruction != nil && *stop.SpecialInstruction != "" {
		pickup.ApptNote = *stop.SpecialInstruction
	}

	if stop.Commodity != "" {
		if pickup.ApptNote != "" {
			pickup.ApptNote += "\nCommodity: " + stop.Commodity
		} else {
			pickup.ApptNote = "Commodity: " + stop.Commodity
		}
	}

	return pickup
}

func (s *Stark) stopGraphQLToConsignee(stop ShipmentStopGraphQL) models.Consignee {
	consignee := models.Consignee{
		CompanyCoreInfo: models.CompanyCoreInfo{
			City:    stop.City,
			State:   stop.StateCode,
			Zipcode: stop.Zipcode,
		},
		ExternalTMSStopID: stop.ID,
		RefNumber:         "",
		Timezone:          "America/New_York",
	}

	if stop.Note != nil {
		consignee.RefNumber = *stop.Note
	}

	// Map appointment
	if stop.Appointment.ID != "" {
		consignee.Timezone = stop.Appointment.Timezone
		if stop.Appointment.Timezone == "" {
			consignee.Timezone = "America/New_York"
		}
		consignee.ApptStartTime = parseTimeNullable(stop.Appointment.StartAt)
		if stop.Appointment.EndAt != nil {
			consignee.ApptEndTime = parseTimeNullable(*stop.Appointment.EndAt)
		}
		consignee.ApptType = mapAppointmentType(stop.Appointment.AppointmentType)
	}

	notes := []string{}
	if stop.SpecialInstruction != nil && *stop.SpecialInstruction != "" {
		notes = append(notes, *stop.SpecialInstruction)
	}
	if stop.LateReason != nil && *stop.LateReason != "" {
		notes = append(notes, "Late Reason: "+*stop.LateReason)
	}
	if len(notes) > 0 {
		consignee.ApptNote = strings.Join(notes, "\n")
	}

	return consignee
}

func (s *Stark) stopGraphQLToStop(stop ShipmentStopGraphQL) models.Stop {
	stopModel := models.Stop{
		StopType:          stop.StopType,
		StopNumber:        stop.StopSequence - 1, // Convert to 0-based indexing
		ExternalTMSStopID: stop.ID,
		Address: models.Address{
			City:  stop.City,
			State: stop.StateCode,
			Zip:   stop.Zipcode,
		},
		Timezone: "America/New_York",
	}

	if stop.Note != nil {
		stopModel.RefNumber = *stop.Note
	}

	if stop.SpecialInstruction != nil {
		stopModel.ApptNote = *stop.SpecialInstruction
	}

	if stop.Commodity != "" {
		if stopModel.ApptNote != "" {
			stopModel.ApptNote += "\nCommodity: " + stop.Commodity
		} else {
			stopModel.ApptNote = "Commodity: " + stop.Commodity
		}
	}

	if stop.LateReason != nil && *stop.LateReason != "" {
		if stopModel.ApptNote != "" {
			stopModel.ApptNote += "\nLate Reason: " + *stop.LateReason
		} else {
			stopModel.ApptNote = "Late Reason: " + *stop.LateReason
		}
	}

	// Map appointment
	if stop.Appointment.ID != "" {
		stopModel.ApptNum = stop.Appointment.ID
		stopModel.Timezone = stop.Appointment.Timezone
		if stopModel.Timezone == "" {
			stopModel.Timezone = "America/New_York"
		}
		stopModel.ApptStartTime = parseTimeNullable(stop.Appointment.StartAt)
		if stop.Appointment.EndAt != nil {
			stopModel.ApptEndTime = parseTimeNullable(*stop.Appointment.EndAt)
		}
		stopModel.ApptType = mapAppointmentType(stop.Appointment.AppointmentType)
		stopModel.ApptRequired = stop.Appointment.AppointmentType != ""
	}

	// Map actual times
	if stop.ArrivedAt != nil {
		stopModel.ActualStartTime = parseTimeNullable(*stop.ArrivedAt)
	}
	if stop.LeftAt != nil {
		stopModel.ActualEndTime = parseTimeNullable(*stop.LeftAt)
	}

	// Map ETA times
	if stop.ManualEta != "" {
		if stop.StopType == "pickup" {
			stopModel.ReadyTime = parseTimeNullable(stop.ManualEta)
		} else {
			stopModel.MustDeliver = parseTimeNullable(stop.ManualEta)
		}
	}

	return stopModel
}
