package stark

import (
	"encoding/json"
)

// ShipmentResponse represents the response from GET /shipments
type ShipmentResponse struct {
	Shipments []Shipment `json:"shipments"`
}

// Shipment represents a single shipment from Stark TMS
type Shipment struct {
	ID             int64  `json:"id"`
	EntityID       int    `json:"entity_id"`
	EntityName     string `json:"entity_name"`
	EntityInitials string `json:"entity_initials"`
	EntityKey      string `json:"entity_key"`
	CreatedAt      string `json:"created_at"`

	HasHazmat   bool   `json:"has_hazmat"`
	PricingType string `json:"pricing_type"`

	Pickups    []Stop `json:"pickups"`
	Deliveries []Stop `json:"deliveries"`

	Info            ShipmentInfo            `json:"info"`
	Rates           ShipmentRates           `json:"rates"`
	Flags           []string                `json:"flags"`
	Users           ShipmentUsers           `json:"users"`
	ShipperMetadata ShipmentShipperMetadata `json:"shipper_metadata"`
	Equipment       ShipmentEquipment       `json:"equipment"`

	CurrentAuctionShipment *AuctionShipment `json:"current_auction_shipment"`
	ShipperTrackingSegment *TrackingSegment `json:"shipper_tracking_segment"`

	IsDriverTracking bool           `json:"is_driver_tracking"`
	TrackingStatus   TrackingStatus `json:"tracking_status"`

	LatestDriverLocationAt  *string `json:"latest_driver_location_at"`
	DriverLocationTimezone  *string `json:"driver_location_timezone"`
	DriverLocationLatitude  *string `json:"driver_location_latitude"`
	DriverLocationLongitude *string `json:"driver_location_longitude"`

	Status                  Status `json:"status"`
	OpenShipmentIssuesCount int    `json:"open_shipment_issues_count"`
}

// Stop represents a pickup or delivery stop
type Stop struct {
	AppointmentStatus            string  `json:"appointment_status"`
	AppointmentType              string  `json:"appointment_type"`
	City                         string  `json:"city"`
	EndAt                        *string `json:"end_at"`
	EndAtDate                    *string `json:"end_at_date"`
	EndAtTime                    *string `json:"end_at_time"`
	FacilityName                 string  `json:"facility_name"`
	StartAt                      string  `json:"start_at"`
	StartAtDate                  string  `json:"start_at_date"`
	StartAtTime                  string  `json:"start_at_time"`
	ArriveByTime                 string  `json:"arrive_by_time"`
	StateCode                    string  `json:"state_code"`
	StopSequence                 int     `json:"stop_sequence"`
	StopType                     string  `json:"stop_type"`
	IsDropTrailer                bool    `json:"is_drop_trailer"`
	Timezone                     string  `json:"timezone"`
	Zipcode                      string  `json:"zipcode"`
	Latitude                     string  `json:"latitude"`
	Longitude                    string  `json:"longitude"`
	ManualETA                    *string `json:"manual_eta"`
	Project44ETA                 *string `json:"project44_eta"`
	Miles                        string  `json:"miles"`
	AppointmentTimeUpdatedByName string  `json:"appointment_time_updated_by_name"`
	AppointmentTimeUpdatedAt     string  `json:"appointment_time_updated_at"`
	ConfirmedByName              string  `json:"confirmed_by_name"`
	SchedulingOption             string  `json:"scheduling_option"`
	PurchaseOrder                string  `json:"purchase_order"`
	BillOfLading                 string  `json:"bill_of_lading"`
	Weight                       int     `json:"weight"`
	PalletCount                  int     `json:"pallet_count"`
	Commodity                    string  `json:"commodity"`
	ArrivedAt                    *string `json:"arrived_at"`
	DepartedAt                   *string `json:"departed_at"`
}

// ShipmentInfo contains additional shipment information
type ShipmentInfo struct {
	Weight                           int     `json:"weight"`
	PalletCount                      int     `json:"pallet_count"`
	Commodity                        string  `json:"commodity"`
	DropTrailer                      bool    `json:"drop_trailer"`
	Offerable                        bool    `json:"offerable"`
	TrackingRequired                 bool    `json:"tracking_required"`
	HasClaims                        *bool   `json:"has_claims"`
	RolloverCount                    int     `json:"rollover_count"`
	Hot                              bool    `json:"hot"`
	VIN                              *string `json:"vin"`
	TruckNumber                      *string `json:"truck_number"`
	TrailerNumber                    *string `json:"trailer_number"`
	BouncedCount                     int     `json:"bounced_count"`
	LastActionClass                  string  `json:"last_action_class"`
	LastActionUpdatedByID            int     `json:"last_action_updated_by_id"`
	LastActionUpdatedByName          string  `json:"last_action_updated_by_name"`
	LastActionAt                     string  `json:"last_action_at"`
	LastActionSnippet                string  `json:"last_action_snippet"`
	LastActionType                   string  `json:"last_action_type"`
	LastNoteSnippet                  string  `json:"last_note_snippet"`
	LastNoteUpdatedByID              int     `json:"last_note_updated_by_id"`
	LastNoteUpdatedByName            string  `json:"last_note_updated_by_name"`
	LastNoteAt                       string  `json:"last_note_at"`
	AccessorialShipperChargesPending bool    `json:"accessorial_shipper_charges_pending"`
	HasUnresolvedCallback            bool    `json:"has_unresolved_callback"`
	HasHazmat                        bool    `json:"has_hazmat"`
	DeclaredValue                    string  `json:"declared_value"`
}

// ShipmentRates contains rate information
type ShipmentRates struct {
	TotalMiles               string            `json:"total_miles"`
	ShipperRate              string            `json:"shipper_rate"`
	ShipperRateConfirmedByID *int              `json:"shipper_rate_confirmed_by_id"`
	RateType                 string            `json:"rate_type"`
	CarrierTotalRate         string            `json:"carrier_total_rate"`
	CarrierRate              *string           `json:"carrier_rate"`
	TCMCarrierPrices         *TCMCarrierPrices `json:"tcm_carrier_prices"`
	TransfixTargetRate       string            `json:"transfix_target_rate"`
	TrueMarginRate           *string           `json:"true_margin_rate"`
	TargetMarginRate         string            `json:"target_margin_rate"`
}

// TCMCarrierPrices contains TCM pricing information
type TCMCarrierPrices struct {
	ListPrice string    `json:"list_price"`
	CMTarget  *CMTarget `json:"cm_target"`
}

// CMTarget contains CM target pricing
type CMTarget struct {
	Amount         string `json:"amount"`
	ShipmentRateID int64  `json:"shipment_rate_id"`
}

// ShipmentUsers contains user information
type ShipmentUsers struct {
	NewShipper         bool   `json:"new_shipper"`
	ShipperName        string `json:"shipper_name"`
	ShipperID          int    `json:"shipper_id"`
	OpsRepName         string `json:"ops_rep_name"`
	OpsRepID           int    `json:"ops_rep_id"`
	OpsRepParentUserID int    `json:"ops_rep_parent_user_id"`
}

// ShipmentShipperMetadata contains shipper metadata
type ShipmentShipperMetadata struct {
	AutoAcceptEnabled  bool `json:"auto_accept_enabled"`
	CanRespondToTender bool `json:"can_respond_to_tender"`
}

// ShipmentEquipment contains equipment information
type ShipmentEquipment struct {
	Icon       string                 `json:"icon"`
	Name       string                 `json:"name"`
	ParentID   int                    `json:"parent_id"`
	Attributes map[string]interface{} `json:"attributes"`
}

// AuctionShipment contains auction information
type AuctionShipment struct {
	ID                    int64  `json:"id"`
	TargetRate            string `json:"target_rate"`
	Bids                  []Bid  `json:"bids"`
	BidCount              int    `json:"bid_count"`
	AuctionShipmentStatus string `json:"auction_shipment_status"`
}

// Bid represents a bid in an auction
type Bid struct {
	ID                         int64   `json:"id"`
	ShipmentID                 int64   `json:"shipment_id"`
	CarrierApplicationID       int     `json:"carrier_application_id"`
	CarrierApplicationName     string  `json:"carrier_application_name"`
	CreatedAt                  string  `json:"created_at"`
	UpdatedAt                  string  `json:"updated_at"`
	CreatedByID                int     `json:"created_by_id"`
	CreatedByParentUserID      int     `json:"created_by_parent_user_id"`
	CreatedByName              string  `json:"created_by_name"`
	CreatedByPhone             string  `json:"created_by_phone"`
	CarrierContactUserID       *int    `json:"carrier_contact_user_id"`
	CarrierContactParentUserID *int    `json:"carrier_contact_parent_user_id"`
	CarrierContactName         *string `json:"carrier_contact_name"`
	CarrierContactPhone        string  `json:"carrier_contact_phone"`
	CarrierContactPhoneExt     *string `json:"carrier_contact_phone_ext"`
	Amount                     string  `json:"amount"`
	AcceptedAt                 *string `json:"accepted_at"`
	DeclinedAt                 *string `json:"declined_at"`
	Direction                  string  `json:"direction"`
	Source                     string  `json:"source"`
	Driver                     Driver  `json:"driver"`
}

// Driver contains driver information
type Driver struct {
	ID        *int    `json:"id"`
	Name      *string `json:"name"`
	CellPhone *string `json:"cell_phone"`
}

// TrackingSegment contains tracking segment information
type TrackingSegment struct {
	ID                       int    `json:"id"`
	MinReqTrackingPercentage int    `json:"min_req_tracking_percentage"`
	Description              string `json:"description"`
	Level                    int    `json:"level"`
}

// TrackingStatus contains tracking status information
type TrackingStatus struct {
	TrackingState  string `json:"tracking_state"`
	TrackingMethod string `json:"tracking_method"`
}

// Status contains shipment status information
type Status struct {
	Name               string  `json:"name"`
	State              string  `json:"state"`
	Key                string  `json:"key"`
	PickupStatus       string  `json:"pickup_status"`
	CurrentStopIndex   int     `json:"current_stop_index"`
	CalculatedMiles    float64 `json:"calculated_miles"`
	TimeLateToNextStop *string `json:"time_late_to_next_stop"`
	ManualETA          *string `json:"manual_eta"`
	Project44ETA       *string `json:"project44_eta"`
}

// GraphQL Request/Response models

// GraphQLRequest represents a GraphQL query request
type GraphQLRequest struct {
	OperationName string                 `json:"operationName"`
	Variables     map[string]interface{} `json:"variables"`
	Query         string                 `json:"query"`
}

// GraphQLResponse represents a generic GraphQL response
type GraphQLResponse struct {
	Data   json.RawMessage `json:"data"`
	Errors []GraphQLError  `json:"errors,omitempty"`
}

// GraphQLError represents a GraphQL error
type GraphQLError struct {
	Message string `json:"message"`
}

// ShippersGraphQLResponse represents the response from the Shippers GraphQL query
type ShippersGraphQLResponse struct {
	Data struct {
		SearchShipperApplications struct {
			Nodes      []ShipperApplication `json:"nodes"`
			PageInfo   PageInfo             `json:"pageInfo"`
			TotalCount int                  `json:"totalCount"`
		} `json:"searchShipperApplications"`
	} `json:"data"`
}

// ShipperApplication represents a shipper from the GraphQL API
type ShipperApplication struct {
	ID            string `json:"id"`
	Name          string `json:"name"`
	State         string `json:"state"`
	ShipmentEmail string `json:"shipmentEmail"`
	ScacValue     string `json:"scacValue"`
	EffectiveScac string `json:"effectiveScac"`
}

// PageInfo represents pagination information from GraphQL
type PageInfo struct {
	EndCursor       string `json:"endCursor"`
	HasNextPage     bool   `json:"hasNextPage"`
	HasPreviousPage bool   `json:"hasPreviousPage"`
	StartCursor     string `json:"startCursor"`
}

// LoadUpdateGraphQLResponse represents the response from the LoadUpdate GraphQL mutation
type LoadUpdateGraphQLResponse struct {
	Data struct {
		UpdateShipmentStopManualEta struct {
			Errors       []GraphQLError      `json:"errors"`
			ShipmentStop ShipmentStopGraphQL `json:"shipmentStop"`
		} `json:"updateShipmentStopManualEta"`
	} `json:"data"`
}

// ShipmentStopGraphQL represents a shipment stop from GraphQL
type ShipmentStopGraphQL struct {
	ID                 string                  `json:"id"`
	City               string                  `json:"city"`
	StateCode          string                  `json:"stateCode"`
	SpecialInstruction *string                 `json:"specialInstruction"`
	Commodity          string                  `json:"commodity"`
	DropTrailer        bool                    `json:"dropTrailer"`
	Note               *string                 `json:"note"`
	LateReason         *string                 `json:"lateReason"`
	Project44Eta       *string                 `json:"project44Eta"`
	ManualEta          string                  `json:"manualEta"`
	ArrivedAt          *string                 `json:"arrivedAt"`
	Latitude           float64                 `json:"latitude"`
	Longitude          float64                 `json:"longitude"`
	LeftAt             *string                 `json:"leftAt"`
	StopSequence       int                     `json:"stopSequence"`
	StopType           string                  `json:"stopType"`
	Zipcode            string                  `json:"zipcode"`
	Appointment        ShipmentStopAppointment `json:"appointment"`
}

// ShipmentStopAppointment represents appointment information for a shipment stop
type ShipmentStopAppointment struct {
	ID              string  `json:"id"`
	Timezone        string  `json:"timezone"`
	StartAt         string  `json:"startAt"`
	EndAt           *string `json:"endAt"`
	AppointmentType string  `json:"appointmentType"`
}

// FacilitiesGraphQLResponse represents the response from the Facilities GraphQL query
type FacilitiesGraphQLResponse struct {
	Data struct {
		PaginatedFacilities struct {
			Nodes      []FacilityGraphQL `json:"nodes"`
			PageInfo   PageInfo          `json:"pageInfo"`
			TotalCount int               `json:"totalCount"`
		} `json:"paginatedFacilities"`
	} `json:"data"`
}

// FacilityGraphQL represents a facility from the GraphQL API
type FacilityGraphQL struct {
	ID                string                   `json:"id"`
	Name              string                   `json:"name"`
	UUID              string                   `json:"uuid"`
	ReviewStatus      string                   `json:"reviewStatus"`
	ReviewStatusSetAt *string                  `json:"reviewStatusSetAt"`
	ReviewStatusSetBy *FacilityReviewer        `json:"reviewStatusSetBy"`
	Contacts          []FacilityContact        `json:"contacts"`
	Location          FacilityLocation         `json:"location"`
	VendorFacilities  []VendorFacility         `json:"vendorFacilities"`
	ShipperFacilities []ShipperFacilityGraphQL `json:"shipperFacilities"`
}

// FacilityReviewer represents who set the review status
type FacilityReviewer struct {
	ID       string `json:"id"`
	FullName string `json:"fullName"`
}

// FacilityContact represents contact information for a facility
type FacilityContact struct {
	Email          *string `json:"email"`
	ID             string  `json:"id"`
	Name           string  `json:"name"`
	Phone          string  `json:"phone"`
	PhoneExtension *string `json:"phoneExtension"`
	Website        *string `json:"website"`
}

// FacilityLocation represents the location/address of a facility
type FacilityLocation struct {
	ID           string  `json:"id"`
	Address      string  `json:"address"`
	AddressLine2 *string `json:"addressLine2"`
	City         string  `json:"city"`
	StateCode    string  `json:"stateCode"`
	Zipcode      string  `json:"zipcode"`
}

// VendorFacility represents vendor facility relationship
type VendorFacility struct {
	ID     string     `json:"id"`
	Vendor VendorInfo `json:"vendor"`
}

// VendorInfo represents vendor information
type VendorInfo struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

// ShipperFacilityGraphQL represents shipper facility relationship
type ShipperFacilityGraphQL struct {
	ID                 string               `json:"id"`
	FacilityCode       string               `json:"facilityCode"`
	Identifiers        []FacilityIdentifier `json:"identifiers"`
	ShipperApplication ShipperAppInfo       `json:"shipperApplication"`
}

// FacilityIdentifier represents facility identifier codes
type FacilityIdentifier struct {
	ID           string `json:"id"`
	FacilityCode string `json:"facilityCode"`
}

// ShipperAppInfo represents shipper application info
type ShipperAppInfo struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

// ShipmentDetailsStatusGraphQLResponse represents the response from the ShipmentDetailsStatus GraphQL query
type ShipmentDetailsStatusGraphQLResponse struct {
	Data struct {
		Shipment ShipmentDetailsGraphQL `json:"shipment"`
	} `json:"data"`
}

// ShipmentDetailsGraphQL represents detailed shipment information from GraphQL
type ShipmentDetailsGraphQL struct {
	ID                        string                     `json:"id"`
	ShipperShipmentReference  *string                    `json:"shipperShipmentReference"`
	ShipperMiles              float64                    `json:"shipperMiles"`
	TotalWeight               int                        `json:"totalWeight"`
	TotalPalletCount          int                        `json:"totalPalletCount"`
	CalculatedMiles           float64                    `json:"calculatedMiles"`
	BOLReferences             []string                   `json:"bolReferences"`
	POReferences              []string                   `json:"poReferences"`
	LoadingType               *string                    `json:"loadingType"`
	IsBackhaul                bool                       `json:"isBackhaul"`
	Team                      bool                       `json:"team"`
	FirstPickup               *ShipmentStopGraphQL       `json:"firstPickup"`
	LastDelivery              *ShipmentStopGraphQL       `json:"lastDelivery"`
	Stops                     []ShipmentStopGraphQL      `json:"stops"`
	Equipment                 EquipmentGraphQL           `json:"equipment"`
	Characteristics           ShipmentCharacteristics    `json:"characteristics"`
	ShipperTrackingSegment    *ShipperTrackingSegment    `json:"shipperTrackingSegment"`
	TMSShipmentPublicUUID     *string                    `json:"tmsShipmentPublicUuid"`
	Links                     ShipmentLinks              `json:"links"`
	State                     string                     `json:"state"`
	SpecialInstruction        *string                    `json:"specialInstruction"`
	HasClaims                 bool                       `json:"hasClaims"`
	HasOsd                    bool                       `json:"hasOsd"`
	ShipperApplication        ShipperApplicationDetails  `json:"shipperApplication"`
	TrackingOverview          TrackingOverview           `json:"trackingOverview"`
	RateConfirmationAgreement *RateConfirmationAgreement `json:"rateConfirmationAgreement"`
	StatusDetails             StatusDetails              `json:"statusDetails"`
}

// EquipmentGraphQL represents equipment information
type EquipmentGraphQL struct {
	ID         string `json:"id"`
	Name       string `json:"name"`
	ParentKey  string `json:"parentKey"`
	ParentName string `json:"parentName"`
}

// ShipmentCharacteristics represents shipment characteristics
type ShipmentCharacteristics struct {
	Hot                 bool `json:"hot"`
	MustGo              bool `json:"mustGo"`
	HaltClaimsBilling   bool `json:"haltClaimsBilling"`
	HaltClaimsInvoicing bool `json:"haltClaimsInvoicing"`
}

// ShipperTrackingSegment represents shipper tracking segment
type ShipperTrackingSegment struct {
	ID          string `json:"id"`
	Description string `json:"description"`
	Level       string `json:"level"`
}

// ShipmentLinks represents links related to the shipment
type ShipmentLinks struct {
	StarkCopyFMSShipmentURL string `json:"starkCopyFmsShipmentUrl"`
}

// ShipperApplicationDetails represents shipper application details
type ShipperApplicationDetails struct {
	ID                  string `json:"id"`
	CarrierInstructions string `json:"carrierInstructions"`
}

// TrackingOverview represents tracking overview information
type TrackingOverview struct {
	TrackingState            string `json:"trackingState"`
	CalculatedTrackingMethod string `json:"calculatedTrackingMethod"`
}

// RateConfirmationAgreement represents rate confirmation agreement
type RateConfirmationAgreement struct {
	ID               string `json:"id"`
	CarrierSignature string `json:"carrierSignature"`
	CarrierSignedAt  string `json:"carrierSignedAt"`
}

// StatusDetails represents detailed status information
type StatusDetails struct {
	DetailedState string `json:"detailedState"`
	PickupStatus  string `json:"pickupStatus"`
}

// CarriersResponse represents the response from GET /v10/carriers
type CarriersResponse struct {
	Carriers []Carrier `json:"carriers"`
}

// Carrier represents a carrier from the Stark API
type Carrier struct {
	ID                            int           `json:"id"`
	State                         string        `json:"state"`
	StateName                     string        `json:"stateName"`
	LegalName                     string        `json:"legalName"`
	ContactName                   *string       `json:"contactName"`
	DBAName                       *string       `json:"dbaName"`
	MCNumber                      *string       `json:"mcNumber"`
	DOTNumber                     string        `json:"dotNumber"`
	PhoneLabel                    *string       `json:"phoneLabel"`
	City                          string        `json:"city"`
	StateCode                     string        `json:"stateCode"`
	PostalCode                    string        `json:"postalCode"`
	LastNote                      *string       `json:"lastNote"`
	NotesCount                    int           `json:"notesCount"`
	IsBeforeActivated             bool          `json:"isBeforeActivated"`
	FMCSAPhone                    *string       `json:"fmcsaPhone"`
	FMCSARating                   *string       `json:"fmcsaRating"`
	InsuredAmount                 *string       `json:"insuredAmount"`
	InsuranceExpirationDate       *string       `json:"insuranceExpirationDate"`
	AdditionalCargoInsuranceTotal *string       `json:"additionalCargoInsuranceTotal"`
	TotalShipmentsCount           int           `json:"totalShipmentsCount"`
	Qualifications                []interface{} `json:"qualifications"`
	CarrierEquipments             []interface{} `json:"carrierEquipments"`
	CarrierManagers               []interface{} `json:"carrierManagers"`
}

// ShipmentProgressGraphQLResponse represents the response from ShipmentProgress GraphQL query
type ShipmentProgressGraphQLResponse struct {
	Data struct {
		Shipment struct {
			ID    string                 `json:"id"`
			Stops []ShipmentProgressStop `json:"stops"`
		} `json:"shipment"`
	} `json:"data"`
}

// ShipmentProgressStop represents a stop in the ShipmentProgress query
type ShipmentProgressStop struct {
	ID                  string                      `json:"id"`
	Address             string                      `json:"address"`
	AddressLine2        *string                     `json:"addressLine2"`
	City                string                      `json:"city"`
	StateCode           string                      `json:"stateCode"`
	Zipcode             string                      `json:"zipcode"`
	StopType            string                      `json:"stopType"`
	StopSequence        int                         `json:"stopSequence"`
	ArrivedAt           *string                     `json:"arrivedAt"`
	ArrivedAtUpdatedBy  ShipmentProgressUser        `json:"arrivedAtUpdatedBy"`
	ArrivedAtRelayedVia *string                     `json:"arrivedAtRelayedVia"`
	LeftAt              *string                     `json:"leftAt"`
	LeftAtUpdatedBy     ShipmentProgressUser        `json:"leftAtUpdatedBy"`
	LeftAtRelayedVia    *string                     `json:"leftAtRelayedVia"`
	Appointment         ShipmentProgressAppointment `json:"appointment"`
}

// ShipmentProgressUser represents a user who updated a check call
type ShipmentProgressUser struct {
	ID       string `json:"id"`
	FullName string `json:"fullName"`
}

// ShipmentProgressAppointment represents appointment info for a stop
type ShipmentProgressAppointment struct {
	Timezone string `json:"timezone"`
}

// UpdateShipmentBasicInfoResponse represents the response from UpdateShipmentBasicInfo mutation
type UpdateShipmentBasicInfoResponse struct {
	Data struct {
		UpdateShipment struct {
			ClientMutationID *string `json:"clientMutationId"`
			Shipment         struct {
				ID              string  `json:"id"`
				CalculatedMiles float64 `json:"calculatedMiles"`
				Characteristics struct {
					BounceCount         int  `json:"bounceCount"`
					FraudValidation     bool `json:"fraudValidation"`
					HasDropTrailerStops bool `json:"hasDropTrailerStops"`
					Hot                 bool `json:"hot"`
					MustGo              bool `json:"mustGo"`
					RolloverCount       int  `json:"rolloverCount"`
					SameDaySpot         bool `json:"sameDaySpot"`
					IsHighRisk          bool `json:"isHighRisk"`
				} `json:"characteristics"`
				IsBackhaul       bool    `json:"isBackhaul"`
				ShipperMiles     float64 `json:"shipperMiles"`
				Team             bool    `json:"team"`
				TotalPalletCount int     `json:"totalPalletCount"`
				TotalWeight      int     `json:"totalWeight"`
			} `json:"shipment"`
			Errors []GraphQLError `json:"errors"`
		} `json:"updateShipment"`
	} `json:"data"`
}

// UpdateShipmentEquipmentResponse represents the response from UpdateShipmentEquipment mutation
type UpdateShipmentEquipmentResponse struct {
	Data struct {
		UpdateShipmentEquipment struct {
			ClientMutationID *string `json:"clientMutationId"`
			Shipment         struct {
				ID                  string           `json:"id"`
				Equipment           EquipmentGraphQL `json:"equipment"`
				EquipmentAttributes []struct {
					ID             string `json:"id"`
					AttributeValue string `json:"attributeValue"`
					Parent         struct {
						ID  string `json:"id"`
						Key string `json:"key"`
					} `json:"parent"`
				} `json:"equipmentAttributes"`
			} `json:"shipment"`
			Errors []GraphQLError `json:"errors"`
		} `json:"updateShipmentEquipment"`
	} `json:"data"`
}

// CreateNoteResponse represents the response from CreateNote mutation
type CreateNoteResponse struct {
	Data struct {
		CreateNote struct {
			ClientMutationID *string        `json:"clientMutationId"`
			Note             NoteGraphQL    `json:"note"`
			Errors           []GraphQLError `json:"errors"`
		} `json:"createNote"`
	} `json:"data"`
}

// LoadNotesResponse represents the response from LoadNotes query
type LoadNotesResponse struct {
	Data struct {
		Shipment struct {
			ID    string        `json:"id"`
			Notes []NoteGraphQL `json:"notes"`
		} `json:"shipment"`
	} `json:"data"`
}

// NoteGraphQL represents a note from GraphQL
type NoteGraphQL struct {
	ID           string  `json:"id"`
	Body         string  `json:"body"`
	CallBackAt   *string `json:"callBackAt"`
	CalledBackAt *string `json:"calledBackAt"`
	CreatedAt    string  `json:"createdAt"`
	CreatedBy    struct {
		FirstName    string `json:"firstName"`
		LastName     string `json:"lastName"`
		ID           string `json:"id"`
		ParentUserID string `json:"parentUserId"`
		UserType     string `json:"userType"`
	} `json:"createdBy"`
	CreatedByID int `json:"createdById"`
	Documents   []struct {
		Name string `json:"name"`
		URL  string `json:"url"`
	} `json:"documents"`
}

// CreateShipmentIssueResponse represents the response from CreateShipmentIssue mutation
type CreateShipmentIssueResponse struct {
	Data struct {
		CreateShipmentIssue struct {
			ShipmentIssue struct {
				ID string `json:"id"`
			} `json:"shipmentIssue"`
			Errors []GraphQLError `json:"errors"`
		} `json:"createShipmentIssue"`
	} `json:"data"`
}

// ShipmentIssuesResponse represents the response from ShipmentIssues query
type ShipmentIssuesResponse struct {
	Data struct {
		Shipment struct {
			ID              string          `json:"id"`
			HasOSD          bool            `json:"hasOsd"`
			HasClaims       bool            `json:"hasClaims"`
			Issues          []ShipmentIssue `json:"issues"`
			IssueActionLogs []struct {
				ID              string `json:"id"`
				ShipmentIssueID string `json:"shipmentIssueId"`
				ActionType      string `json:"actionType"`
			} `json:"issueActionLogs"`
		} `json:"shipment"`
	} `json:"data"`
}

// ShipmentIssue represents a shipment issue from GraphQL
type ShipmentIssue struct {
	ID        string `json:"id"`
	CreatedAt string `json:"createdAt"`
	UpdatedAt string `json:"updatedAt"`
	CreatedBy struct {
		ID           string `json:"id"`
		ParentUserID string `json:"parentUserId"`
		FirstName    string `json:"firstName"`
		LastName     string `json:"lastName"`
	} `json:"createdBy"`
	DueAt *string `json:"dueAt"`
	Fault struct {
		ID    string `json:"id"`
		Fault string `json:"fault"`
	} `json:"fault"`
	Notes []struct {
		ID   string `json:"id"`
		Body string `json:"body"`
	} `json:"notes"`
	Owner struct {
		FollowerType string `json:"followerType"`
		FollowerID   string `json:"followerId"`
		FirstName    string `json:"firstName"`
		LastName     string `json:"lastName"`
		Nickname     string `json:"nickname"`
	} `json:"owner"`
	Priority int `json:"priority"`
	Reason   struct {
		ID     string `json:"id"`
		Reason string `json:"reason"`
	} `json:"reason"`
	State   string `json:"state"`
	Summary string `json:"summary"`
	Type    struct {
		ID          string `json:"id"`
		CategoryID  string `json:"categoryId"`
		Name        string `json:"name"`
		Description string `json:"description"`
	} `json:"type"`
	Updater struct {
		ID           string `json:"id"`
		ParentUserID string `json:"parentUserId"`
		FirstName    string `json:"firstName"`
		LastName     string `json:"lastName"`
	} `json:"updater"`
}

// UpdateShipmentStopsResponse represents the response from UpdateShipmentStops mutation
type UpdateShipmentStopsResponse struct {
	Data struct {
		UpdateShipmentStops struct {
			Errors []GraphQLError `json:"errors"`
		} `json:"updateShipmentStops"`
	} `json:"data"`
}

// CreateShipmentResponse represents the response from CreateShipment mutation
type CreateShipmentResponse struct {
	Data struct {
		CreateShipment struct {
			Shipment struct {
				ID string `json:"id"`
			} `json:"shipment"`
			Errors []GraphQLError `json:"errors"`
		} `json:"createShipment"`
	} `json:"data"`
}

// OktaAuthnResponse represents the response from Okta Authentication API
type OktaAuthnResponse struct {
	Status       string `json:"status"` // SUCCESS, MFA_REQUIRED, etc.
	SessionToken string `json:"sessionToken,omitempty"`
	StateToken   string `json:"stateToken,omitempty"`
	Embedded     struct {
		Factors []OktaFactor `json:"factors,omitempty"`
		User    struct {
			ID      string `json:"id"`
			Profile struct {
				Login     string `json:"login"`
				Email     string `json:"email"`
				FirstName string `json:"firstName"`
				LastName  string `json:"lastName"`
			} `json:"profile"`
		} `json:"user,omitempty"`
	} `json:"_embedded,omitempty"`
}

// OktaFactor represents an MFA factor
type OktaFactor struct {
	ID         string `json:"id"`
	FactorType string `json:"factorType"` // sms, push, totp, etc.
	Provider   string `json:"provider"`   // OKTA, GOOGLE, etc.
}

// OktaUserInfo represents user information from Okta
type OktaUserInfo struct {
	ID      string `json:"id"`
	Profile struct {
		Login     string `json:"login"`
		Email     string `json:"email"`
		FirstName string `json:"firstName"`
		LastName  string `json:"lastName"`
	} `json:"profile"`
}

// OktaTokenResponse represents the response from Okta token endpoint
type OktaTokenResponse struct {
	AccessToken string `json:"access_token"`
	TokenType   string `json:"token_type"`
	ExpiresIn   int    `json:"expires_in"`
	Scope       string `json:"scope"`
	IDToken     string `json:"id_token,omitempty"`
}

// OktaEndUserInfo represents user information from Okta enduser API
type OktaEndUserInfo struct {
	FirstName string `json:"firstName"`
	LastName  string `json:"lastName"`
	Email     string `json:"email"`
	Login     string `json:"login"`
	LastLogin string `json:"lastLogin"`
	UserID    string `json:"userId"` // This is the x-user-id we need!
	OrgName   string `json:"orgName"`
	OrgID     string `json:"orgId"`
	Admin     bool   `json:"admin"`
}
