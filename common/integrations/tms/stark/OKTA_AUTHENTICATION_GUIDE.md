# Okta Authentication Implementation Guide for Stark TMS

## Overview

Stark TMS uses <PERSON><PERSON> for authentication. **You should use the Okta API directly** rather than scraping browser interactions. This is more reliable, maintainable, and follows best practices.

## Recommendation: Use Okta API Directly ✅

**Why use Okta API:**
- ✅ More reliable and maintainable
- ✅ Well-documented standard OAuth2/OIDC flows
- ✅ Less fragile (won't break if UI changes)
- ✅ Better security practices
- ✅ Official support from Okta

**What you still need to scrape:**
- Configuration parameters (tenant URL, client_id, redirect_uri)
- Whether they use standard Okta endpoints or custom wrappers
- How to get `x-user-id` and `x-rogers-revision` after authentication

## Standard Okta Authentication API Flow

Okta provides a standard Authentication API that you can use:

### Option 1: Okta Authentication API (Recommended for non-browser apps)

```
POST https://nfi.okta.com/api/v1/authn
{
  "username": "<EMAIL>",
  "password": "password"
}

Response:
{
  "status": "SUCCESS" | "MFA_REQUIRED",
  "sessionToken": "xxx" (if SUCCESS),
  "stateToken": "xxx" (if MFA_REQUIRED),
  "_embedded": {
    "factors": [...] (if MFA_REQUIRED)
  }
}
```

### Option 2: OAuth2 Authorization Code Flow (If Stark uses OAuth)

```
1. GET https://nfi.okta.com/oauth2/v1/authorize?client_id=xxx&redirect_uri=xxx&response_type=code
2. POST https://nfi.okta.com/api/v1/authn (username/password)
3. POST https://nfi.okta.com/api/v1/authn/factors/{factorId}/verify (MFA if needed)
4. GET https://nfi.okta.com/oauth2/v1/authorize/callback?sessionToken=xxx
5. POST https://nfi.okta.com/oauth2/v1/token (exchange code for access_token)
```

## What to Scrape (Minimal - Just Configuration)

You still need to scrape to find the **configuration parameters**, but then use the standard Okta API endpoints:

1. **Okta Tenant URL**: `https://nfi.okta.com` ✅ **FOUND**
   - Confirmed from `https://nfi.okta.com/app/UserHome`

2. **Client ID** (if using OAuth):
   - Found in authorization URL: `?client_id=xxx`
   - Or check Stark's Okta app configuration

3. **Redirect URI**:
   - Where Okta redirects after auth
   - Usually `https://apps.transfix.io/callback` or similar

4. **Which flow they use**:
   - Direct Auth API (`/api/v1/authn`) - simpler
   - OAuth2 flow (`/oauth2/v1/authorize` + `/oauth2/v1/token`) - more complex

5. **How to get Stark API token**:
   - After Okta auth, how does Stark get the `x-user-token`?
   - Is it from a callback? Token exchange? Session cookie?

6. **User info endpoints**:
   - How to get `x-user-id` (from `/api/v1/users/me` or Stark API?)
   - How to get `x-rogers-revision` (from app config or API?)

## Step-by-Step: What to Scrape from Browser (Minimal)

### 1. Open Browser Developer Tools

1. Navigate to `https://apps-staging.transfix.io/` (or production URL)
2. Open Developer Tools (F12 or Cmd+Option+I)
3. Go to the **Network** tab
4. **Enable "Preserve log"** (important - prevents clearing on redirects)
5. Clear the network log

### 2. Initiate Login Flow

1. Click the login button or navigate to the login page
2. Enter credentials (use a test account)
3. Complete any MFA if required
4. **Watch the Network tab** - don't close it yet

### 3. Capture Initial Okta Request

**Look for:**
- Requests to `*.okta.com` or `*.okta.com/oauth2/`
- Initial redirect or authorization request

**What to capture:**
- **URL**: The full Okta authorization URL
- **Method**: Usually `GET` or `POST`
- **Query Parameters**: 
  - `client_id`
  - `redirect_uri`
  - `response_type`
  - `scope`
  - `state` (if present)
  - Any other OAuth parameters

**Example format:**
```
https://<tenant>.okta.com/oauth2/v1/authorize?
  client_id=xxx&
  redirect_uri=xxx&
  response_type=code&
  scope=openid+profile
```

### 4. Capture Login Form Submission

**Look for:**
- POST request to Okta login endpoint (usually `/api/v1/authn` or similar)
- Request that submits username/password

**What to capture:**
- **URL**: Full endpoint URL (e.g., `https://<tenant>.okta.com/api/v1/authn`)
- **Method**: `POST`
- **Headers**: 
  - `Content-Type`
  - `Accept`
  - `X-Okta-User-Agent-Extended` (if present)
  - Any custom headers
- **Request Body**: 
  - Field names for username/password
  - Any additional fields (state, token, etc.)
  - Format (JSON, form-encoded, etc.)

**Example request body:**
```json
{
  "username": "<EMAIL>",
  "password": "password123",
  "options": {
    "warnBeforePasswordExpired": true,
    "multiOptionalFactorEnroll": true
  }
}
```

### 5. Capture MFA Flow (if applicable)

**If MFA is required, look for:**
- Additional POST requests after initial login
- MFA challenge/verify endpoints

**What to capture:**
- **URL**: MFA verification endpoint
- **Method**: Usually `POST`
- **Request Body**: 
  - Factor ID or type
  - Passcode/token field
  - State token from previous response

**Example:**
```json
{
  "stateToken": "xxx",
  "factorId": "xxx",
  "passCode": "123456"
}
```

### 6. Capture Session Token/Cookie

**After successful authentication, look for:**
- Response containing `sessionToken` or similar
- Cookies being set (check the **Cookies** tab or **Response Headers**)
- Redirect to callback URL

**What to capture:**
- **Session Token**: Value from response JSON
- **Cookies**: 
  - `sid` (Okta session cookie)
  - Any other session-related cookies
- **Redirect URL**: Where Okta redirects after auth

### 7. Capture Token Exchange

**Look for:**
- Request that exchanges Okta session for Stark API token
- Usually happens after redirect to Stark application
- May be a callback endpoint or token exchange endpoint

**What to capture:**
- **URL**: Token exchange endpoint
- **Method**: Usually `POST`
- **Headers**: Include session cookie or token
- **Request Body**: 
  - Session token
  - Grant type
  - Any other parameters
- **Response**: Should contain `access_token` or similar

### 8. Capture Stark API Headers

**After authentication, make a test API call:**
1. Navigate to a page that loads shipment data
2. Look for requests to `/stark/v8/shipments` or `/marketplace`
3. Check the **Request Headers**

**What to capture:**
- `x-user-token` - Where does this come from?
- `x-user-email` - Usually the logged-in user's email
- `x-user-id` - User ID (may be in Okta user profile or session)
- `x-rogers-revision` - App revision/version (may be in app config or response)

**Where to find these:**
- Check JavaScript code that sets these headers
- Check localStorage/sessionStorage
- Check initial app load response
- Check user profile API response

### 9. Capture User Profile/Info

**Look for:**
- API call that fetches current user info
- Usually happens after login

**What to capture:**
- **URL**: User profile endpoint
- **Response**: Should contain user ID, email, and other info needed for headers

## Implementation Checklist

Once you've scraped the above, you'll need to implement:

- [ ] **Step 1**: Initial Okta authorization request
  - Build authorization URL with correct parameters
  - Handle redirects

- [ ] **Step 2**: Submit credentials
  - POST to Okta authn endpoint
  - Handle response (may need MFA)

- [ ] **Step 3**: Handle MFA (if required)
  - Detect MFA requirement from response
  - Prompt for MFA code (or handle automatically if possible)
  - Submit MFA verification

- [ ] **Step 4**: Extract session token
  - Parse response for `sessionToken` or session cookie
  - Store session information

- [ ] **Step 5**: Exchange for API token
  - Call token exchange endpoint
  - Extract `access_token` or API token

- [ ] **Step 6**: Get user info
  - Call user profile endpoint
  - Extract `x-user-id`, `x-user-email`
  - Extract `x-rogers-revision` (may need separate API call)

- [ ] **Step 7**: Store tokens
  - Store `access_token` in `tms.AccessToken`
  - Store expiration in `tms.AccessTokenExpirationDate`
  - Store `x-user-token` in `tms.APIKey`
  - Store `x-user-id` and `x-rogers-revision` in `tms.Note` (or find better place)

- [ ] **Step 8**: Implement refresh
  - Detect when token expires
  - Re-authenticate or refresh token
  - Update stored tokens

## Tools to Help

### Browser Extensions
- **Okta Browser Plugin** - May show Okta-specific network requests
- **ModHeader** - To modify request headers and see what's required

### Network Tab Filters
- Filter by: `okta` to see only Okta requests
- Filter by: `XHR` or `Fetch` to see API calls
- Filter by: `Doc` to see page loads

### What to Export
1. **HAR file**: Export network log as HAR (File → Save all as HAR)
2. **cURL commands**: Right-click on requests → Copy → Copy as cURL
3. **Screenshots**: Of important request/response details

## Common Okta Patterns

### Standard OAuth2 Flow
```
1. GET /oauth2/v1/authorize?client_id=xxx&redirect_uri=xxx
2. POST /api/v1/authn (username/password)
3. POST /api/v1/authn/factors/{factorId}/verify (MFA)
4. GET /oauth2/v1/authorize/callback?sessionToken=xxx
5. POST /oauth2/v1/token (exchange code for token)
```

### Session-Based Flow
```
1. POST /api/v1/authn (username/password)
2. POST /api/v1/authn/factors/{factorId}/verify (MFA)
3. Response contains sessionToken
4. Use sessionToken to get session cookie
5. Session cookie used for subsequent API calls
```

## Questions to Answer

When scraping, try to answer:

1. **What is the Okta tenant URL?**
   - Format: `https://<tenant>.okta.com`

2. **What is the client_id?**
   - Usually in the authorization URL

3. **What is the redirect_uri?**
   - Where Okta redirects after auth

4. **Is MFA always required?**
   - Or only for certain users?

5. **How long do tokens last?**
   - Check `expires_in` in token response

6. **Can tokens be refreshed?**
   - Or do we need to re-authenticate?

7. **Where does x-rogers-revision come from?**
   - Is it static? From an API? From app config?

8. **Where does x-user-id come from?**
   - From Okta user profile? From Stark user API?

## Example: What a Scraped Flow Might Look Like

```bash
# Step 1: Initial authorization
GET https://transfix.okta.com/oauth2/v1/authorize?
  client_id=0oa1234567890&
  redirect_uri=https://apps.transfix.io/callback&
  response_type=code&
  scope=openid+profile

# Step 2: Login
POST https://transfix.okta.com/api/v1/authn
{
  "username": "<EMAIL>",
  "password": "password"
}

# Response:
{
  "status": "MFA_REQUIRED",
  "stateToken": "xxx",
  "_embedded": {
    "factors": [...]
  }
}

# Step 3: MFA (if required)
POST https://transfix.okta.com/api/v1/authn/factors/sms123/verify
{
  "stateToken": "xxx",
  "passCode": "123456"
}

# Response:
{
  "status": "SUCCESS",
  "sessionToken": "xxx"
}

# Step 4: Get session cookie
GET https://transfix.okta.com/login/sessionCookieRedirect?
  token=xxx&
  redirectUrl=https://apps.transfix.io

# Step 5: Exchange for API token (if needed)
POST https://apps.transfix.io/api/auth/token
{
  "sessionToken": "xxx"
}

# Response:
{
  "access_token": "xxx",
  "user_id": "839816",
  "user_email": "<EMAIL>",
  "rogers_revision": "202511181545-75c32848f7fa9980dc2a7136c292bf9223084efe"
}
```

## Next Steps After Scraping

1. **Document everything** - Save all URLs, request/response examples
2. **Test the flow** - Try to replicate in code
3. **Handle edge cases** - MFA, expired tokens, errors
4. **Implement in `auth.go`** - Replace TODOs with actual implementation
5. **Test thoroughly** - Multiple users, MFA scenarios, token refresh

## Need Help?

If you encounter issues:
- Check if Okta has API documentation
- Look for Okta SDK examples
- Check if there's a simpler token endpoint we're missing
- Verify if `x-rogers-revision` is static or needs to be fetched

