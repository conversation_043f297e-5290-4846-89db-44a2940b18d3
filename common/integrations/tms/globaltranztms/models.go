package globaltranztms

type (
	// Customer Models
	GetCustomerBoardRequest struct {
		FromDate                  string   `json:"fromDate"`
		IncludeInactive           bool     `json:"includeInactive"`
		IncludeMasterAccountsOnly bool     `json:"includeMasterAccountsOnly"`
		IsTimeframeAll            bool     `json:"isTimeframeAll"`
		PageNumber                int      `json:"pageNumber"`
		PageSize                  int      `json:"pageSize"`
		SearchFilter              []string `json:"searchFilter"`
		SearchText                string   `json:"searchText"`
		SortModel                 []string `json:"sortModel"`
		ToDate                    string   `json:"toDate"`
	}

	GetCustomerBoardResponse struct {
		Message       any                 `json:"message"`
		DidError      bool                `json:"didError"`
		ErrorMessages any                 `json:"errorMessages"`
		Model         []CustomerBoardItem `json:"model"`
		PageSize      int                 `json:"pageSize"`
		PageNumber    int                 `json:"pageNumber"`
		ItemsCount    int                 `json:"itemsCount"`
		PageCount     int                 `json:"pageCount"`
	}

	CustomerBoardItem struct {
		CustomerBk   int    `json:"customerBk"`
		CustomerName string `json:"customerName"`
		SalesRepName string `json:"salesRepName"`
		PhoneNumber  string `json:"phoneNumber"`
		EmailAddress string `json:"emailAddress"`
	}
)
