package globaltranztms

type NoteType int

const (
	NoteTypeToAgent         NoteType = 13
	NoteTypeEventLog        NoteType = 17
	NoteTypeMiscellaneous   NoteType = 18
	NoteTypeFromAgent       NoteType = 21
	NoteTypeCarrierOffer    NoteType = 24
	NoteTypeTransitExternal NoteType = 25
	NoteTypeTrackingUpdate  NoteType = 27
	NoteTypeETA             NoteType = 28
)

// NoteTypeToString converts a note type ID to a readable string
func NoteTypeToString(noteTypeID int) string {
	switch NoteType(noteTypeID) {
	case NoteTypeToAgent:
		return "To Agent"
	case NoteTypeCarrierOffer:
		return "Carrier Offer"
	case NoteTypeTransitExternal:
		return "Transit External"
	case NoteTypeTrackingUpdate:
		return "Tracking Update"
	case NoteTypeETA:
		return "ETA"
	case NoteTypeFromAgent:
		return "From Agent"
	case NoteTypeEventLog:
		return "Event Log"
	case NoteTypeMiscellaneous:
		return "Miscellaneous"
	default:
		return ""
	}
}
