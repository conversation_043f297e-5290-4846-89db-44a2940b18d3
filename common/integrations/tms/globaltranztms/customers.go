package globaltranztms

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"strconv"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	tmsCustomerDB "github.com/drumkitai/drumkit/common/rds/tms_customers"
)

const (
	GetCustomerBoardPath = "api/tms-customer/Customer/Board"
)

// GetCustomers fetches all customers from GlobalTranz TMS and saves them to the database
func (gt GlobalTranz) GetCustomers(ctx context.Context) ([]models.TMSCustomer, error) {
	var uniqueCustomers []models.TMSCustomer
	var err error

	ctx, metaSpan := otel.StartSpan(ctx, "GetCustomersGlobalTranzTMS", otel.IntegrationAttrs(gt.tms))
	defer func() { metaSpan.End(err) }()

	log.Info(ctx, "fetching customers from GlobalTranz TMS")

	allCustomers, err := gt.getCustomers(ctx)
	if err != nil {
		log.Error(
			ctx,
			"failed to fetch customer pages",
			zap.Error(err),
		)
		return nil, err
	}

	// Deduplicate customers by ExternalTMSID (CustomerBK in TMS)
	seenExternalCustomerIDs := make(map[int]bool)
	for _, item := range allCustomers {
		if _, exists := seenExternalCustomerIDs[item.CustomerBk]; !exists {
			seenExternalCustomerIDs[item.CustomerBk] = true

			customer := gt.mapCustomerBoardItemToTMSCustomer(item)
			uniqueCustomers = append(uniqueCustomers, customer)
		}
	}

	log.Info(
		ctx,
		"mapped customers to TMSCustomer model",
		zap.Int("totalFetched", len(allCustomers)),
		zap.Int("uniqueCustomers", len(uniqueCustomers)),
	)

	if len(uniqueCustomers) > 0 {
		if err = tmsCustomerDB.RefreshTMSCustomers(ctx, &uniqueCustomers); err != nil {
			log.Error(
				ctx,
				"failed to save customers to database",
				zap.Error(err),
			)
			return nil, err
		}

		log.Info(
			ctx,
			"successfully saved customers to database",
			zap.Int("count", len(uniqueCustomers)),
		)
	}

	return uniqueCustomers, nil
}

func (gt GlobalTranz) getCustomers(ctx context.Context) ([]CustomerBoardItem, error) {
	var allCustomers []CustomerBoardItem
	pageSize := 1000
	pageNumber := 1

	for {
		response, err := gt.getCustomerBoardPage(ctx, pageNumber, pageSize)
		if err != nil {
			log.Error(
				ctx,
				"failed to fetch customer board page",
				zap.Int("pageNumber", pageNumber),
				zap.Error(err),
			)
			return nil, err
		}

		allCustomers = append(allCustomers, response.Model...)

		log.Debug(
			ctx,
			"fetched customer board page",
			zap.Int("pageNumber", pageNumber),
			zap.Int("pageSize", pageSize),
			zap.Int("itemsInPage", len(response.Model)),
			zap.Int("totalItems", response.ItemsCount),
			zap.Int("totalPages", response.PageCount),
		)

		if pageNumber >= response.PageCount || len(response.Model) == 0 {
			break
		}

		pageNumber++
	}

	log.Info(
		ctx,
		"fetched all customer board pages",
		zap.Int("totalCustomers", len(allCustomers)),
		zap.Int("totalPages", pageNumber),
	)

	return allCustomers, nil
}

func (gt GlobalTranz) getCustomerBoardPage(
	ctx context.Context,
	pageNumber, pageSize int,
) (GetCustomerBoardResponse, error) {
	var result GetCustomerBoardResponse

	fromDate := time.Date(1970, 1, 1, 3, 0, 0, 0, time.UTC)
	toDate := time.Now()

	payload := GetCustomerBoardRequest{
		FromDate:                  fromDate.Format(time.RFC3339),
		IncludeInactive:           false,
		IncludeMasterAccountsOnly: true,
		IsTimeframeAll:            true,
		PageNumber:                pageNumber,
		PageSize:                  pageSize,
		SearchFilter:              []string{},
		SearchText:                "",
		SortModel:                 []string{},
		ToDate:                    toDate.Format(time.RFC3339),
	}

	reqBody, err := json.Marshal(payload)
	if err != nil {
		log.Error(
			ctx,
			"failed to marshal request body",
			zap.Error(err),
		)
		return GetCustomerBoardResponse{}, err
	}

	err = gt.doWithRetry(
		ctx,
		http.MethodPost,
		gt.tmsHost,
		GetCustomerBoardPath,
		nil,
		bytes.NewBuffer(reqBody),
		&result,
	)
	if err != nil {
		log.Error(
			ctx,
			"failed to fetch customer board",
			zap.Error(err),
		)
		return GetCustomerBoardResponse{}, err
	}

	if result.DidError {
		log.Error(
			ctx,
			"globaltranztms customer board returned errors",
			zap.Any("errorMessages", result.ErrorMessages),
		)
		return GetCustomerBoardResponse{}, errors.New("globaltranztms customer board returned errors")
	}

	return result, nil
}

func (gt GlobalTranz) mapCustomerBoardItemToTMSCustomer(item CustomerBoardItem) models.TMSCustomer {
	return models.TMSCustomer{
		CompanyCoreInfo: models.CompanyCoreInfo{
			ExternalTMSID: strconv.Itoa(item.CustomerBk),
			Name:          item.CustomerName,
			Phone:         item.PhoneNumber,
			Email:         item.EmailAddress,
		},
		TMSIntegrationID: gt.tms.ID,
		TMSIntegration:   gt.tms,
		OwnerName:        item.SalesRepName,
	}
}
