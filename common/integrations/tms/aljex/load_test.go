package aljex

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"io"
	"net/http"
	"os"
	"regexp"
	"strings"
	"testing"
	"time"

	"github.com/PuerkitoBio/goquery"
	"github.com/gofiber/fiber/v2/log"
	"github.com/google/go-cmp/cmp"
	"github.com/joho/godotenv"
	"github.com/kelseyhightower/envconfig"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/drumkitai/drumkit/common/models"
)

type envVars struct {
	AppID           string `envconfig:"NFI_APP_ID" required:"true"`
	Password        string `envconfig:"NFI_PASS" required:"true"`
	TwoFactorSecret string `envconfig:"NFI_TWO_FACTOR_SECRET" required:"true"`
}

var (
	env envVars

	oldLoad = models.Load{
		FreightTrackingID: "2080916",
		ServiceID:         1,
		LoadCoreInfo: models.LoadCoreInfo{
			Status: "covered",
			Specifications: models.Specifications{
				MinTempFahrenheit:   -10,
				TotalOutPalletCount: 22,
			},
			// NOTE: that we currently do not support updating PO nums because it's multiple fields combined into 1
			PONums: "3100000,3100001,3100002,3100003,3100004,3100005",
			Customer: models.Customer{
				CompanyCoreInfo: models.CompanyCoreInfo{
					Name:         "7-11 C/O NFI LOGISTICS",
					AddressLine1: "1515 BURNT MILL ROAD",
					City:         "CHERRY HILL",
					State:        "NJ",
					Zipcode:      "08003",
				},
			},
			BillTo: models.BillTo{
				CompanyCoreInfo: models.CompanyCoreInfo{
					Name:         "7-11 C/O NFI LOGISTICS",
					AddressLine1: "1515 BURNT MILL ROAD",
					City:         "CHERRY HILL",
					State:        "NJ",
					Zipcode:      "08003",
				},
			},
			Consignee: models.Consignee{
				CompanyCoreInfo: models.CompanyCoreInfo{
					City:  "BOSTON",
					State: "MA",
				},
			},
			Pickup: models.Pickup{
				CompanyCoreInfo: models.CompanyCoreInfo{
					City:  "BOSTON",
					State: "MA",
				},
				ReadyTime: models.NullTime{
					Time:  time.Date(2023, time.November, 2, 0, 0, 0, 0, time.UTC),
					Valid: true,
				},
			},
			Carrier: models.Carrier{
				// NOTE: Load MUST be covered (aka assigned to a carrier) in order to be able to fill out dependent
				// fields like confirmation times and delivery times in test.
				Name:      "TE SLAA TRUCKING, LTD.",
				SCAC:      "TESL",
				MCNumber:  "144350",
				DOTNumber: "69933",
				Phone:     "(*************",
			},
			// NOTE: Editing rate fields is not supported rn, so these should not change upon update
			RateData: models.RateData{
				CustomerRateType: "All In",
				// CustomerNumHours:  23,
				// CustomerLHRateUSD: 850,

				CarrierRateType: "Hourly",
				// CarrierLHRateUSD: 33,
				// CarrierNumHours:  11,
				CarrierMaxRate: 0.5,

				FSCPercent:    models.Ptr(float32(7)),
				FSCPerMile:    nil,
				NetProfitUSD:  546.50,
				ProfitPercent: 60.09,
			},
		},
	}

	newLoad = models.Load{
		FreightTrackingID: "2080916",
		ServiceID:         1,
		LoadCoreInfo: models.LoadCoreInfo{
			Status: "delivered", // automatically updated by Aljex
			PONums: "3100000,3100001,3100002,3100003,3100004,3100005",
			Specifications: models.Specifications{
				MinTempFahrenheit:   -10,
				TotalOutPalletCount: 22,
			},
			// NOTE: Editing rate fields is not supported rn, so these should not change upon update
			RateData: models.RateData{
				CustomerRateType: "All In",
				// CustomerNumHours:  23,
				// CustomerLHRateUSD: 850,

				CarrierRateType: "Hourly",
				// CarrierLHRateUSD: 33,
				// CarrierNumHours:  11,
				CarrierMaxRate: 0.5,

				FSCPercent:    models.Ptr(float32(7)),
				FSCPerMile:    nil,
				NetProfitUSD:  546.50,
				ProfitPercent: 60.09,
			},
			Customer: models.Customer{
				CompanyCoreInfo: models.CompanyCoreInfo{
					ExternalTMSID: "",
					Name:          "7-11 C/O NFI LOGISTICS",
					AddressLine1:  "1515 BURNT MILL ROAD",
					AddressLine2:  "",
					City:          "CHERRY HILL",
					State:         "NJ",
					Zipcode:       "08003",
					Country:       "",
					Contact:       "CUSTOMER CONTACT",
					Phone:         "(*************",
					Email:         "",
				},
				RefNumber: "CUSTOMERREF",
			},
			BillTo: models.BillTo{
				CompanyCoreInfo: models.CompanyCoreInfo{
					ExternalTMSID: "",
					Name:          "7-11 C/O NFI LOGISTICS",
					AddressLine1:  "1515 BURNT MILL ROAD",
					AddressLine2:  "",
					City:          "CHERRY HILL",
					State:         "NJ",
					Zipcode:       "08003",
					Country:       "",
					// Can't modify BillTo Contact even on Aljex, probs should be read-only like other BillTo fields
					Contact: "",
					Phone:   "",
					Email:   "",
				},
			},
			Pickup: models.Pickup{
				CompanyCoreInfo: models.CompanyCoreInfo{
					ExternalTMSID: "",
					Name:          "D & B GROCERS INC",
					AddressLine1:  "501 BOYLSTON",
					AddressLine2:  "APT #1",
					City:          "BOSTON",
					State:         "MA",
					Zipcode:       "02116",
					Country:       "United States",
					Contact:       "PICKUP CONTACT",
					Phone:         "(*************",
					Email:         "<EMAIL>",
				},
				BusinessHours: "PU BIZHRS",
				RefNumber:     "PU1234",
				ReadyTime: models.NullTime{
					Time:  time.Date(2023, time.October, 12, 8, 0, 0, 0, time.UTC),
					Valid: true,
				},
				ApptStartTime: models.NullTime{
					Time:  time.Date(2023, time.October, 11, 23, 0, 0, 0, time.UTC),
					Valid: true,
				},
				ApptNote: "HERE'S AN APPT NOTE",
			},
			Consignee: models.Consignee{
				CompanyCoreInfo: models.CompanyCoreInfo{
					ExternalTMSID: "",
					// TODO: support changing PU and Consignee name
					Name:         "DHL",
					AddressLine1: "51 BOYLSTON ST",
					AddressLine2: "APT #2",
					City:         "ROXBURY",
					State:        "MA",
					Zipcode:      "02118",
					Country:      "United States",
					Contact:      "CONSIGNEE CONTACT",
					Phone:        "(*************",
					Email:        "<EMAIL>",
				},
				BusinessHours: "MF 8-5",
				RefNumber:     "CONSIGNEEREF",
				MustDeliver: models.NullTime{
					Time:  time.Date(2023, time.October, 21, 0, 0, 0, 0, time.UTC),
					Valid: true,
				},
				ApptStartTime: models.NullTime{
					Time:  time.Date(2023, time.October, 28, 22, 0, 0, 0, time.UTC),
					Valid: true,
				},
				ApptNote: "COSIGNEE APPT NOTE",
			},
			Carrier: models.Carrier{
				// NOTE: Load MUST be covered (aka assigned to a carrier) in order to be able to fill out dependent
				// fields like confirmation times and delivery times in test.
				// TODO: support assigning a load to a carrier
				Name:                 "TE SLAA TRUCKING, LTD.",
				SCAC:                 "TESL",
				MCNumber:             "144350",
				DOTNumber:            "69933",
				Phone:                "(*************",
				Dispatcher:           "GUY DISPATCHER",
				SealNumber:           "DJFH123",
				FirstDriverName:      "JOHN DRIVER",
				FirstDriverPhone:     "(*************",
				SecondDriverName:     "Jane Driver",
				SecondDriverPhone:    "(*************",
				Email:                "<EMAIL>",
				DispatchCity:         "BROCKTON",
				DispatchState:        "MA",
				ExternalTMSTruckID:   "TRUCK 123",
				ExternalTMSTrailerID: "TRAILER 456",
				// Load must be "covered" aka assigned to a carrier in order to be able to edit
				// confirmation and delivery time these fields, but Aljex doesn't set them to read-only
				ConfirmationSentTime: models.NullTime{
					Time:  time.Date(2023, time.November, 1, 10, 0, 0, 0, time.UTC),
					Valid: true,
				},
				ConfirmationReceivedTime: models.NullTime{
					Time:  time.Date(2023, time.November, 2, 11, 0, 0, 0, time.UTC),
					Valid: true,
				},
				DispatchedTime: models.NullTime{
					Time:  time.Date(2023, time.November, 3, 12, 0, 0, 0, time.UTC),
					Valid: true,
				},
				ExpectedPickupTime: models.NullTime{
					Time:  time.Date(2023, time.November, 4, 13, 0, 0, 0, time.UTC),
					Valid: true,
				},
				PickupStart: models.NullTime{
					Time:  time.Date(2023, time.November, 5, 14, 0, 0, 0, time.UTC),
					Valid: true,
				},
				PickupEnd: models.NullTime{
					Time:  time.Date(2023, time.November, 6, 15, 0, 0, 0, time.UTC),
					Valid: true,
				},
				ExpectedDeliveryTime: models.NullTime{
					Time:  time.Date(2023, time.November, 7, 16, 0, 0, 0, time.UTC),
					Valid: true,
				},
				DeliveryStart: models.NullTime{
					Time:  time.Date(2023, time.November, 8, 17, 0, 0, 0, time.UTC),
					Valid: true,
				},
				DeliveryEnd: models.NullTime{
					Time:  time.Date(2023, time.November, 9, 18, 0, 0, 0, time.UTC),
					Valid: true,
				},
				SignedBy: "THE SIGNER",
			},
		},
	}
)

// NOTE: that in order for this test to work, the PRO must start with the minimum required values listed in `oldLoad`.
// We don't yet support load building or carrier assignment, so if Customer, Bill To, or Carrier are different in Aljex,
// then they need to be changed back to match `oldLoad`.
func TestLiveUpdateLoad(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping TestLiveUpdateLoad: run with LIVE_TEST=true to enable")
		return
	}

	ctx := context.Background()

	require.Contains(t, nfiTestPROs, oldLoad.FreightTrackingID)
	require.NoError(t, loadEnv())
	require.NotEmpty(t, env)

	integration := aljexTestClient(ctx, env.AppID, env.Password, env.TwoFactorSecret)
	client, err := New(ctx, integration)
	require.NoError(t, err)

	res, _, err := client.GetLoad(ctx, oldLoad.FreightTrackingID)
	require.NoError(t, err)

	if cmp.Equal(res, oldLoad) {
		log.Info("current load does not match oldLoad, resetting")

		res, _, err = client.UpdateLoad(ctx, &oldLoad, &oldLoad)
		require.NoError(t, err)
		require.Equal(t, oldLoad, res, "failed to reset PRO %s, current load does not match oldLoad",
			oldLoad.FreightTrackingID)
	}

	res, _, err = client.UpdateLoad(ctx, &oldLoad, &newLoad)
	require.NoError(t, err)
	assert.Equal(t, newLoad, res)

}

// While TestLiveUpdateLoad checks if we inadvertently make changes to the PRO, it only checks the values
// we parse into a struct, not all of the HTML. This test does compare the HTML.
func TestLiveUpdateRawHTML(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping TestLiveUpdateRawHTML: run with LIVE_TEST=true to enable")
		return
	}

	ctx := context.Background()
	pro := "2080916"

	require.Contains(t, nfiTestPROs, pro)
	require.NoError(t, loadEnv())
	require.NotEmpty(t, env)

	integration := aljexTestClient(ctx, env.AppID, env.Password, env.TwoFactorSecret)
	client, err := New(ctx, integration)
	require.NoError(t, err)

	// Get original HTML
	res, err := client.getLoadHTML(ctx, pro)
	require.NoError(t, err)

	originalHTML := string(res)

	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(res))
	require.NoError(t, err)
	aljexLoad := client.parseLoadHTML(ctx, doc, pro)
	originalLoad := aljexLoad.ToLoadModel(ctx, pro, 1, 1)

	// Call Update func with no changes
	_, _, err = client.UpdateLoad(ctx, originalLoad, originalLoad)
	require.NoError(t, err)

	res, err = client.getLoadHTML(ctx, pro)
	require.NoError(t, err)

	updatedHTML := string(res)

	// Remove the lines we expect to differ on each page refresh
	ctlrecPattern := `(?i)<input(.*?)name=["]*ctlrec["]*(.*?)value=".+?">`

	r, err := regexp.Compile(ctlrecPattern)
	require.NoError(t, err)

	originalHTML = r.ReplaceAllString(originalHTML, `"<input type=hidden name=ctlrec value="XXX"`)
	updatedHTML = r.ReplaceAllString(updatedHTML, `"<input type=hidden name=ctlrec value="XXX"`)

	lastUpdatedPattern := `(?i)<input(.*?)id=["]*last_upd["]*(.*?)value=".+?">`

	r, err = regexp.Compile(lastUpdatedPattern)
	require.NoError(t, err)

	originalHTML = r.ReplaceAllString(originalHTML, `"<input type=hidden id=last_upd value="YYY"`)
	updatedHTML = r.ReplaceAllString(updatedHTML, `"<input type=hidden id=last_upd value="YYY"`)

	pattern := `(?i)<input(.*?)id=["]*fpwsys["]*(.*?)value=".+?">`

	r, err = regexp.Compile(pattern)
	require.NoError(t, err)

	originalHTML = r.ReplaceAllString(originalHTML, `"<input type=hidden id=last_upd value="YYY"`)
	updatedHTML = r.ReplaceAllString(updatedHTML, `"<input type=hidden id=last_upd value="YYY"`)

	assert.Equal(t, originalHTML, updatedHTML)

}

func TestParseLoadInfo(t *testing.T) {
	ctx := context.Background()
	file, err := os.Open("./testHTML/pro_sample.html")
	require.NoError(t, err)
	defer file.Close()

	doc, err := goquery.NewDocumentFromReader(file)
	require.NoError(t, err)

	// Init empty client
	// NOTE: Because pro_sample.html defines a load operator (#fld62), this test
	// doesn't need a more fleshed out test client to handle the case when we must load a separate page.
	// See TestParseLoadOperatorAssignable instead.
	client := &Aljex{}

	actualLoad := client.parseLoadHTML(ctx, doc, "1234")

	expectedLoad := &LoadData{
		Status:         StringValue{Value: "delivered", FormName: "", IsReadOnly: true},
		OutPalletCount: FloatValue{Value: 9, FormName: "fld97", IsReadOnly: false},
		PONums:         StringValue{Value: "", FormName: "", IsReadOnly: true},
		Operator:       StringValue{Value: "JQUELIN", FormName: "fld62", IsReadOnly: false, MaxLength: 8},
		// TotalWeight:    FloatValue{Value: 0, FormName: "fld25", IsReadOnly: false},
		EquipmentType: StringValue{Value: "VAN", FormName: "fld18", IsReadOnly: false},
		SpecialInstructions: StringValue{
			//nolint:lll
			Value:      `DESTINATION: RET-US-Woodburn DESTIN ATION: RET-US-WOODBURN DESTINATION: OODBURN DESTINATION: RET-US-WOODBUR N DESTINATION: RET-US-WOODBURN DEST ET-US-WOODBURN DESTINATION: RET-US- Woodburn DESTINATION: RET-US-Woodbu - DRIVER MUST SIGN OFF CARTON COUNT, NOT PALLET COUNT AT PICKUP AND DELIVERY. PALLETS CANNOT BE DOUBLE STACKED OR REWORKED.`,
			FormName:   "notes",
			IsReadOnly: false,
		},
		TotalPieces: FloatValue{Value: 171, FormName: "fld11", IsReadOnly: false},
		TotalWeight: FloatValue{Value: 1234, FormName: "fld13", IsReadOnly: false},
		AdditionalReferences: models.AdditionalReferences{
			{
				Qualifier: "BL",
				Number:    "bol1",
			},
			{
				Qualifier: "LOAD",
				Number:    "load1",
			},
			{
				Qualifier: "",
				Number:    "ref1",
			},
			{
				Qualifier: "",
				Number:    "ref2",
			},
			{
				Qualifier: "",
				Number:    "ref3",
			},
			{
				Qualifier: "",
				Number:    "ref4",
			},
			{
				Qualifier: "",
				Number:    "ref5",
			},
			{
				Qualifier: "",
				Number:    "ref6",
			},
		},
		Description: StringValue{Value: "summary of goods", FormName: "fld12", MaxLength: 18},
		Commodities: []CommodityItem{
			{
				Pieces:       StringValue{Value: "171", FormName: "exa01", MaxLength: 5},
				FreightClass: StringValue{Value: "", FormName: "exa03"},
				Weight:       StringValue{Value: "1234", FormName: "exa04", MaxLength: 5},
				Length:       StringValue{Value: "", FormName: "exc01", MaxLength: 4},
				Width:        StringValue{Value: "", FormName: "exc02", MaxLength: 4},
				Height:       StringValue{Value: "", FormName: "exc03", MaxLength: 4},
				ProductCode:  StringValue{Value: "SKU124", FormName: "exa05", MaxLength: 15},
				Description:  StringValue{Value: "marshmallow fluff", FormName: "exa07", MaxLength: 40},
			},
		},

		Carrier: Carrier{
			Carrier: StringValue{Value: "ALPHA BRAVO TRANSPORTATION.",
				FormName: "fldcarr", IsReadOnly: true, MaxLength: 30},
			Dot:  StringValue{Value: "123123dot", FormName: "dotno", IsReadOnly: true, MaxLength: 9},
			Mc:   StringValue{Value: "230428", FormName: "mcno", IsReadOnly: true, MaxLength: 9},
			Scac: StringValue{Value: "SFPA", FormName: "scac", IsReadOnly: true, MaxLength: 4},
			Seal: StringValue{Value: "", FormName: "di_fld158",
				IsReadOnly: false, OtherFormNames: []string{"di_fld158", "fld158"},
				MaxLength: 18},
			Phone: StringValue{Value: "(*************", FormName: "fld172", IsReadOnly: false, MaxLength: 14},
			Email: StringValue{Value: "<EMAIL>", FormName: "fld196", IsReadOnly: false, MaxLength: 60},
			FirstDriver: StringValue{Value: "", FormName: "fld74",
				IsReadOnly: false, OtherFormNames: []string{"fld74"},
				MaxLength: 18},
			FirstDriverCell: StringValue{Value: "", FormName: "di_fld177",
				IsReadOnly: false, OtherFormNames: []string{"tr_fld177", "di_fld177", "fld177"},
				MaxLength: 14},
			SecondDriver: StringValue{Value: "", FormName: "di_fld188",
				IsReadOnly: false, OtherFormNames: []string{"fld188", "di_fld188"},
				MaxLength: 18},
			SecondDriverCell: StringValue{Value: "", FormName: "di_fld189",
				IsReadOnly: false, OtherFormNames: []string{"fld189", "di_fld189"},
				MaxLength: 14},
			DispCity:   StringValue{Value: "", FormName: "fld91", IsReadOnly: false, MaxLength: 15},
			DispState:  StringValue{Value: "", FormName: "fld92", IsReadOnly: false, MaxLength: 2},
			Dispatcher: StringValue{Value: "", FormName: "fld176", IsReadOnly: false, MaxLength: 20},
			Trailer: StringValue{Value: "", FormName: "di_fld89",
				IsReadOnly: false, OtherFormNames: []string{"fld89x", "fld89", "di_fld89"},
				MaxLength: 18},
			Truck: StringValue{Value: "", FormName: "di_fld191",
				IsReadOnly: false, OtherFormNames: []string{"di_fld191", "fld191x", "fld191"},
				MaxLength: 18},
			ConfSentDate: StringValue{Value: "08/29/23", FormName: "fld549", IsReadOnly: false, MaxLength: 8},
			ConfSentTime: StringValue{Value: "15:57", FormName: "fld559", IsReadOnly: false, MaxLength: 5},
			ConfRecdDate: StringValue{Value: "", FormName: "fld561", IsReadOnly: false, MaxLength: 8},
			ConfRecdTime: StringValue{Value: "", FormName: "fld562", IsReadOnly: false, MaxLength: 5},
			DispatchedDate: StringValue{Value: "08/29/23", FormName: "di_hdate",
				IsReadOnly: false, OtherFormNames: []string{"hdate", "tr_hdate", "di_hdate"},
				MaxLength: 8},
			DispatchedTime: StringValue{Value: "10:05", FormName: "di_fld53", MaxLength: 5,
				IsReadOnly: false, OtherFormNames: []string{"fld53", "tr_fld53", "di_fld53"}},
			WillPuDate: StringValue{Value: "08/28/23", FormName: "di_wdate",
				IsReadOnly: false, OtherFormNames: []string{"di_wdate", "wdate"},
				MaxLength: 8},
			WillPuTime: StringValue{Value: "", FormName: "di_fld54", MaxLength: 5,
				IsReadOnly: false, OtherFormNames: []string{"di_fld54", "fld54"}},
			ArrivedPuDate: StringValue{Value: "08/28/23", FormName: "di_avdate",
				IsReadOnly: false, OtherFormNames: []string{"avdate", "di_avdate"},
				MaxLength: 8},
			ArrivedPuTime: StringValue{Value: "18:00", FormName: "di_avtime", MaxLength: 5,
				IsReadOnly: false, OtherFormNames: []string{"avtime", "di_avtime"}},
			LoadedDate: StringValue{Value: "08/28/23", FormName: "di_ldate",
				IsReadOnly: false, OtherFormNames: []string{"ldate", "di_ldate"},
				MaxLength: 8},
			LoadedTime: StringValue{Value: "19:00", FormName: "di_ltime", MaxLength: 5,
				IsReadOnly: false, OtherFormNames: []string{"ltime", "di_ltime"}},
			DelEtaDate: StringValue{Value: "09/05/23", FormName: "di_edate",
				IsReadOnly: false, OtherFormNames: []string{"edate", "di_edate", "tr_edate"},
				MaxLength: 8},
			DelEtaTime: StringValue{Value: "11:00", FormName: "di_etime", MaxLength: 5,
				IsReadOnly: false, OtherFormNames: []string{"etime", "di_etime", "tr_etime"}},
			ArrivedConsDate: StringValue{
				Value:          "09/05/23",
				FormName:       "di_addate",
				IsReadOnly:     false,
				MaxLength:      8,
				OtherFormNames: []string{"di_addate", "addate", "tr_addate"}},
			ArrivedConsTime: StringValue{Value: "10:00", FormName: "di_addtime", MaxLength: 5,
				OtherFormNames: []string{"di_addtime", "addtime", "tr_addtime"}},
			DeliveredDate: StringValue{Value: "09/05/23", FormName: "di_fld145", MaxLength: 8,
				OtherFormNames: []string{"fld145", "di_fld145", "im_fld145", "tr_fld145", "af_fld145"}},
			DeliveredTime: StringValue{Value: "11:00", FormName: "di_dtime", MaxLength: 5,
				OtherFormNames: []string{"dtime", "di_dtime", "im_dtime", "af_dtime", "tr_dtime"}},
			SignedBy: StringValue{Value: "RYAN", FormName: "di_fld147", MaxLength: 25,
				OtherFormNames: []string{"fld147", "di_fld147"}},
		},

		Customer: Customer{
			Name:    StringValue{Value: "D C/O N3G", FormName: "s_fld2", IsReadOnly: false, MaxLength: 30},
			Address: StringValue{Value: "123 MAIN ST", FormName: "fld3", IsReadOnly: true, MaxLength: 30},
			City:    StringValue{Value: "CAMDEN", FormName: "s_fld7", IsReadOnly: true, MaxLength: 20},
			State:   StringValue{Value: "NJ", FormName: "fld5", IsReadOnly: true, MaxLength: 2},
			Zip:     StringValue{Value: "08102", FormName: "fld6", IsReadOnly: true, MaxLength: 6},
			Country: StringValue{Value: "", FormName: "fld333", IsReadOnly: true, MaxLength: 20},
			Contact: StringValue{Value: "", FormName: "fld9", IsReadOnly: false, MaxLength: 18},
			Email:   StringValue{Value: "", FormName: "fldp4", IsReadOnly: true, MaxLength: 60},
			Email2:  StringValue{Value: "", FormName: "fldp5", IsReadOnly: true, MaxLength: 60},
			Email3:  StringValue{Value: "", FormName: "fldp6", IsReadOnly: true, MaxLength: 60},
			Email4:  StringValue{Value: "", FormName: "fldp7", IsReadOnly: true, MaxLength: 60},
			Email5:  StringValue{Value: "", FormName: "fldp8", IsReadOnly: true, MaxLength: 60},
			Email6:  StringValue{Value: "", FormName: "fldp9", IsReadOnly: true, MaxLength: 60},
			Phone:   StringValue{Value: "", FormName: "fld8", IsReadOnly: false, MaxLength: 14},
			Ref: StringValue{
				Value:          "DE1",
				FormName:       "fld59",
				IsReadOnly:     false,
				MaxLength:      18,
				OtherFormNames: []string{"s_fld59"}},
		},

		BillTo: BillTo{
			Name:    StringValue{Value: "D C/O N3G", FormName: "s_fld169", IsReadOnly: false, MaxLength: 30},
			Address: StringValue{Value: "123 MAIN ST", FormName: "btaddr", IsReadOnly: true, MaxLength: 30},
			City:    StringValue{Value: "CAMDEN", FormName: "", IsReadOnly: true, MaxLength: 20},
			State:   StringValue{Value: "NJ", FormName: "", IsReadOnly: true, MaxLength: 2},
			Zip:     StringValue{Value: "08102", FormName: "btzip", IsReadOnly: true, MaxLength: 6},
			Country: StringValue{Value: "USA", FormName: "btcountry", IsReadOnly: true, MaxLength: 20},
			Contact: StringValue{Value: "Jane Smith", FormName: "btcontact", IsReadOnly: false, MaxLength: 18},
			Phone:   StringValue{Value: "(*************", FormName: "", IsReadOnly: false, MaxLength: 14},
		},

		PickUp: PickUp{
			Name: StringValue{Value: "D BRANDS",
				FormName: "s_fld30", IsReadOnly: false,
				OtherFormNames: []string{"fld30x", "s_fld30", "fld30"},
				MaxLength:      30,
			},
			Address:       StringValue{Value: "12345 NAME BLVD", FormName: "fld31", IsReadOnly: false, MaxLength: 30},
			Address2:      StringValue{Value: "", FormName: "fld181", IsReadOnly: false, MaxLength: 30},
			City:          StringValue{Value: "MORENO VALLEY", FormName: "fld32", IsReadOnly: false, MaxLength: 15},
			State:         StringValue{Value: "CA", FormName: "fld33", IsReadOnly: false, MaxLength: 2},
			Zip:           StringValue{Value: "92551", FormName: "fld34", IsReadOnly: false, MaxLength: 6},
			Country:       StringValue{Value: "", FormName: "fld334", IsReadOnly: false, MaxLength: 20},
			Contact:       StringValue{Value: "", FormName: "fld35", IsReadOnly: false, MaxLength: 18},
			Email:         StringValue{Value: "", FormName: "fld197", IsReadOnly: false, MaxLength: 60},
			Phone:         StringValue{Value: "", FormName: "fld36", IsReadOnly: false, MaxLength: 14},
			ReadyDate:     StringValue{Value: "08/28/23", FormName: "fld39", IsReadOnly: false, MaxLength: 8},
			ReadyTime:     StringValue{Value: "redacted", FormName: "fld14", IsReadOnly: false, MaxLength: 8},
			ApptDate:      StringValue{Value: "08/28/23", FormName: "fld185", IsReadOnly: false, MaxLength: 8},
			ApptNote:      StringValue{Value: "", FormName: "fld187", IsReadOnly: false, MaxLength: 19},
			ApptStartTime: StringValue{Value: "18:00", FormName: "fld186", IsReadOnly: false, MaxLength: 8},
			BusinessHours: StringValue{Value: "", FormName: "fld15", IsReadOnly: false, MaxLength: 9},
			Ref:           StringValue{Value: "2071818", FormName: "fld155", IsReadOnly: false, MaxLength: 18},
		},

		Consignee: Consignee{
			Name: StringValue{
				Value: "DSOL", FormName: "fld29",
				IsReadOnly: false, OtherFormNames: []string{"fld29x", "s_fld29"},
				MaxLength: 30},
			Address: StringValue{Value: "12345 NE 66TH STREET", FormName: "fld60",
				IsReadOnly: false, MaxLength: 30},
			Address2:      StringValue{Value: "SUITE 100", FormName: "fld182", IsReadOnly: false, MaxLength: 30},
			State:         StringValue{Value: "WA", FormName: "fld41", IsReadOnly: false, MaxLength: 2},
			Zip:           StringValue{Value: "98662", FormName: "fld70", IsReadOnly: false, MaxLength: 6},
			Country:       StringValue{Value: "", FormName: "fld335", IsReadOnly: false, MaxLength: 20},
			Appt:          StringValue{Value: "09/05/23", FormName: "fld150", IsReadOnly: false, MaxLength: 8},
			ApptNote:      StringValue{Value: "", FormName: "fld152", IsReadOnly: false, MaxLength: 19},
			ApptStartTime: StringValue{Value: "11:00", FormName: "fld151", IsReadOnly: false, MaxLength: 8},
			City:          StringValue{Value: "VANCOUVER", FormName: "fld10", IsReadOnly: false, MaxLength: 20},
			Contact:       StringValue{Value: "ALICE TEST", FormName: "fld43", IsReadOnly: false, MaxLength: 18},
			Email:         StringValue{Value: "<EMAIL>", FormName: "fld198", IsReadOnly: false, MaxLength: 60},
			BusinessHours: StringValue{Value: "redacted", FormName: "fld42", IsReadOnly: false, MaxLength: 9},
			MustDeliver:   StringValue{Value: "", FormName: "fld602", IsReadOnly: false, MaxLength: 8},
			Phone:         StringValue{Value: "(*************", FormName: "fld58", IsReadOnly: false, MaxLength: 14},
			Ref:           StringValue{Value: "QF1111", FormName: "fld156", IsReadOnly: false, MaxLength: 18},
		},
		RateData: RateData{
			// Customer rate info
			CustomerRateType:       StringValue{Value: "Flat Rate", FormName: "fld23", IsReadOnly: false},
			CustomerNumHours:       FloatValue{Value: 15, FormName: "fld526", IsReadOnly: false},
			CarrierLHRateUSD:       FloatValue{Value: 2595, FormName: "payrate", IsReadOnly: false},
			CustomerLHRateUSD:      FloatValue{Value: 1800, FormName: "fld24", IsReadOnly: false},
			CustomerLineHaulCharge: FloatValue{Value: 0, FormName: "fld25", IsReadOnly: false},
			CustomerTotalCost:      FloatValue{Value: 1800, FormName: "t4", IsReadOnly: true},

			// Carrier rate info
			CarrierRateType: StringValue{
				Value:          "All In",
				FormName:       "paytype",
				OtherFormNames: []string{"paytype", "fld102"},
			},
			CarrierNumHours:       FloatValue{Value: 10.2, FormName: "fld527", IsReadOnly: false},
			CarrierLineHaulCharge: FloatValue{Value: 2595, FormName: "paynet", IsReadOnly: true},
			CarrierTotalCost:      FloatValue{Value: 2595, FormName: "t3", IsReadOnly: true},

			// Fuel surcharge info
			Fsc:     FloatValue{Value: 0, FormName: "fld664", IsReadOnly: false},
			FscMile: FloatValue{Value: 0, FormName: "fld665", IsReadOnly: false},
			MaxRate: FloatValue{Value: 1600, FormName: "fld604", IsReadOnly: false},

			// Net profit info
			NetProfitUSD:     FloatValue{Value: -795, FormName: "fldp0", IsReadOnly: false},
			NetProfitPercent: FloatValue{Value: -44.17, FormName: "fldp1", IsReadOnly: false},
		},
		Specifications: Specifications{
			MinTempFahrenheit:     FloatValue{Value: -3, FormName: "fld95", IsReadOnly: false},
			MaxTempFahrenheit:     FloatValue{Value: 32, FormName: "fldextra2", IsReadOnly: false},
			CustomerDistanceMiles: FloatValue{Value: 1234, FormName: "fld88", IsReadOnly: false},
		},
		Notes: []Note{{
			CreatedAt: StringValue{Value: "redacted", FormName: "", IsReadOnly: false},
			UpdatedBy: StringValue{Value: "redacted", FormName: "", IsReadOnly: false},
			Note:      StringValue{Value: "Sent crr email for", FormName: "", IsReadOnly: false},
		}, {
			CreatedAt: StringValue{Value: "redacted", FormName: "", IsReadOnly: false},
			UpdatedBy: StringValue{Value: "redacted", FormName: "", IsReadOnly: false},
			Note:      StringValue{Value: "UNABLE TO REACH, SENT EMAIL", FormName: "", IsReadOnly: false},
		}, {
			CreatedAt: StringValue{Value: "redacted", FormName: "", IsReadOnly: false},
			UpdatedBy: StringValue{Value: "EDI", FormName: "", IsReadOnly: false},
			Note:      StringValue{Value: "DESTINATION", FormName: "", IsReadOnly: false},
		}},
	}

	assert.Equal(t, expectedLoad, actualLoad)
}

type mockHTTPClient struct {
	calls []string
}

func (c *mockHTTPClient) Do(req *http.Request) (*http.Response, error) {
	c.calls = append(c.calls, fmt.Sprintf("Do(prcnam=%s)", req.URL.Query().Get("prcnam")))

	html := `<html>
	<body>
		<input name="doassrep" id="doassrep" type="hidden" value="%s" style="">
	</body>
</html>`

	pro := req.URL.Query().Get("pro")
	switch pro {
	case "1234":
		return &http.Response{
			StatusCode: http.StatusOK,
			Body:       io.NopCloser(strings.NewReader(fmt.Sprintf(html, "Y"))),
		}, nil

	case "4567":
		return &http.Response{
			StatusCode: http.StatusOK,
			Body:       io.NopCloser(strings.NewReader(fmt.Sprintf(html, "N"))),
		}, nil
	}

	return nil, errors.New("unexpected PRO: " + pro)
}

// Case when there's no operator assigned so there's no remove icon, but can assign on Smart Search page
func TestParseLoadOperatorAssignable(t *testing.T) {
	ctx := context.Background()

	// No operator assigned in HTML
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(`<html>
    <body>
        <tr>
            <td nowrap="" align="right">Disp Assigned</td>
                    <td align="left" nowrap=""><input type="text" readonly=""
					style="background-color: rgb(232, 232, 232); display: inline;"
					name="fld62" id="fld62" value="" size="8" maxlength="8" class="twin" autocomplete="off">
            <img id="rmvrep" class="unassrep" src="/images/vision/btn_minus.png" style="display:none;">
                    </td>

                    <td align="right">&nbsp; Disp Actual </td>
            <td>
            <input type="text" id="fld63" name="fld63" value="" size="8" maxlength="8" class="twin"
			style="display: inline;" autocomplete="off">
            </td>
            </tr>    </body>
</html>`))
	require.NoError(t, err)

	// Init empty client. Don't
	client := &Aljex{}
	mock := &mockHTTPClient{}
	client.httpClient = mock
	client.creds = &Credentials{}
	client.config = &Config{}
	client.tms = aljexTestClient(ctx, env.AppID, env.Password, env.TwoFactorSecret)

	load := client.parseLoadHTML(ctx, doc, "1234")

	assert.False(t, load.Operator.IsReadOnly)
	assert.Equal(t, []string{"Do(prcnam=ssweb)"}, mock.calls)
}

// Case when there's an operator that can be removed
func TestParseLoadOperatorRemovable(t *testing.T) {
	ctx := context.Background()
	file, err := os.Open("./testHTML/pro_sample.html")
	require.NoError(t, err)
	defer file.Close()

	// Init Aljex and mock HTTP client.
	client := &Aljex{}
	mock := &mockHTTPClient{}
	client.httpClient = mock
	client.creds = &Credentials{}
	client.config = &Config{}
	client.tms = aljexTestClient(ctx, env.AppID, env.Password, env.TwoFactorSecret)

	doc, err := goquery.NewDocumentFromReader(strings.NewReader(`<html>
    <body>
        <tr>
            <td nowrap="" align="right">Disp Assigned</td>
                    <td align="left" nowrap=""><input type="text" readonly=""
					style="background-color: rgb(232, 232, 232); display: inline;"
					name="fld62" id="fld62" value="JQUELIN" size="8" maxlength="8" class="twin" autocomplete="off">
            <img id="rmvrep" class="unassrep" src="/images/vision/btn_minus.png">
                    </td>

                    <td align="right">&nbsp; Disp Actual </td>
            <td>
            <input type="text" id="fld63" name="fld63" value="" size="8" maxlength="8"
			class="twin" style="display: inline;" autocomplete="off">
            </td>
            </tr>    </body>
</html>`))
	require.NoError(t, err)

	load := client.parseLoadHTML(ctx, doc, "1234")

	assert.False(t, load.Operator.IsReadOnly)
	assert.Empty(t, mock.calls)
}

// Case when there's already an operator and it can't be re-assigned (observed cases include when the PRO status
// is HOLD, COVERED, or DELIVERED)
func TestParseLoadOperatorReadOnly(t *testing.T) {
	ctx := context.Background()

	doc, err := goquery.NewDocumentFromReader(strings.NewReader(`<html>
    <body>
        <tr>
            <td nowrap="" align="right">Disp Assigned</td>
                    <td align="left" nowrap=""><input type="text" readonly=""
					style="background-color: rgb(232, 232, 232); display: inline;"
					name="fld62" id="fld62" value="JQUELIN" size="8" maxlength="8" class="twin" autocomplete="off">
            <img id="rmvrep" class="unassrep" src="/images/vision/btn_minus.png" style="display: none">
                    </td>

                    <td align="right">&nbsp; Disp Actual </td>
            <td>
            <input type="text" id="fld63" name="fld63" value="" size="8" maxlength="8"
			class="twin" style="display: inline;" autocomplete="off">
            </td>
            </tr>    </body>
</html>`))
	require.NoError(t, err)

	// Init Aljex and mock HTTP client.
	client := &Aljex{}
	mock := &mockHTTPClient{}
	client.httpClient = mock
	client.creds = &Credentials{}
	client.config = &Config{}
	client.tms = aljexTestClient(ctx, env.AppID, env.Password, env.TwoFactorSecret)

	load := client.parseLoadHTML(ctx, doc, "4567")

	assert.True(t, load.Operator.IsReadOnly)
	assert.Equal(t, []string{"Do(prcnam=ssweb)"}, mock.calls)
}

func TestGetLoadByIDType(t *testing.T) {
	ctx := context.Background()
	t.Run("No loads found", func(t *testing.T) {
		html, err := os.ReadFile("./testHTML/no_loads_found.html")
		require.NoError(t, err)

		proNums, err := parseSearchResults(ctx, html)
		require.NoError(t, err)
		assert.Len(t, proNums, 0)
	})

	t.Run("1 load found", func(t *testing.T) {
		html, err := os.ReadFile("./testHTML/1_load_found.html")
		require.NoError(t, err)

		proNums, err := parseSearchResults(ctx, html)
		require.NoError(t, err)
		assert.ElementsMatch(t, proNums, []string{"2153447"})
	})

	t.Run("3 loads found", func(t *testing.T) {
		html, err := os.ReadFile("./testHTML/3_loads_found.html")
		require.NoError(t, err)

		proNums, err := parseSearchResults(ctx, html)
		require.NoError(t, err)
		assert.ElementsMatch(t, proNums, []string{"2153447", "2153477", "2151741"})
	})
}

func loadEnv() error {
	if err := godotenv.Load(); err != nil {
		log.Warnf("unable to load .env file: %s", err)
	}

	if err := envconfig.Process("", &env); err != nil {
		return fmt.Errorf("failed to parse env vars: %w", err)
	}

	return nil
}

// TODO: live test a completed PRO so we'll know in real-time if the structure of the page has changed
// and our scraper is out of date. Unit tests are using a static HTML file.
