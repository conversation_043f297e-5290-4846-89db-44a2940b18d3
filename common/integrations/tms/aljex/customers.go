package aljex

import (
	"context"
	"fmt"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	tmsCustomerDB "github.com/drumkitai/drumkit/common/rds/tms_customers"
	"github.com/drumkitai/drumkit/common/redis"
)

func (a *Aljex) GetCustomers(ctx context.Context) (customers []models.TMSCustomer, err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "GetCustomersAljex", otel.IntegrationAttrs(a.tms))
	defer func() { metaSpan.End(err) }()

	log.Info(ctx, "aljex.GetCustomers")

	allCharacters := []string{
		"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M",
		"N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z",
		"0", "1", "2", "3", "4", "5", "6", "7", "8", "9",
	}

	var allCustomers []models.TMSCustomer
	var failedCharacters []string
	charactersKey := fmt.Sprintf("integration-id-%d-%s", a.tms.ID, redis.CustomerJob)
	failedCharactersKey := fmt.Sprintf("integration-id-%d-%s-failed", a.tms.ID, redis.CustomerJob)

	// Check if this is a retry request from context
	isRetry := ctx.Value("retry") != nil
	var charactersToProcess []string

	if isRetry {
		// This is a retry - only process failed characters from Redis
		failedCharactersStr, _, err := redis.GetKey[string](ctx, failedCharactersKey)
		if err != nil || failedCharactersStr == "" {
			log.Info(ctx, "No failed characters found in Redis for retry")
			return []models.TMSCustomer{}, nil
		}
		charactersToProcess = strings.Split(failedCharactersStr, ",")
		log.Info(ctx, "Retrying failed characters", zap.Strings("failedCharacters", charactersToProcess))
	} else {
		// Normal run - process all characters
		charactersToProcess = allCharacters
		log.Info(ctx, "Processing all characters (A-Z, 0-9)")
	}

	for i, character := range charactersToProcess {
		log.Info(ctx, "Fetching customers starting with character",
			zap.String("character", character),
			zap.Int("progress", i+1),
			zap.Int("total", len(charactersToProcess)),
		)

		characterCustomers, err := a.searchCustomersByCharacter(ctx, character)
		if err != nil {
			log.Error(ctx, "Failed to fetch customers for character",
				zap.Error(err),
				zap.String("character", character),
			)
			failedCharacters = append(failedCharacters, character)
			continue
		}

		if len(characterCustomers) > 0 {
			log.Info(ctx, "Found customers for character",
				zap.String("character", character),
				zap.Int("count", len(characterCustomers)),
			)

			// Upsert customers to database in batches
			customersToRefresh := &characterCustomers
			if err = tmsCustomerDB.RefreshTMSCustomers(ctx, customersToRefresh); err != nil {
				log.Error(ctx, "Failed to upsert customers to database",
					zap.Error(err),
					zap.String("character", character),
				)
				failedCharacters = append(failedCharacters, character)
				continue
			}

			allCustomers = append(allCustomers, characterCustomers...)
		} else {
			log.Debug(ctx, "No customers found for character", zap.String("character", character))
		}
	}

	// Handle failed characters in Redis
	if len(failedCharacters) > 0 {
		// Store failed characters for next retry
		failedCharactersStr := strings.Join(failedCharacters, ",")
		if err := redis.SetKey(ctx, failedCharactersKey, failedCharactersStr, 24*time.Hour); err != nil {
			log.Error(ctx, "failed to save failed characters to Redis", zap.Error(err))
		} else {
			log.Info(ctx, "Saved failed characters for retry", zap.Strings("failedCharacters", failedCharacters))
		}
	} else {
		if err := redis.DeleteKey(ctx, charactersKey); err != nil {
			log.Warn(ctx, "failed to delete charactersKey from Redis", zap.Error(err))
		}
		if err := redis.DeleteKey(ctx, failedCharactersKey); err != nil {
			log.Warn(ctx, "failed to delete failedCharactersKey from Redis", zap.Error(err))
		}
		log.Info(ctx, "Successfully cleared Redis state - job completed")
	}

	log.Info(ctx, "aljex.GetCustomers completed", zap.Int("totalCustomers", len(allCustomers)))

	return allCustomers, nil
}
