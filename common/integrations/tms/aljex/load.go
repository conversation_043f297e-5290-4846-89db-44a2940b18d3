package aljex

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/PuerkitoBio/goquery"
	"go.opentelemetry.io/otel/attribute"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	tmsutil "github.com/drumkitai/drumkit/common/integrations/tms/util"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

type (
	// Map representation of LoadData{} for JSON marshaling/unmarshaling
	rawFreightData struct {
		Status         FormAttributes `json:"status"`
		OutPalletCount FormAttributes `json:"outPalletCount"`
		Operator       FormAttributes `json:"operator"`

		// All PO numbers will be flattened into a single CSV string
		PONums FormAttributes `json:"poNums"`

		AdditionalReferences []FormAttributes `json:"additionalReferences"`

		Carrier   map[string]FormAttributes `json:"carrier"`
		Customer  map[string]FormAttributes `json:"customer"`
		BillTo    map[string]FormAttributes `json:"billTo"`
		PickUp    map[string]FormAttributes `json:"pickUp"`
		Consignee map[string]FormAttributes `json:"consignee"`
		RateData  map[string]FormAttributes `json:"rateData"`
	}

	FormAttributes = StringValue
)

func (a *Aljex) GetLoadIDs(ctx context.Context, query models.SearchLoadsQuery) (res []string, err error) {
	// If needed, we can add support for status, transportType, pickup/dropoff state by adding them to the params
	// but for now, we don't support them so we log a warning and fail-open
	if query.Status != "" || query.TransportType != "" ||
		query.Pickup != (models.Address{}) || query.Dropoff != (models.Address{}) {

		log.Warn(
			ctx,
			"searching by status, transportType, pickup, or dropoff not yet supported for Aljex, please check usage",
			zap.Any("query", query),
		)
	}

	// Aljex table limits to 2000 results so we filter by date range
	// NOTE: Aljex caches search params, so every param MUST be set or a previous search executed by another web/API
	// client will influence results.
	// For example, if Sophie logged into web app and searched by customer, and this code
	// does not explicitly set customer to "", then Aljex will implicitly filter by customer when Drumkit searches.
	params := url.Values{}
	params.Set("ctlrec", "")
	params.Set("ctlval", "")
	params.Set("qual", a.creds.Qual)
	params.Set("sys", "3a")
	params.Set("name", a.creds.Name)
	params.Set("c_tok", a.creds.Token)
	params.Set("type", "login")
	params.Set("pagename", "Default")
	params.Set("prcnam", "t3alogin22")
	params.Set("filtershow", "1")
	params.Set("showmap", "1")
	params.Set("sptstate", "ALL")
	params.Set("spttstate", "ALL")
	params.Set("pagen", "Default")
	params.Set("seln", "00000000")
	params.Set("input", "")
	params.Set("sortit", "")
	params.Set("restore", "")

	// Load statuses
	// `selXX` are hidden inputs for checkbox's value; 1 indicates box is *not* checked (counterintuitive, I know)
	params.Set("box40", "on")  // Status open = true
	params.Set("sel40", "0")   // Status open = true
	params.Set("sel42", "0")   // Status assigned = true
	params.Set("sel41", "0")   // Status dispatched = true
	params.Set("sel44", "0")   // Status covered = true
	params.Set("sel80", "0")   // Status At PU = true
	params.Set("sel43", "0")   // Status Loaded = true
	params.Set("sel81", "0")   // Status At Consignee = true
	params.Set("sel45", "0")   // Status Delivered = true
	params.Set("sel47", "0")   // Status TORD = true
	params.Set("sel46", "0")   // Status Hold = true
	params.Set("box119", "on") // ¯\_(ツ)_/¯ , "tonu", commented out in HTML but in request body so we keep it
	params.Set("sel119", "0")  // same as box119

	// Modes
	params.Set("sel35", "0") // Brokerage mode = true, derived from box35
	params.Set("sel36", "0") // Trucking mode = true, derived from box36
	params.Set("sel37", "0") // Logistics mode = true, derived from box37
	params.Set("sel38", "1") // Air freight = false
	params.Set("sel39", "1") // Intermodal = false
	params.Set("sel83", "1") // Spot loads = false // QN should this be true or false?

	params.Set("sel90", "1")  // Shipment ticket = false // QN can I exclude these params cuz I want both true and false
	params.Set("sel91", "1")  // Exception Missed Pickup = false
	params.Set("sel102", "1") // Waiting for Signed Confirmation = false
	params.Set("fradius", "0")
	params.Set("tradius", "0")

	// Time
	// NOTE: Aljex supports searching by "Ship Date", "PU Appt", "Del Appt".
	// We only support "PU Appt" for now. If a load doesn't have a PU date but "Load Date" falls in that window,
	// Aljex includes it in results.
	// If to and from date are the same, it will return loads with on that date, not an empty list.
	params.Set("puappdate", helpers.Ternary(query.FromDate.Valid, query.FromDate.Time.Format("01/02/06"), ""))
	params.Set("topuappdate", helpers.Ternary(query.ToDate.Valid, query.ToDate.Time.Format("01/02/06"), ""))

	// By ID
	params.Set("frompro", helpers.Ternary(query.FromFreightTrackingID != "", query.FromFreightTrackingID, ""))
	params.Set("topro", "")

	// Specs
	params.Set("sel71", "1") // Hazmat
	params.Set("sel72", "1") // Temp control
	params.Set("sel70", "1") // Post load boards
	params.Set("sel74", "1") // Customs bonded

	// Location filters (using available Pickup/Dropoff fields)
	params.Set("pname", query.Pickup.City)
	params.Set("pstate", query.Pickup.State)
	params.Set("pzip", query.Pickup.Zip)
	params.Set("cname", query.Dropoff.City)
	params.Set("cstate", query.Dropoff.State)
	params.Set("czip", query.Dropoff.Zip)

	// Additional parameters from raw query string that are not currently set
	params.Set("customername", "") // Customer name filter
	params.Set("customerid", "")   // Customer ID filter
	params.Set("carriername", "")  // Carrier name filter
	params.Set("carrierid", "")    // Carrier ID filter
	params.Set("manifest", "")     // Manifest filter
	params.Set("custref", "")      // Customer reference filter
	params.Set("trailer", "")      // Trailer filter
	params.Set("freightmix", "")   // Freight mix filter
	params.Set("servrep", "")      // Service rep filter
	params.Set("delappdate", "")   // Delivery appointment date
	params.Set("todelappdate", "") // To delivery appointment date
	params.Set("rdydate", "")      // Ready date
	params.Set("tordydate", "")    // To ready date
	params.Set("assdisp", "")
	params.Set("actdisp", "")
	params.Set("salesy", "")
	params.Set("office", "")
	params.Set("revtype", "")
	params.Set("equtype", "")
	params.Set("busunit", "")
	params.Set("frstate", "")
	params.Set("tostate", "")
	params.Set("fzone", "")
	params.Set("tzone", "")
	params.Set("toggle", "")
	params.Set("newmultis", "")
	params.Set("oldpage", "")
	params.Set("refresh", "Y")

	respBody, _, err := a.postForm(ctx, a.getAPIURL(), strings.NewReader(params.Encode()), true, s3backup.TypeLoads)
	if err != nil {
		return nil, err
	}

	document, err := goquery.NewDocumentFromReader(bytes.NewReader(respBody))
	if err != nil {
		return nil, fmt.Errorf("goquery error: %w", err)
	}

	table := document.Find("#datagridtable")
	if table.Length() == 0 {
		return nil, errors.New("no datagridtable found in response")
	}

	trs := table.Find("tr .lo")
	if trs.Length() == 0 {
		return nil, errors.New("no trs found in datagridtable")
	}

	trs.Each(func(_ int, tr *goquery.Selection) {
		pro := strings.TrimSpace(tr.Find("td").Eq(1).Find("a").Text())
		pro = extractPRO(pro)

		if strings.TrimSpace(pro) == "" {
			log.Info(ctx, "parsed empty pro, check regex", zap.Any("tr", tr.Text()))
			return
		}

		if query.FromFreightTrackingID != "" {
			if pro >= query.FromFreightTrackingID {
				res = append(res, pro)
			}
		} else {
			res = append(res, pro)
		}
	})

	return res, nil
}

func (a *Aljex) GetLoad(ctx context.Context, freightTrackingID string) (res models.Load,
	attrs models.LoadAttributes, err error) {
	spanAttrs := append(otel.IntegrationAttrs(a.tms),
		attribute.String("freight_tracking_id", freightTrackingID))
	ctx, metaSpan := otel.StartSpan(ctx, "GetLoadAljex", spanAttrs)
	defer func() { metaSpan.End(err) }()

	log.Info(ctx, "aljex.GetLoad")

	doc, err := a.getLoad(ctx, freightTrackingID)
	if err != nil {
		return res, attrs, fmt.Errorf("error loading PRO page in GetLoad: %w", err)
	}

	aljexLoad := a.parseLoadHTML(ctx, doc, freightTrackingID)
	load := aljexLoad.ToLoadModel(ctx, freightTrackingID, a.tms.ServiceID, a.tms.ID)
	attrs = aljexLoad.ToLoadAttributes()

	return *load, attrs, err
}

func (a *Aljex) GetDefaultLoadAttributes() models.LoadAttributes {
	attrs := DefaultLoadAttributes
	tmsutil.ApplyTMSFeatureFlags(&a.tms, &attrs)

	return attrs
}

func (a *Aljex) CreateLoad(ctx context.Context, load models.Load, _ *models.TMSUser) (result models.Load, err error) {
	spanAttrs := append(otel.IntegrationAttrs(a.tms), otel.LoadAttrs(load)...)
	ctx, metaSpan := otel.StartSpan(ctx, "CreateLoadAljex", spanAttrs)
	defer func() { metaSpan.End(err) }()

	log.Info(ctx, "aljex.CreateLoad")

	// Truncate all time values to the minute since that's how many significant digits Aljex returns
	carrier := &load.Carrier
	carrier.DispatchedTime.Time = carrier.DispatchedTime.Time.Truncate(time.Minute)
	carrier.ConfirmationSentTime.Time = carrier.ConfirmationSentTime.Time.Truncate(time.Minute)
	carrier.ConfirmationReceivedTime.Time = carrier.ConfirmationReceivedTime.Time.Truncate(time.Minute)
	carrier.ExpectedPickupTime.Time = carrier.ExpectedPickupTime.Time.Truncate(time.Minute)
	carrier.PickupStart.Time = carrier.PickupStart.Time.Truncate(time.Minute)
	carrier.PickupEnd.Time = carrier.PickupEnd.Time.Truncate(time.Minute)
	carrier.ExpectedDeliveryTime.Time = carrier.ExpectedDeliveryTime.Time.Truncate(time.Minute)
	carrier.DeliveryStart.Time = carrier.DeliveryStart.Time.Truncate(time.Minute)
	carrier.DeliveryEnd.Time = carrier.DeliveryEnd.Time.Truncate(time.Minute)

	doc, err := a.getNewLoadForm(ctx)
	if err != nil {
		return result, fmt.Errorf("error loading new load form: %w", err)
	}

	formData := url.Values{}

	formData.Set("pro", "")
	formData.Set("qual", a.creds.Qual)
	formData.Set("type", "save")
	formData.Set("prcnam", "t3atagexa")
	formData.Set("name", a.creds.Name)
	formData.Set("c_tok", a.creds.Token)
	formData.Set("sys", "3a")
	formData.Set("company", a.creds.Company)
	formData.Set("ctlrec", "")
	formData.Set("ctlval", "")
	formData.Set("reprn", "")
	formData.Set("no_triplegs", "")
	formData.Set("no_tripleg_browse", "Y")
	formData.Set("refresh", "Y")

	allInputs := doc.Find("form[name='main'] input, textarea")

	// i've used a for loop here instead of .Each()
	// because allInputs.Each() is always = nil for
	// some reason. my guess is that the create load
	// form is entirely empty so all inputs ARE empty initially. hence the loop instead of .Each()
	for i := 0; i < allInputs.Length(); i++ {
		input := allInputs.Eq(i)
		inputName, nameExists := input.Attr("name")
		value := input.AttrOr("value", input.Text())

		// Preserve existing form values as baseline, but skip critical fields
		if nameExists && value != "" && inputName != "pro" && inputName != "type" && inputName != "prcnam" {
			formData.Add(inputName, value)
		}
	}

	aljexData := a.ToAljexData(ctx, load)
	loadDataMap, err := toStructMap(aljexData)
	if err != nil {
		return result, fmt.Errorf("error converting load to structured data: %w", err)
	}

	// Since this is creation, we don't have existing metadata, so create empty metadata for addDataToForm
	emptyMetadata := &rawFreightData{
		Customer:  make(map[string]FormAttributes),
		BillTo:    make(map[string]FormAttributes),
		PickUp:    make(map[string]FormAttributes),
		Consignee: make(map[string]FormAttributes),
		Carrier:   make(map[string]FormAttributes),
		RateData:  make(map[string]FormAttributes),
	}

	// Set the customer's external TMS ID to enable bill-to auto-population in Aljex
	err = a.setCustomerID(ctx, &formData, load)
	if err != nil {
		log.WarnNoSentry(ctx, "error setting customer ID for bill-to auto-population", zap.Error(err))
	}

	// added a new param to function (isCreateMode).
	// if isCreateMode = true, all fields are considered writable regardless of metadata read-only status
	// for UdpateLoad, we use isCreateMode = false
	// which respects readOnly fields
	// to preven conflicts by updating only writable fields
	addDataToForm(ctx, &formData, loadDataMap.Customer, emptyMetadata.Customer, true)
	addDataToForm(ctx, &formData, loadDataMap.BillTo, emptyMetadata.BillTo, true)
	addDataToForm(ctx, &formData, loadDataMap.PickUp, emptyMetadata.PickUp, true)
	addDataToForm(ctx, &formData, loadDataMap.Consignee, emptyMetadata.Consignee, true)
	addDataToForm(ctx, &formData, loadDataMap.Carrier, emptyMetadata.Carrier, true)
	addDataToForm(ctx, &formData, loadDataMap.RateData, emptyMetadata.RateData, true)

	// If FSC fields are nil, remove them from form data to avoid sending "0" to Aljex
	if load.RateData.FSCPercent == nil || *load.RateData.FSCPercent == 0 {
		formData.Del("fld664")
	}
	if load.RateData.FSCPerMile == nil || *load.RateData.FSCPerMile == 0 {
		formData.Del("fld665")
	}

	formData.Set("fld115", "BROKERAGE")

	if aljexData.RateData.CarrierRateType.Value != "" {
		formData.Set("paytype", aljexData.RateData.CarrierRateType.Value)
		formData.Set("fld102", aljexData.RateData.CarrierRateType.Value)
	}

	// Map summary fields
	formData.Set(aljexData.Description.FormName, aljexData.Description.Value)

	if aljexData.TotalPieces.Value > 0 {
		formData.Set(aljexData.TotalPieces.FormName, fmt.Sprintf("%.0f", aljexData.TotalPieces.Value))
	}
	if aljexData.TotalWeight.Value > 0 {
		formData.Set(aljexData.TotalWeight.FormName, fmt.Sprintf("%.0f", aljexData.TotalWeight.Value))
	}

	// Map equipment type
	if aljexData.EquipmentType.Value != "" {
		formData.Set(aljexData.EquipmentType.FormName, aljexData.EquipmentType.Value)
	}

	if aljexData.SpecialInstructions.Value != "" {
		formData.Set(aljexData.SpecialInstructions.FormName, aljexData.SpecialInstructions.Value)
	}

	// Map individual commodity items
	populateCommoditiesInForm(&formData, aljexData.Commodities)

	populateAdditionalReferencesInForm(&formData, aljexData.AdditionalReferences)

	resp, _, err := a.postForm(ctx, a.getAPIURL(), strings.NewReader(formData.Encode()), true, s3backup.TypeLoads)
	if err != nil {
		return result, errtypes.NewUserFacingError(err, cleanErrorMessage)
	}

	// now we get the loadID that was just created
	doc, err = goquery.NewDocumentFromReader(bytes.NewReader(resp))
	if err != nil {
		return result, errtypes.WrapNewUserFacingError(
			"Create load succeeded but failed to get new load's ID, please check Aljex", err, cleanErrorMessage)
	}

	// a new route.php is fetched after redirection when a new load has been created on Aljex
	loadID, _ := doc.Find("form[name='web'] input[name='pro']").Attr("value")
	if loadID == "" {
		err = errors.New("failed to get new load ID from Aljex")

		return result, errtypes.WrapNewUserFacingError(
			"Create load succeeded but failed to get new load's ID, please check Aljex", err)
	}

	result, _, err = a.GetLoad(ctx, loadID)
	if err != nil {
		return result, errtypes.WrapNewUserFacingError(
			"Create load succeeded but failed to get new load's ID, please check Aljex", err, cleanErrorMessage)
	}

	return result, nil
}

func (a *Aljex) UpdateLoad(
	ctx context.Context,
	curLoad *models.Load,
	updatedLoad *models.Load,
) (res models.Load, attrs models.LoadAttributes, err error) {
	spanAttrs := append(otel.IntegrationAttrs(a.tms), otel.LoadAttrs(*updatedLoad)...)
	ctx, metaSpan := otel.StartSpan(ctx, "UpdateLoadAljex", spanAttrs)
	defer func() { metaSpan.End(err) }()

	log.Info(ctx, "aljex.UpdateLoad")

	// Truncate all time values to the minute since that's how many significant digits Aljex returns
	carrier := &updatedLoad.Carrier
	carrier.DispatchedTime.Time = carrier.DispatchedTime.Time.Truncate(time.Minute)
	carrier.ConfirmationSentTime.Time = carrier.ConfirmationSentTime.Time.Truncate(time.Minute)
	carrier.ConfirmationReceivedTime.Time = carrier.ConfirmationReceivedTime.Time.Truncate(time.Minute)
	carrier.ConfirmationReceivedTime.Time = carrier.ConfirmationReceivedTime.Time.Truncate(time.Minute)
	carrier.ExpectedPickupTime.Time = carrier.ExpectedPickupTime.Time.Truncate(time.Minute)
	carrier.PickupStart.Time = carrier.PickupStart.Time.Truncate(time.Minute)
	carrier.PickupEnd.Time = carrier.PickupEnd.Time.Truncate(time.Minute)
	carrier.ExpectedDeliveryTime.Time = carrier.ExpectedDeliveryTime.Time.Truncate(time.Minute)
	carrier.DeliveryStart.Time = carrier.DeliveryStart.Time.Truncate(time.Minute)
	carrier.DeliveryEnd.Time = carrier.DeliveryEnd.Time.Truncate(time.Minute)

	doc, err := a.getLoad(ctx, curLoad.FreightTrackingID)
	if err != nil {
		return res, attrs, fmt.Errorf("error loading PRO page in GetLoad: %w", err)
	}

	formMetadata := a.parseLoadHTML(ctx, doc, curLoad.FreightTrackingID)
	metadataMap, err := toStructMap(formMetadata)
	if err != nil {
		return res, attrs, fmt.Errorf("error converting metadata to rawFreightData: %w", err)
	}

	formData := url.Values{}

	// There are many more fields than those in models.Load{} (600+), so first add those to POST req
	allInputs := doc.Find("form[name='main'] input, textarea")

	// Settle in for a story, kids: Carrier LH Rate USD is referenced by both "fld103" and "payrate" form names.
	// But for some reason, "fld103" is always empty in the form, even though 1) it isn't in the browser source code
	// and 2) payrate isn't empty. My hypothesis is that there's a JS func that runs after the page loads
	// to set fld103 := payrate. This behavior causes the aljex.UpdateLoad() to incorrectly overwrite that field to 0.
	//
	// TL;DR fld103 is the form name actually required for the update, so to fix the issue,
	// we manually set it to the value of "payrate".
	var carrierLHRateUSD string
	allInputs.Each(func(_ int, input *goquery.Selection) {
		inputName, nameExists := input.Attr("name")
		value := input.AttrOr("value", input.Text())

		if inputName == "payrate" {
			log.Info(ctx, "found payrate field, setting fld103", zap.String("val", value))
			carrierLHRateUSD = value
		}

		// This will also preserve read-only fields, which we skip in addDataToForm
		if nameExists {
			formData.Add(inputName, value)
		}
	})
	formData.Set("fld103", carrierLHRateUSD)
	formData.Set("fld102", formMetadata.RateData.CarrierRateType.Value)

	// Main form includes repeated values so use Set not Add to uniquely define required keys
	formData.Set("pro", curLoad.FreightTrackingID)
	formData.Set("prcnam", "t3atagexa")
	formData.Set("type", "save")
	formData.Set("qual", a.creds.Qual)
	formData.Set("name", a.creds.Name)
	formData.Set("company", a.creds.Company) // NOTE: this is actually empty, but doesn't impact result
	formData.Set("c_tok", a.creds.Token)
	formData.Set("sys", "3a")
	formData.Set("ctlrec", "")

	updatedAljex := a.ToAljexData(ctx, *updatedLoad)

	updatesMap, err := toStructMap(updatedAljex)
	if err != nil {
		return res, attrs, errors.New("error converting updated load to rawFreightData")
	}

	// Status is automatically updated, and editing PONums & rate data is not supported right now
	if !formMetadata.Operator.IsReadOnly {
		formData.Set("fld62", updatedLoad.Operator)
	}
	addDataToForm(ctx, &formData, updatesMap.Customer, metadataMap.Customer, false)
	addDataToForm(ctx, &formData, updatesMap.BillTo, metadataMap.BillTo, false)
	addDataToForm(ctx, &formData, updatesMap.PickUp, metadataMap.PickUp, false)
	addDataToForm(ctx, &formData, updatesMap.Consignee, metadataMap.Consignee, false)
	addDataToForm(ctx, &formData, updatesMap.Carrier, metadataMap.Carrier, false)
	addDataToForm(ctx, &formData, updatesMap.RateData, metadataMap.RateData, false)

	if updatedLoad.RateData.FSCPercent == nil || *updatedLoad.RateData.FSCPercent == 0 {
		formData.Del("fld664")
	}
	if updatedLoad.RateData.FSCPerMile == nil || *updatedLoad.RateData.FSCPerMile == 0 {
		formData.Del("fld665")
	}

	if updatedLoad.RateData.CustomerRateType != "" {
		formData.Set("fld23", mapRateTypeCode(updatedLoad.RateData.CustomerRateType))
	}
	formData.Set("fld24", fmt.Sprintf("%.2f", updatedLoad.RateData.CustomerLineHaulRate))
	formData.Set("fld526", fmt.Sprintf("%.2f", updatedLoad.RateData.CustomerRateNumUnits))

	if newPayrate := formData.Get("payrate"); newPayrate != "" {
		formData.Set("fld103", newPayrate)
		log.Info(ctx, "updated fld103 with new payrate value", zap.String("val", newPayrate))
	}

	// very similar to commodities population where removing data
	// from a specific ref field doesnt remove it on aljex too.
	// so we clear out data first and then re populate with new fresh data
	populateAdditionalReferencesInForm(&formData, updatedAljex.AdditionalReferences)

	populateCommoditiesInForm(&formData, updatedAljex.Commodities)

	if updatedAljex.TotalPieces.Value > 0 {
		formData.Set(updatedAljex.TotalPieces.FormName, fmt.Sprintf("%.0f", updatedAljex.TotalPieces.Value))
	}
	if updatedAljex.TotalWeight.Value > 0 {
		formData.Set(updatedAljex.TotalWeight.FormName, fmt.Sprintf("%.0f", updatedAljex.TotalWeight.Value))
	}

	if updatedAljex.EquipmentType.Value != "" {
		formData.Set(updatedAljex.EquipmentType.FormName, updatedAljex.EquipmentType.Value)
	}

	if updatedLoad.Specifications.BillableWeight.Val > 0 {
		formData.Set("fld911", fmt.Sprintf("%.0f", updatedLoad.Specifications.BillableWeight.Val))
	}

	if updatedLoad.Specifications.PlanningComment != "" {
		formData.Set("notes", updatedLoad.Specifications.PlanningComment)
	}

	// If load update is successful, it returns a simple, small HTML page.
	// If not, it returns the same page but will a bunch of autosuggestions JS code
	// See example in ./livetest/update_pro_success_resp.html
	checkRespFunc := func(respBody string) int {
		if !strings.Contains(respBody, "<form method=post name=web action=/route.php>") {
			return http.StatusBadRequest
		}
		return http.StatusOK
	}

	_, _, err = a.postForm(ctx, a.getAPIURL(), strings.NewReader(formData.Encode()),
		true, s3backup.TypeLoads, checkRespFunc)
	if err != nil {
		return res, attrs, fmt.Errorf("failed to update Aljex load: %w", err)
	}

	// Re-parse load
	return a.GetLoad(ctx, curLoad.FreightTrackingID)
}

func (a *Aljex) GetLoadsByIDType(
	ctx context.Context,
	id string, idType string,
) (_ []models.Load, _ models.LoadAttributes, err error) {

	spanAttrs := append(otel.IntegrationAttrs(a.tms),
		attribute.String("id", id), attribute.String("idType", id))

	ctx, metaSpan := otel.StartSpan(ctx, "GetLoadsByIDType", spanAttrs)
	defer func() { metaSpan.End(err) }()

	switch idType {
	case PROIDType:
		load, attrs, err := a.GetLoad(ctx, id)
		return []models.Load{load}, attrs, err

	case CustomerRefIDType:
		load, attrs, err := a.getLoadByCustomerRef(ctx, id)
		return []models.Load{load}, attrs, err

	default:
		return nil, a.GetDefaultLoadAttributes(), fmt.Errorf("unrecognized ID type: %s", idType)
	}
}

// maps specific transport type to general TransportTypeEnum category
func (a *Aljex) MapTransportTypeEnum(transportType string) (models.TransportType, error) {
	transportType = strings.TrimSpace(strings.ToLower(transportType))

	if category, exists := equipmentToTransportTypeEnum[transportType]; exists {
		return category, nil
	}

	return "", fmt.Errorf("unknown transport type: %s", transportType)
}

func (a *Aljex) getLoadByCustomerRef(
	ctx context.Context,
	customerRef string,
) (res models.Load, attrs models.LoadAttributes, err error) {
	spanAttrs := append(otel.IntegrationAttrs(a.tms),
		attribute.String("customer_ref", customerRef))

	ctx, metaSpan := otel.StartSpan(ctx, "getLoadByCustomerRef", spanAttrs)
	defer func() { metaSpan.End(err) }()

	attrs = a.GetDefaultLoadAttributes()

	// Search dates; limit to +/1 month as this search takes several seconds
	fdate := time.Now().AddDate(0, 0, -45)
	tdate := time.Now().AddDate(0, 0, 45)

	formData := url.Values{}
	formData.Add("prcnam", "vtag")
	formData.Add("QUAL", a.creds.Qual)
	formData.Add("name", a.creds.Name)
	formData.Add("c_tok", a.creds.Token)
	formData.Add("ctlrec", "")
	formData.Add("ctlval", "")
	formData.Add("sys", "3a")
	formData.Add("type", "lookup")
	formData.Add("status", "All")
	formData.Add("limit", "100")
	formData.Add("fdate", fdate.Format("1/2/06")) // Start (PU) Ready date
	formData.Add("tdate", tdate.Format("1/2/06")) // End (PU) Ready date
	formData.Add("reference", customerRef)
	formData.Add("screen", "on") // Display results on screen
	formData.Add("fregion", "?")
	formData.Add("tregion", "?")
	// formData.Add("equtype", "!") // ¯\_(ツ)_/¯

	resp, _, err := a.postForm(ctx, a.getAPIURL(), strings.NewReader(formData.Encode()),
		true, s3backup.TypeLoads)
	if err != nil {
		return res, attrs, fmt.Errorf("error searching for customer ref: %w", err)
	}

	proNums, err := parseSearchResults(ctx, resp)
	if err != nil {
		return res, attrs, err
	}
	count := len(proNums)

	switch count {
	case 0:
		err = errtypes.HTTPResponseError{
			IntegrationName: a.tms.Name,
			AxleTSPID:       a.tms.ID,
			ServiceID:       a.tms.ServiceID,
			HTTPMethod:      http.MethodGet,
			URL:             fmt.Sprintf("%s=%s", customerRef, CustomerRefIDType),
			StatusCode:      http.StatusNotFound,
			ResponseBody:    []byte("404 Not Found"),
		}

		return res, attrs, err

	case 1:
		return a.GetLoad(ctx, proNums[0])

	default:
		err = errtypes.HTTPResponseError{
			IntegrationName: a.tms.Name,
			AxleTSPID:       a.tms.ID,
			ServiceID:       a.tms.ServiceID,
			HTTPMethod:      http.MethodGet,
			URL:             fmt.Sprintf("%s=%s", customerRef, CustomerRefIDType),
			StatusCode:      http.StatusConflict,
			ResponseBody: []byte(fmt.Sprintf("non-deterministic customerRef, %d results returned (%v)",
				count, proNums)),
		}

		return res, attrs, err
	}
}
