package revenova

import (
	"encoding/json"
	"encoding/xml"
)

type TokenResp struct {
	XMLName xml.Name  `xml:"Envelope"`
	Body    LoginBody `xml:"Body"`
}

type LoginBody struct {
	LoginResponse LoginResponse `xml:"loginResponse"`
}

type LoginResponse struct {
	Result LoginResult `xml:"result"`
}

type LoginResult struct {
	MetadataServerURL string   `xml:"metadataServerUrl"`
	PasswordExpired   bool     `xml:"passwordExpired"`
	Sandbox           bool     `xml:"sandbox"`
	ServerURL         string   `xml:"serverUrl"`
	SessionID         string   `xml:"sessionId"`
	UserID            string   `xml:"userId"`
	UserInfo          UserInfo `xml:"userInfo"`
}

type UserInfo struct {
	OrganizationID            string `xml:"organizationId"`
	OrganizationMultiCurrency bool   `xml:"organizationMultiCurrency"`
	OrganizationName          string `xml:"organizationName"`
	ProfileID                 string `xml:"profileId"`
	RoleID                    string `xml:"roleId"`
	SessionSecondsValid       int    `xml:"sessionSecondsValid"`
	UserEmail                 string `xml:"userEmail"`
	UserFullName              string `xml:"userFullName"`
	UserID                    string `xml:"userId"`
	UserName                  string `xml:"userName"`
}

type GetLoadData struct {
	WeightUnits              string      `json:"weightUnits"`
	VolumeUnits              *string     `json:"volumeUnits"`
	TrackingProvider         *string     `json:"trackingProvider"`
	TrackingProviderCustomer *string     `json:"trackingProviderCustomer"`
	TrackingNumber           *string     `json:"trackingNumber"`
	TrackingNumberCustomer   *string     `json:"trackingNumberCustomer"`
	TotalWeight              int         `json:"totalWeight"`
	TotalVolume              json.Number `json:"totalVolume"`
	TenderAcceptedDate       string      `json:"tenderAcceptedDate"`
	Stops                    []struct {
		StopStatus             *string `json:"stopStatus"`
		StopNumber             int     `json:"stopNumber"`
		ShippingReceivingHours string  `json:"shippingReceivingHours"`
		ShippingContact        struct {
			Phone     string `json:"phone"`
			LastName  string `json:"lastName"`
			FirstName string `json:"firstName"`
			Email     string `json:"email"`
		} `json:"shippingContact"`
		References           *string `json:"references"`
		PickupDeliveryNumber *string `json:"pickupDeliveryNumber"`
		MilesAway            float64 `json:"milesAway"`
		Location             struct {
			ShippingStateProvince string `json:"shippingStateProvince"`
			ShippingPostalCode    string `json:"shippingPostalCode"`
			ShippingCountry       string `json:"shippingCountry"`
			ShippingCity          string `json:"shippingCity"`
			ShippingAddress       string `json:"shippingAddress"`
			CompanyName           string `json:"companyName"`
		} `json:"location"`
		KilometersAway        float64 `json:"kilometersAway"`
		IsPickup              bool    `json:"isPickup"`
		IsGeolocation         bool    `json:"isGeolocation"`
		IsDropOff             bool    `json:"isDropOff"`
		Instructions          *string `json:"instructions"`
		ExpectedDate          *string `json:"expectedDate"`
		DepartureTime         *string `json:"departureTime"`
		DepartureDate         *string `json:"departureDate"`
		CumulativeMiles       float64 `json:"cumulativeMiles"`
		CarrierStatusComments *string `json:"carrierStatusComments"`
		CarrierStatusAsOf     *string `json:"carrierStatusAsOf"`
		CarrierStatus         *string `json:"carrierStatus"`
		CarrierEtaTime        *string `json:"carrierEtaTime"`
		CarrierEtaDate        *string `json:"carrierEtaDate"`
		ArrivalTime           *string `json:"arrivalTime"`
		ArrivalStatus         string  `json:"arrivalStatus"`
		ArrivalDate           *string `json:"arrivalDate"`
		AppointmentTime       string  `json:"appointmentTime"`
		AppointmentRequired   bool    `json:"appointmentRequired"`
		Address               string  `json:"address"`
	} `json:"Stops"`
	StateLane              string  `json:"stateLane"`
	ShipToAddress          string  `json:"shipToAddress"`
	ShipStatus             string  `json:"shipStatus"`
	ShipFromAddress        string  `json:"shipFromAddress"`
	ScheduleStatus         string  `json:"scheduleStatus"`
	ProNumber              *string `json:"proNumber"`
	PoNumber               *string `json:"poNumber"`
	PodReceived            bool    `json:"podReceived"`
	OtherInstructions      *string `json:"otherInstructions"`
	Origin                 string  `json:"origin"`
	OrderNumber            *string `json:"orderNumber"`
	OrderDate              string  `json:"orderDate"`
	ModeName               string  `json:"modeName"`
	LoadStatusComments     *string `json:"loadStatusComments"`
	LoadStatus             string  `json:"loadStatus"`
	LoadPostingDescription string  `json:"loadPostingDescription"`
	LoadNumber             string  `json:"loadNumber"`
	LoadID                 string  `json:"loadId"`
	LineItems              []struct {
		WeightUnits         string      `json:"weightUnits"`
		Weight              float64     `json:"weight"`
		VolumeUnits         *string     `json:"volumeUnits"`
		Volume              json.Number `json:"volume"`
		Turnable            bool        `json:"turnable"`
		Stackable           bool        `json:"stackable"`
		ScheduleBCode       string      `json:"scheduleBCode"`
		PickupStopNumber    int         `json:"pickupStopNumber"`
		PackagingUnits      string      `json:"packagingUnits"`
		PackagingUnitCount  int         `json:"packagingUnitCount"`
		NmfcNumber          string      `json:"nmfcNumber"`
		NmfcClass           string      `json:"nmfcClass"`
		LinearFeet          float64     `json:"linearFeet"`
		ItemNumber          string      `json:"itemNumber"`
		ItemDescription     string      `json:"itemDescription"`
		HtsCode             string      `json:"htsCode"`
		HsCode              string      `json:"hsCode"`
		HazMatPackingGroup  *string     `json:"hazMatPackingGroup"`
		HazMatNumber        *string     `json:"hazMatNumber"`
		HazMatContact       *string     `json:"hazMatContact"`
		HazMatClassDivision *string     `json:"hazMatClassDivision"`
		HazardousMaterials  bool        `json:"hazardousMaterials"`
		HandlingUnitWidth   float64     `json:"handlingUnitWidth"`
		HandlingUnits       string      `json:"handlingUnits"`
		HandlingUnitLength  float64     `json:"handlingUnitLength"`
		HandlingUnitHeight  float64     `json:"handlingUnitHeight"`
		HandlingUnitCount   int         `json:"handlingUnitCount"`
		DimensionUnits      string      `json:"dimensionUnits"`
		DeliveryStopNumber  int         `json:"deliveryStopNumber"`
	} `json:"LineItems"`
	LinearFeet             float64     `json:"linearFeet"`
	LastReportedState      string      `json:"lastReportedState"`
	LastReportedLongitude  float64     `json:"lastReportedLongitude"`
	LastReportedLatitude   float64     `json:"lastReportedLatitude"`
	LastReportedCountry    string      `json:"lastReportedCountry"`
	LastReportedCity       string      `json:"lastReportedCity"`
	LastModifiedDate       string      `json:"lastModifiedDate"`
	HazardousMaterials     bool        `json:"hazardousMaterials"`
	ExpectedShipDate       string      `json:"expectedShipDate"`
	ExpectedDeliveryDate   *string     `json:"expectedDeliveryDate"`
	DistanceMiles          int         `json:"distanceMiles"`
	DistanceKilometers     int         `json:"distanceKilometers"`
	Destination            string      `json:"destination"`
	DeliveryStatus         string      `json:"deliveryStatus"`
	CurrencyCode           string      `json:"currencyCode"`
	CreatedDate            string      `json:"createdDate"`
	ContainerTrailerNumber string      `json:"containerTrailerNumber"`
	CityLane               string      `json:"cityLane"`
	CarrierUploads         *string     `json:"carrierUploads"`
	Carrier                string      `json:"carrier"`
	CargoValue             float64     `json:"cargoValue"`
	BookingNumber          *string     `json:"bookingNumber"`
	BillOfLadingNumber     string      `json:"billOfLadingNumber"`
	APILoadID              *string     `json:"apiLoadId"`
	Accessorials           []Accessory `json:"Accessorials"`
}

type PostLoad struct {
	Wsl struct {
		LoadID                 string      `json:"loadId,omitempty"` // Required for updates, omit for creates
		WeightUnits            string      `json:"weightUnits"`
		TotalWeight            int         `json:"totalWeight"`
		TemperatureControlled  bool        `json:"temperatureControlled"`
		Stops                  []Stop      `json:"Stops"`
		ProNumber              *string     `json:"proNumber"`
		PoNumber               *string     `json:"poNumber"`
		OrderDate              string      `json:"orderDate"`
		ModeName               string      `json:"modeName"`
		LoadPostingDescription string      `json:"loadPostingDescription"`
		LoadNumber             string      `json:"loadNumber"`
		LineItems              []LineItem  `json:"LineItems"`
		LinearFeet             *string     `json:"linearFeet"`
		HazardousMaterials     bool        `json:"hazardousMaterials"`
		DistanceMiles          int         `json:"distanceMiles"`
		DistanceKilometers     int         `json:"distanceKilometers"`
		CargoValue             float64     `json:"cargoValue"`
		ContainerTrailerNumber string      `json:"containerTrailerNumber"`
		BillOfLadingNumber     *string     `json:"billOfLadingNumber"`
		Accessorials           []Accessory `json:"Accessorials"`
	} `json:"wsl"`
}

type LineItem struct {
	WeightUnits         string  `json:"weightUnits"`
	Weight              float64 `json:"weight"`
	Stackable           bool    `json:"stackable"`
	Turnable            bool    `json:"turnable"`
	ScheduleBCode       string  `json:"scheduleBCode"`
	PickupStopNumber    int     `json:"pickupStopNumber"`
	PackagingUnits      string  `json:"packagingUnits"`
	PackagingUnitCount  int     `json:"packagingUnitCount"`
	NmfcNumber          string  `json:"nmfcNumber"`
	NmfcClass           string  `json:"nmfcClass"`
	LinearFeet          float64 `json:"linearFeet"`
	ItemNumber          string  `json:"itemNumber"`
	ItemDescription     string  `json:"itemDescription"`
	HtsCode             string  `json:"htsCode"`
	HsCode              string  `json:"hsCode"`
	HazMatPackingGroup  *string `json:"hazMatPackingGroup"`
	HazMatNumber        *string `json:"hazMatNumber"`
	HazMatContact       *string `json:"hazMatContact"`
	HazMatClassDivision *string `json:"hazMatClassDivision"`
	HazardousMaterials  bool    `json:"hazardousMaterials"`
	HandlingUnitWidth   float64 `json:"handlingUnitWidth"`
	HandlingUnits       string  `json:"handlingUnits"`
	HandlingUnitLength  float64 `json:"handlingUnitLength"`
	HandlingUnitHeight  float64 `json:"handlingUnitHeight"`
	HandlingUnitCount   int     `json:"handlingUnitCount"`
	DimensionUnits      string  `json:"dimensionUnits"`
	DeliveryStopNumber  int     `json:"deliveryStopNumber"`
}

type Stop struct {
	StopNumber             int             `json:"stopNumber"`
	ShippingReceivingHours string          `json:"shippingReceivingHours"`
	ShippingContact        ShippingContact `json:"shippingContact"`
	Location               Location        `json:"location"`
	IsPickup               bool            `json:"isPickup"`
	IsDropOff              bool            `json:"isDropOff"`
	IsGeolocation          bool            `json:"isGeolocation"`
	ExpectedDate           *string         `json:"expectedDate"`
	AppointmentTime        *string         `json:"appointmentTime"`
	AppointmentRequired    bool            `json:"appointmentRequired"`
}

type ShippingContact struct {
	Phone     string `json:"phone"`
	LastName  string `json:"lastName"`
	FirstName string `json:"firstName"`
	Email     string `json:"email"`
}

type Location struct {
	ShippingStateProvince string `json:"shippingStateProvince"`
	ShippingPostalCode    string `json:"shippingPostalCode"`
	ShippingCountry       string `json:"shippingCountry"`
	ShippingCity          string `json:"shippingCity"`
	ShippingAddress       string `json:"shippingAddress"`
	CompanyName           string `json:"companyName"`
}

type Accessory struct {
	StopNumber      int    `json:"stopNumber"`
	AccessorialName string `json:"accessorialName"`
	AccessorialID   string `json:"accessorialId"`
}

// Salesforce API Response Models for Customers

type SalesforceQueryResponse struct {
	TotalSize      int                 `json:"totalSize"`
	Done           bool                `json:"done"`
	NextRecordsURL string              `json:"nextRecordsUrl,omitempty"`
	Records        []SalesforceAccount `json:"records"`
}

type SalesforceAccount struct {
	Attributes           SalesforceAttributes `json:"attributes"`
	ID                   string               `json:"Id"`
	Name                 string               `json:"Name"`
	Phone                string               `json:"Phone"`
	BillingStreet        string               `json:"BillingStreet"`
	BillingCity          string               `json:"BillingCity"`
	BillingState         string               `json:"BillingState"`
	BillingPostalCode    string               `json:"BillingPostalCode"`
	BillingCountry       string               `json:"BillingCountry"`
	BillingStateCode     string               `json:"BillingStateCode"`
	BillingCountryCode   string               `json:"BillingCountryCode"`
	ShippingStreet       string               `json:"ShippingStreet"`
	ShippingCity         string               `json:"ShippingCity"`
	ShippingState        string               `json:"ShippingState"`
	ShippingPostalCode   string               `json:"ShippingPostalCode"`
	ShippingCountry      string               `json:"ShippingCountry"`
	ShippingStateCode    string               `json:"ShippingStateCode"`
	ShippingCountryCode  string               `json:"ShippingCountryCode"`
	CustomerSupportEmail string               `json:"Customer_Support_Email__c"`
	BillingContactEmail  string               `json:"cc_Billing_Contact_Email__c"`
	TMSType              string               `json:"rtms__TMS_Type__c"`
}

type SalesforceAttributes struct {
	Type string `json:"type"`
	URL  string `json:"url"`
}

// SalesforceCountResponse represents the response for COUNT queries
type SalesforceCountResponse struct {
	TotalSize int                     `json:"totalSize"`
	Done      bool                    `json:"done"`
	Records   []SalesforceCountRecord `json:"records"`
}

type SalesforceCountRecord struct {
	Attributes SalesforceAttributes `json:"attributes"`
	Expr0      int                  `json:"expr0"`
}

// SalesforceLoadQueryResponse represents the response for Load queries
type SalesforceLoadQueryResponse struct {
	TotalSize      int              `json:"totalSize"`
	Done           bool             `json:"done"`
	NextRecordsURL string           `json:"nextRecordsUrl,omitempty"`
	Records        []SalesforceLoad `json:"records"`
}

// SalesforceLoad represents a Load record from Salesforce with location fields
type SalesforceLoad struct {
	Attributes            SalesforceAttributes `json:"attributes"`
	ShipFromAddress       string               `json:"rtms__Ship_From_Address__c"`
	ShipToAddress         string               `json:"rtms__Ship_To_Address__c"`
	Origin                string               `json:"rtms__Origin__c"`
	Destination           string               `json:"rtms__Destination__c"`
	OriginStreet          string               `json:"mp_Origin_Street__c"`
	OriginPostalCode      string               `json:"mp_Origin_Postal_Code__c"`
	DestinationStreet     string               `json:"mp_Destination_Street__c"`
	DestinationPostalCode string               `json:"mp_Destination_Postal_Code__c"`
	OriginCity            string               `json:"cc_Origin_City__c"`
	OriginState           string               `json:"cc_Origin_State__c"`
	DestinationCity       string               `json:"cc_Destination_City__c"`
	DestinationState      string               `json:"cc_Destination_State__c"`
	PostalCodeLane        string               `json:"rtms__Postal_Code_Lane__c"`
	StateLane             string               `json:"rtms__State_Lane__c"`
	CountryLane           string               `json:"rtms__Country_Lane__c"`
}

// SalesforceLoadIDResponse represents the response for Load ID queries
// Used by GetLoadIDs to fetch load numbers for polling
type SalesforceLoadIDResponse struct {
	TotalSize      int                      `json:"totalSize"`
	Done           bool                     `json:"done"`
	NextRecordsURL string                   `json:"nextRecordsUrl,omitempty"`
	Records        []SalesforceLoadIDRecord `json:"records"`
}

// SalesforceLoadIDRecord represents a minimal Load record with just ID and Name
type SalesforceLoadIDRecord struct {
	Attributes SalesforceAttributes `json:"attributes"`
	ID         string               `json:"Id"`
	Name       string               `json:"Name"`
}

// SalesforceAccountLocationQueryResponse represents the response for Account location queries
type SalesforceAccountLocationQueryResponse struct {
	TotalSize      int                         `json:"totalSize"`
	Done           bool                        `json:"done"`
	NextRecordsURL string                      `json:"nextRecordsUrl,omitempty"`
	Records        []SalesforceAccountLocation `json:"records"`
}

// SalesforceAccountLocation represents an Account record from Salesforce with location fields
type SalesforceAccountLocation struct {
	Attributes         SalesforceAttributes `json:"attributes"`
	ID                 string               `json:"Id"`
	Name               string               `json:"Name"`
	Phone              string               `json:"Phone"`
	ShippingStreet     string               `json:"ShippingStreet"`
	ShippingCity       string               `json:"ShippingCity"`
	ShippingState      string               `json:"ShippingState"`
	ShippingPostalCode string               `json:"ShippingPostalCode"`
	ShippingCountry    string               `json:"ShippingCountry"`
}
