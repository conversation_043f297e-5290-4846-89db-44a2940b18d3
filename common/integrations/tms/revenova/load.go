package revenova

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"regexp"
	"strconv"
	"strings"
	"time"

	"go.opentelemetry.io/otel/attribute"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/helpers/timezone"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

const (
	// Salesforce API paths
	salesforceLoadServicePath = "/services/apexrest/rtms/tmsloadservice"
	salesforceLoadObjectPath  = "/services/data/v58.0/sobjects/rtms__Load__c"

	// Date and time formats
	dateTimeFormat               = "2006-01-02 15:04"
	dateTimeISOFormat            = "2006-01-02T15:04:05Z"
	timeOnlyWithoutSecondsFormat = "15:04"
)

// PartialResultError wraps an error that occurred during pagination
// while still providing the partial results that were collected
type PartialResultError struct {
	OriginalError error
	PartialCount  int
	FailedAtPage  int
}

func (e *PartialResultError) Error() string {
	return fmt.Sprintf(
		"pagination failed at page %d after collecting %d items: %v",
		e.FailedAtPage,
		e.PartialCount,
		e.OriginalError,
	)
}

func (e *PartialResultError) Unwrap() error {
	return e.OriginalError
}

// parseDepartureTime attempts to parse a departure time string, handling both
// RFC3339 datetime format and time-only format (HH:MM) that needs to be combined with a date.
func parseDepartureTime(ctx context.Context, timeStr string, dateStr *string) (time.Time, bool) {
	// Try parsing as full RFC3339 datetime first
	if dispatchTime, err := time.Parse(time.RFC3339, timeStr); err == nil {
		return dispatchTime, true
	}

	// If RFC3339 fails, try parsing as time-only format (HH:MM) and combine with date
	departureTime, err := time.Parse(timeOnlyWithoutSecondsFormat, timeStr)
	if err != nil {
		log.Warn(
			ctx,
			"unable to parse departure time string",
			zap.String("timeString", timeStr),
			zap.Error(err),
		)
		return time.Time{}, false
	}

	if dateStr == nil {
		log.WarnNoSentry(
			ctx,
			"departure time provided but no departure date, skipping",
			zap.String("departureTime", timeStr),
			zap.String("timeString", timeStr),
		)
		return time.Time{}, false
	}

	departureDate, err := time.Parse(time.DateOnly, *dateStr)
	if err != nil {
		log.Warn(
			ctx,
			"unable to parse departure date",
			zap.String("dateString", *dateStr),
			zap.Error(err),
		)
		return time.Time{}, false
	}

	// Combine date and time
	return time.Date(
		departureDate.Year(), departureDate.Month(), departureDate.Day(),
		departureTime.Hour(), departureTime.Minute(), 0, 0,
		departureDate.Location(),
	), true
}

// Helper function to parse a single time string and combine with date
func parseTimeWithDate(ctx context.Context, dateStr *string, timeStr string) (models.NullTime, error) {
	if dateStr == nil {
		return models.NullTime{Valid: false}, errors.New("date is required")
	}

	dateParsed, err := time.Parse(time.DateOnly, *dateStr)
	if err != nil {
		log.Warn(
			ctx,
			"unable to parse date",
			zap.String("dateString", *dateStr),
			zap.Error(err),
		)
		return models.NullTime{Valid: false}, fmt.Errorf("unable to parse date: %w", err)
	}

	parsedTime, err := time.Parse(timeOnlyWithoutSecondsFormat, strings.TrimSpace(timeStr))
	if err != nil {
		log.Warn(
			ctx,
			"unable to parse time",
			zap.String("timeString", timeStr),
			zap.Error(err),
		)
		return models.NullTime{Valid: false}, fmt.Errorf("unable to parse time: %w", err)
	}

	return models.NullTime{
		Time: time.Date(
			dateParsed.Year(),
			dateParsed.Month(),
			dateParsed.Day(),
			parsedTime.Hour(),
			parsedTime.Minute(),
			0,
			0,
			dateParsed.Location(),
		),
		Valid: true,
	}, nil
}

// parseAppointmentTime attempts to parse an appointment time string (HH:MM format or HH:MM-HH:MM range)
// and combine it with an expected date to create a full datetime.
// - If the appointment time is a range (e.g., "12:00-15:00"), it returns both start and end times.
// - If it's a single time, only apptStartTime will be valid.
func parseAppointmentTime(
	ctx context.Context,
	appointmentTime string,
	expectedDate *string,
) (models.NullTime, models.NullTime, error) {
	if appointmentTime == "" || expectedDate == nil {
		return models.NullTime{Valid: false},
			models.NullTime{Valid: false},
			errors.New("appointment time or expected date is required")
	}

	// Check if appointment time is a range (e.g., "12:00-15:00")
	timeParts := strings.Split(appointmentTime, "-")
	if len(timeParts) == 2 {
		startTime, err := parseTimeWithDate(ctx, expectedDate, timeParts[0])
		if err != nil {
			return models.NullTime{Valid: false},
				models.NullTime{Valid: false},
				fmt.Errorf("unable to parse appointment start time: %w", err)
		}

		endTime, err := parseTimeWithDate(ctx, expectedDate, timeParts[1])
		if err != nil {
			return models.NullTime{Valid: false},
				models.NullTime{Valid: false},
				fmt.Errorf("unable to parse appointment end time: %w", err)
		}

		return startTime, endTime, nil
	}

	// Single time format: parse only start time
	startTime, err := parseTimeWithDate(ctx, expectedDate, appointmentTime)
	if err != nil {
		return models.NullTime{Valid: false},
			models.NullTime{Valid: false},
			fmt.Errorf("unable to parse appointment time: %w", err)
	}

	return startTime, models.NullTime{Valid: false}, nil
}

// GetLoadIDs retrieves load IDs (load numbers) from Revenova within the specified date range
// Returns load numbers (Name field) which will be used as freight_tracking_id
// The poller will then call GetLoad() with each load number to fetch full load details
func (r Revenova) GetLoadIDs(
	ctx context.Context,
	query models.SearchLoadsQuery,
) ([]string, error) {
	var err error
	ctx, metaSpan := otel.StartSpan(ctx, "GetLoadIDsRevenova", otel.IntegrationAttrs(r.tms))
	defer func() { metaSpan.End(err) }()

	var allLoadIDs []string
	loadIDMap := make(map[string]struct{}) // Use map for deduplication

	// Build SOQL query with date filtering
	soqlQuery := "SELECT Id, Name FROM rtms__Load__c WHERE "

	var conditions []string

	// Add date filters based on LastModifiedDate
	if query.FromDate.Valid {
		// Salesforce expects ISO 8601 format
		fromDate := query.FromDate.Time.UTC().Format(dateTimeISOFormat)
		conditions = append(conditions, fmt.Sprintf("LastModifiedDate >= %s", fromDate))
	}

	if query.ToDate.Valid {
		toDate := query.ToDate.Time.UTC().Format(dateTimeISOFormat)
		conditions = append(conditions, fmt.Sprintf("LastModifiedDate <= %s", toDate))
	}

	// If no conditions, add a default one to make the query valid
	if len(conditions) == 0 {
		conditions = append(conditions, "1=1")
	}

	// Combine conditions
	soqlQuery += strings.Join(conditions, " AND ")

	// Order by LastModifiedDate DESC to get most recent loads first
	soqlQuery += " ORDER BY LastModifiedDate DESC"

	log.Info(
		ctx,
		"executing Salesforce SOQL query for load IDs",
		zap.String("query", soqlQuery),
		zap.Bool("fromDateValid", query.FromDate.Valid),
		zap.Bool("toDateValid", query.ToDate.Valid),
	)

	// Salesforce returns max 2000 records per query
	// Use nextRecordsUrl for pagination
	nextRecordsPath := ""
	pageCount := 0

	for {
		pageCount++
		queryParams := url.Values{}

		var resp SalesforceLoadIDResponse

		if nextRecordsPath != "" {
			// Fetch next page using the nextRecordsUrl
			log.Info(
				ctx,
				"fetching next page of load IDs",
				zap.Int("pageNumber", pageCount),
				zap.String("nextRecordsPath", nextRecordsPath))

			err = r.get(ctx, nextRecordsPath, nil, &resp, s3backup.TypeLoads)
		} else {
			// First page
			queryParams.Set("q", soqlQuery)
			log.Info(
				ctx,
				"fetching first page of load IDs",
				zap.Int("pageNumber", pageCount))

			err = r.get(ctx, "/services/data/v59.0/query", queryParams, &resp, s3backup.TypeLoads)
		}

		if err != nil {
			log.Error(
				ctx,
				"failed to query load IDs from Salesforce",
				zap.Error(err),
				zap.Int("pageNumber", pageCount),
				zap.Int("loadsCollectedSoFar", len(loadIDMap)),
			)
			// Return what we have so far rather than discarding all collected data
			// This provides resilience against transient pagination failures
			log.Warn(
				ctx,
				"pagination failed, returning partial load IDs",
				zap.Int("failedAtPage", pageCount),
				zap.Int("partialCount", len(loadIDMap)),
			)
			// Return partial results with error information
			allLoadIDs = mapKeysToSlice(loadIDMap)
			partialErr := &PartialResultError{
				OriginalError: err,
				PartialCount:  len(loadIDMap),
				FailedAtPage:  pageCount,
			}
			err = partialErr // Update err for telemetry
			return allLoadIDs, partialErr
		}

		log.Info(
			ctx,
			"received load IDs page from Salesforce",
			zap.Int("pageNumber", pageCount),
			zap.Int("recordsInPage", len(resp.Records)),
			zap.Int("totalSize", resp.TotalSize),
			zap.Bool("done", resp.Done),
		)

		// Extract load numbers (Name field) and deduplicate
		for _, record := range resp.Records {
			if record.Name != "" {
				loadIDMap[record.Name] = struct{}{}
			} else {
				// Fallback to ID if Name is empty (shouldn't happen, but be defensive)
				log.Warn(
					ctx,
					"load record has empty Name, using ID instead",
					zap.String("loadID", record.ID),
				)
				loadIDMap[record.ID] = struct{}{}
			}
		}

		// Check if more pages exist
		if resp.Done {
			log.Info(
				ctx,
				"completed fetching all load IDs",
				zap.Int("totalPages", pageCount),
				zap.Int("uniqueLoadCount", len(loadIDMap)),
			)
			break
		}

		// Set nextRecordsPath for the next iteration
		nextRecordsPath = resp.NextRecordsURL
		if nextRecordsPath == "" {
			log.Warn(ctx, "nextRecordsUrl is empty but done is false, breaking pagination")
			allLoadIDs = mapKeysToSlice(loadIDMap)
			partialErr := &PartialResultError{
				OriginalError: errors.New("pagination inconsistency: done=false but nextRecordsUrl is empty"),
				PartialCount:  len(loadIDMap),
				FailedAtPage:  pageCount,
			}
			err = partialErr // Update err for telemetry
			return allLoadIDs, partialErr
		}
	}

	allLoadIDs = mapKeysToSlice(loadIDMap)
	log.Info(ctx, "finished fetching all load IDs",
		zap.Int("totalCount", len(allLoadIDs)),
		zap.Int("totalPages", pageCount))

	return allLoadIDs, nil
}

// Helper function to convert map keys to a slice
func mapKeysToSlice(m map[string]struct{}) []string {
	result := make([]string, 0, len(m))
	for k := range m {
		result = append(result, k)
	}
	return result
}

func (r *Revenova) GetLoad(ctx context.Context, loadID string) (models.Load, models.LoadAttributes, error) {
	var err error
	var metaSpan otel.MetaSpan

	spanAttrs := append(otel.IntegrationAttrs(r.tms), attribute.String("freight_tracking_id", loadID))
	ctx, metaSpan = otel.StartSpan(ctx, "GetLoadRevenova", spanAttrs)
	defer func() { metaSpan.End(err) }()

	attrs := r.GetDefaultLoadAttributes()

	// Check if the loadID is a Salesforce ID (18 chars starting with alphanumeric) or a load number
	// Salesforce IDs are typically 15 or 18 characters and start with letters/numbers
	// Load numbers are typically shorter or have different patterns like "Load-101699", "89", etc.
	actualLoadID := loadID
	if !r.isSalesforceID(loadID) {
		// This appears to be a load number, not a Salesforce ID
		// Query Salesforce to find the Load ID by the load number (Name field)
		log.Info(ctx, "Load ID appears to be a load number, querying Salesforce to find the actual Load ID",
			zap.String("loadNumber", loadID))

		salesforceID, err := r.getLoadIDByLoadNumber(ctx, loadID)
		if err != nil {
			log.Error(ctx, "Failed to find Load ID by load number", zap.Error(err))
			return models.Load{}, attrs, fmt.Errorf("failed to find load by number %s: %w", loadID, err)
		}

		actualLoadID = salesforceID
		log.Info(ctx, "Found Salesforce Load ID for load number",
			zap.String("loadNumber", loadID),
			zap.String("salesforceLoadID", actualLoadID))
	}

	endPoint := fmt.Sprintf("%s/%s", salesforceLoadServicePath, actualLoadID)
	var loadResp GetLoadData
	err = r.get(ctx, endPoint, nil, &loadResp, s3backup.TypeLoads)
	if err != nil {
		return models.Load{}, attrs, err
	}

	// Log the raw response to debug what data we're actually getting
	log.Info(ctx, "Revenova API Response Details",
		zap.String("loadID", loadResp.LoadID),
		zap.String("carrier", loadResp.Carrier),
		zap.Float64("cargoValue", loadResp.CargoValue),
		zap.Int("numStops", len(loadResp.Stops)),
		zap.Int("numLineItems", len(loadResp.LineItems)))

	// Log first stop details if available
	if len(loadResp.Stops) > 0 {
		firstStop := loadResp.Stops[0]
		log.Info(ctx, "First Stop Details",
			zap.String("companyName", firstStop.Location.CompanyName),
			zap.String("address", firstStop.Location.ShippingAddress),
			zap.String("city", firstStop.Location.ShippingCity),
			zap.String("firstName", firstStop.ShippingContact.FirstName),
			zap.String("lastName", firstStop.ShippingContact.LastName),
			zap.String("phone", firstStop.ShippingContact.Phone),
			zap.String("email", firstStop.ShippingContact.Email))
	}

	loadData := r.RevenovaLoadToDrumkitLoad(ctx, loadResp)

	// If customer data is missing, fetch the customer ID from Salesforce and then get customer details
	// This works like Turvo's GetCustomerByID approach
	if loadData.Customer.Name == "" && loadResp.LoadID != "" {
		log.Info(ctx, "Customer name is empty, fetching customer ID from load record")

		// First, get the customer ID associated with this load
		customerID, err := r.getCustomerIDFromLoad(ctx, loadResp.LoadID)
		if err != nil {
			log.Warn(ctx, "Failed to get customer ID from load", zap.Error(err))
		} else if customerID != "" {
			log.Info(ctx, "Found customer ID, fetching customer details",
				zap.String("customerID", customerID))

			// Set the customer ID
			loadData.Customer.ExternalTMSID = customerID
			loadData.BillTo.ExternalTMSID = customerID

			// Now fetch the complete customer details
			customer, err := r.GetCustomerByID(ctx, customerID)
			if err != nil {
				log.Warn(ctx, "Failed to fetch customer details", zap.Error(err))
			} else {
				// Determine which address to use (prefer Billing, fallback to Shipping)
				// Use helper function to get the best available address
				customerAddr := r.getBestAddress(customer)

				// Populate BOTH customer and billTo with the SAME address
				// In logistics, customer = billTo (the company being billed)
				loadData.Customer.Name = customer.Name
				loadData.Customer.AddressLine1 = customerAddr.Street
				loadData.Customer.City = customerAddr.City
				loadData.Customer.State = customerAddr.State
				loadData.Customer.Zipcode = customerAddr.Zipcode
				loadData.Customer.Country = customerAddr.Country
				loadData.Customer.Phone = customer.Phone

				// BillTo gets the SAME data as Customer
				loadData.BillTo.Name = customer.Name
				loadData.BillTo.AddressLine1 = customerAddr.Street
				loadData.BillTo.City = customerAddr.City
				loadData.BillTo.State = customerAddr.State
				loadData.BillTo.Zipcode = customerAddr.Zipcode
				loadData.BillTo.Country = customerAddr.Country
				loadData.BillTo.Phone = customer.Phone

				log.Info(ctx, "Successfully fetched and populated customer data",
					zap.String("customerName", loadData.Customer.Name),
					zap.String("customerCity", loadData.Customer.City))
			}
		}
	}

	return loadData, attrs, nil
}

func (r *Revenova) CreateLoad(
	ctx context.Context,
	load models.Load,
	_ *models.TMSUser,
) (result models.Load, err error) {
	spanAttrs := append(otel.IntegrationAttrs(r.tms), otel.LoadAttrs(load)...)
	ctx, metaSpan := otel.StartSpan(ctx, "CreateLoadRevenova", spanAttrs)
	defer func() { metaSpan.End(err) }()

	reqBody := r.DrumkitLoadToRevenovaLoad(load)

	var response GetLoadData
	err = r.post(ctx, salesforceLoadServicePath, nil, reqBody, &response, s3backup.TypeLoads)
	if err != nil {
		return result, err
	}

	result = r.RevenovaLoadToDrumkitLoad(ctx, response)
	if err != nil {
		return result, err
	}

	// IMPORTANT: Preserve customer ExternalTMSID and details from the original request
	// The Revenova API response doesn't include customer details, so we need to copy them from the input
	if load.Customer.ExternalTMSID != "" {
		log.Info(ctx, "preserving customer ExternalTMSID from request",
			zap.String("customerID", load.Customer.ExternalTMSID))

		result.Customer.ExternalTMSID = load.Customer.ExternalTMSID
		result.BillTo.ExternalTMSID = load.Customer.ExternalTMSID

		// Preserve customer details from the request since API doesn't return them
		result.Customer.Name = load.Customer.Name
		result.Customer.AddressLine1 = load.Customer.AddressLine1
		result.Customer.AddressLine2 = load.Customer.AddressLine2
		result.Customer.City = load.Customer.City
		result.Customer.State = load.Customer.State
		result.Customer.Zipcode = load.Customer.Zipcode
		result.Customer.Country = load.Customer.Country
		result.Customer.Phone = load.Customer.Phone
		result.Customer.Email = load.Customer.Email
		result.Customer.Contact = load.Customer.Contact
		result.Customer.RefNumber = load.Customer.RefNumber

		// Also copy to BillTo
		result.BillTo.Name = load.Customer.Name
		result.BillTo.AddressLine1 = load.Customer.AddressLine1
		result.BillTo.AddressLine2 = load.Customer.AddressLine2
		result.BillTo.City = load.Customer.City
		result.BillTo.State = load.Customer.State
		result.BillTo.Zipcode = load.Customer.Zipcode
		result.BillTo.Country = load.Customer.Country
		result.BillTo.Phone = load.Customer.Phone
		result.BillTo.Email = load.Customer.Email
		result.BillTo.Contact = load.Customer.Contact
	}

	// Preserve pickup information from request (API may not return complete details)
	if load.Pickup.Name != "" || load.Pickup.ExternalTMSID != "" {
		log.Info(ctx, "preserving pickup details from request")
		result.Pickup.ExternalTMSID = load.Pickup.ExternalTMSID
		result.Pickup.Name = load.Pickup.Name
		result.Pickup.AddressLine1 = load.Pickup.AddressLine1
		result.Pickup.AddressLine2 = load.Pickup.AddressLine2
		result.Pickup.City = load.Pickup.City
		result.Pickup.State = load.Pickup.State
		result.Pickup.Zipcode = load.Pickup.Zipcode
		result.Pickup.Country = load.Pickup.Country
		result.Pickup.Contact = load.Pickup.Contact
		result.Pickup.Phone = load.Pickup.Phone
		result.Pickup.Email = load.Pickup.Email
		result.Pickup.RefNumber = load.Pickup.RefNumber
		result.Pickup.BusinessHours = load.Pickup.BusinessHours
	}

	// Preserve consignee information from request (API may not return complete details)
	if load.Consignee.Name != "" || load.Consignee.ExternalTMSID != "" {
		log.Info(ctx, "preserving consignee details from request")
		result.Consignee.ExternalTMSID = load.Consignee.ExternalTMSID
		result.Consignee.Name = load.Consignee.Name
		result.Consignee.AddressLine1 = load.Consignee.AddressLine1
		result.Consignee.AddressLine2 = load.Consignee.AddressLine2
		result.Consignee.City = load.Consignee.City
		result.Consignee.State = load.Consignee.State
		result.Consignee.Zipcode = load.Consignee.Zipcode
		result.Consignee.Country = load.Consignee.Country
		result.Consignee.Contact = load.Consignee.Contact
		result.Consignee.Phone = load.Consignee.Phone
		result.Consignee.Email = load.Consignee.Email
		result.Consignee.RefNumber = load.Consignee.RefNumber
		result.Consignee.BusinessHours = load.Consignee.BusinessHours
	}

	// After successful load creation, update the load with customer information in Salesforce
	// This is required because Revenova doesn't support customer assignment during load creation
	if load.Customer.ExternalTMSID != "" && result.ExternalTMSID != "" {
		log.Info(ctx, "updating load with customer in Salesforce",
			zap.String("loadID", result.ExternalTMSID),
			zap.String("customerID", load.Customer.ExternalTMSID))

		err = r.UpdateLoadCustomer(ctx, result.ExternalTMSID, load.Customer.ExternalTMSID)
		if err != nil {
			log.Error(ctx, "failed to update load with customer in Salesforce", zap.Error(err))
			// Don't fail the entire operation, just log the error
			// The load was created successfully, customer association in SF failed
		} else {
			log.Info(ctx, "successfully associated customer with load in Salesforce",
				zap.String("loadID", result.ExternalTMSID),
				zap.String("customerID", load.Customer.ExternalTMSID))
		}
	}

	return result, nil
}

func (r *Revenova) UpdateLoad(
	ctx context.Context,
	_ *models.Load,
	load *models.Load,
) (result models.Load, attr models.LoadAttributes, err error) {
	spanAttrs := append(otel.IntegrationAttrs(r.tms), otel.LoadAttrs(*load)...)
	ctx, metaSpan := otel.StartSpan(ctx, "UpdateLoadRevenova", spanAttrs)
	defer func() { metaSpan.End(err) }()

	log.Info(
		ctx,
		"Starting UpdateLoad for Revenova",
		zap.String("externalTMSID", load.ExternalTMSID),
		zap.String("freightTrackingID", load.FreightTrackingID),
		zap.Float32("weight", load.Specifications.TotalWeight.Val),
	)

	// First, fetch the current load from Revenova to get the exact current state
	// This is necessary because Revenova doesn't allow date changes via API
	var currentLoadData GetLoadData
	endPoint := fmt.Sprintf("%s/%s", salesforceLoadServicePath, load.ExternalTMSID)
	err = r.get(ctx, endPoint, nil, &currentLoadData, s3backup.TypeLoads)
	if err != nil {
		log.Error(ctx, "Failed to fetch current load before update", zap.Error(err))
		return result, attr, fmt.Errorf("failed to fetch current load before update: %w", err)
	}

	log.Info(
		ctx,
		"Fetched current load from Revenova",
		zap.Int("numStops", len(currentLoadData.Stops)),
		zap.Int("numLineItems", len(currentLoadData.LineItems)),
	)

	// Now build the update request based on the current load data
	// This preserves all the dates and other fields that Revenova won't allow us to change
	reqBody := r.buildUpdateRequest(currentLoadData, *load)

	// Set the LoadID in the request body for updates
	reqBody.Wsl.LoadID = load.ExternalTMSID

	queryParams := url.Values{}
	queryParams.Add("loadId", load.ExternalTMSID)       // Use ExternalTMSID (Revenova's loadId)
	queryParams.Add("loadName", load.FreightTrackingID) // Use FreightTrackingID as loadName

	// Log the request body for debugging
	if reqBodyJSON, marshalErr := json.Marshal(reqBody); marshalErr == nil {
		log.Info(ctx, "PATCH request body",
			zap.String("json", string(reqBodyJSON)))
	}

	log.Info(
		ctx,
		"Sending PATCH request to update load",
		zap.String("loadId", load.ExternalTMSID),
		zap.Int("weight", reqBody.Wsl.TotalWeight),
	)

	var response GetLoadData
	err = r.patch(ctx, salesforceLoadServicePath, queryParams, reqBody, &response,
		nil, s3backup.TypeLoads)
	if err != nil {
		log.Error(ctx, "PATCH request failed", zap.Error(err))
		return result, attr, fmt.Errorf("updating Load failed: %w", err)
	}

	log.Info(ctx, "Successfully updated load in Revenova")

	result = r.RevenovaLoadToDrumkitLoad(ctx, response)

	// Preserve customer/pickup/consignee data from the request (like we do in CreateLoad)
	// since the API response may not include complete details
	if load.Customer.ExternalTMSID != "" {
		result.Customer.ExternalTMSID = load.Customer.ExternalTMSID
		result.Customer.Name = load.Customer.Name
		result.Customer.AddressLine1 = load.Customer.AddressLine1
		result.Customer.City = load.Customer.City
		result.Customer.State = load.Customer.State
		result.Customer.Zipcode = load.Customer.Zipcode
		result.Customer.Country = load.Customer.Country
		result.Customer.Phone = load.Customer.Phone
		result.Customer.Email = load.Customer.Email

		result.BillTo.ExternalTMSID = load.Customer.ExternalTMSID
		result.BillTo.Name = load.Customer.Name
		result.BillTo.AddressLine1 = load.Customer.AddressLine1
		result.BillTo.City = load.Customer.City
		result.BillTo.State = load.Customer.State
		result.BillTo.Zipcode = load.Customer.Zipcode
		result.BillTo.Country = load.Customer.Country
		result.BillTo.Phone = load.Customer.Phone
		result.BillTo.Email = load.Customer.Email
	}

	return result, attr, nil
}

// buildUpdateRequest creates an update request by merging the current Revenova load
// with the changes from Drumkit, preserving fields that Revenova doesn't allow to change
func (r Revenova) buildUpdateRequest(currentLoad GetLoadData, updatedLoad models.Load) PostLoad {
	var req PostLoad

	// Preserve all the current values from Revenova
	req.Wsl.WeightUnits = helpers.Or(currentLoad.WeightUnits, "lbs")
	req.Wsl.TotalWeight = int(updatedLoad.Specifications.TotalWeight.Val) // Allow weight changes
	req.Wsl.TemperatureControlled = updatedLoad.Specifications.IsRefrigerated
	req.Wsl.ModeName = helpers.Or(currentLoad.ModeName, "LTL")
	req.Wsl.LoadPostingDescription = helpers.Or(currentLoad.LoadPostingDescription, "Load updated via Drumkit")
	req.Wsl.LoadNumber = helpers.Or(currentLoad.LoadNumber, "Temp Load Name")
	req.Wsl.OrderDate = helpers.Or(currentLoad.OrderDate, time.Now().Format("2006-01-02"))
	req.Wsl.HazardousMaterials = updatedLoad.Specifications.Hazmat
	req.Wsl.DistanceMiles = currentLoad.DistanceMiles
	req.Wsl.DistanceKilometers = currentLoad.DistanceKilometers
	req.Wsl.CargoValue = currentLoad.CargoValue
	req.Wsl.ContainerTrailerNumber = currentLoad.ContainerTrailerNumber

	// Handle optional fields - convert empty strings to nil
	req.Wsl.ProNumber = nil
	if currentLoad.ProNumber != nil && *currentLoad.ProNumber != "" {
		req.Wsl.ProNumber = currentLoad.ProNumber
	}

	req.Wsl.PoNumber = nil
	if currentLoad.PoNumber != nil && *currentLoad.PoNumber != "" {
		req.Wsl.PoNumber = currentLoad.PoNumber
	}

	req.Wsl.BillOfLadingNumber = nil
	if currentLoad.BillOfLadingNumber != "" {
		req.Wsl.BillOfLadingNumber = &currentLoad.BillOfLadingNumber
	}

	// Preserve stops with their original dates
	req.Wsl.Stops = make([]Stop, len(currentLoad.Stops))
	for i, stop := range currentLoad.Stops {
		// Handle AppointmentTime - convert empty string to nil
		var apptTime *string
		if stop.AppointmentTime != "" {
			apptTime = &stop.AppointmentTime
		}

		// Required fields must not be empty strings - provide defaults per documentation
		req.Wsl.Stops[i] = Stop{
			StopNumber: stop.StopNumber,
			// Required - default per doc
			ShippingReceivingHours: helpers.Or(stop.ShippingReceivingHours, "09:00-22:00"),
			IsPickup:               stop.IsPickup,
			IsDropOff:              stop.IsDropOff,
			IsGeolocation:          stop.IsGeolocation,
			ExpectedDate:           stop.ExpectedDate, // Preserve original date
			AppointmentTime:        apptTime,          // nil if empty, pointer if not empty
			AppointmentRequired:    stop.AppointmentRequired,
			ShippingContact: ShippingContact{
				Phone:     stop.ShippingContact.Phone,                            // Can be empty
				Email:     stop.ShippingContact.Email,                            // Can be empty
				FirstName: helpers.Or(stop.ShippingContact.FirstName, "Contact"), // Required - default
				LastName:  helpers.Or(stop.ShippingContact.LastName, "Name"),     // Required - default
			},
			Location: Location{
				ShippingCity:          stop.Location.ShippingCity,
				ShippingStateProvince: stop.Location.ShippingStateProvince,
				ShippingCountry:       helpers.Or(stop.Location.ShippingCountry, "US"),
				ShippingPostalCode:    stop.Location.ShippingPostalCode,
				ShippingAddress:       stop.Location.ShippingAddress,
				CompanyName:           helpers.Or(stop.Location.CompanyName, "Location"), // Required - default
			},
		}
	}

	// Preserve line items but update with new values from request
	req.Wsl.LineItems = make([]LineItem, len(currentLoad.LineItems))
	for i, item := range currentLoad.LineItems {
		// Use updated weight for line items
		lineItemWeight := item.Weight
		if i == 0 && updatedLoad.Specifications.TotalWeight.Val > 0 {
			// Update the first line item's weight to match the total weight
			lineItemWeight = float64(updatedLoad.Specifications.TotalWeight.Val)
		}

		// Use updated description from specifications.commodities if provided
		itemDescription := item.ItemDescription
		if i == 0 && updatedLoad.Specifications.Commodities != "" {
			itemDescription = updatedLoad.Specifications.Commodities
		}

		// Use updated handling unit count from totalPieces if provided
		handlingUnitCount := item.HandlingUnitCount
		if i == 0 && updatedLoad.Specifications.TotalPieces.Val > 0 {
			handlingUnitCount = int(updatedLoad.Specifications.TotalPieces.Val)
		} else if handlingUnitCount == 0 {
			handlingUnitCount = 1 // Default per documentation example
		}

		// Check if there are commodities in the request to use for more detailed updates
		if i == 0 && len(updatedLoad.Commodities) > 0 {
			commodity := updatedLoad.Commodities[0]

			// Use commodity description if provided
			if commodity.Description != "" {
				itemDescription = commodity.Description
			}

			// Use commodity handling quantity if provided
			if commodity.HandlingQuantity > 0 {
				handlingUnitCount = commodity.HandlingQuantity
			}

			// Use commodity weight if provided, but only if specifications.totalWeight is not set
			// Specifications.TotalWeight takes precedence over commodity weight
			if commodity.WeightTotal > 0 && updatedLoad.Specifications.TotalWeight.Val == 0 {
				lineItemWeight = float64(commodity.WeightTotal)
			}
		}

		// Convert empty string pointers to nil for optional fields
		var hazMatPackingGroup *string
		if item.HazMatPackingGroup != nil && *item.HazMatPackingGroup != "" {
			hazMatPackingGroup = item.HazMatPackingGroup
		}

		var hazMatNumber *string
		if item.HazMatNumber != nil && *item.HazMatNumber != "" {
			hazMatNumber = item.HazMatNumber
		}

		var hazMatContact *string
		if item.HazMatContact != nil && *item.HazMatContact != "" {
			hazMatContact = item.HazMatContact
		}

		var hazMatClassDivision *string
		if item.HazMatClassDivision != nil && *item.HazMatClassDivision != "" {
			hazMatClassDivision = item.HazMatClassDivision
		}

		// Use actual dimension values from the API
		// Do not hardcode dimensions - use what's in the current load data
		handlingWidth := item.HandlingUnitWidth
		handlingLength := item.HandlingUnitLength
		handlingHeight := item.HandlingUnitHeight

		req.Wsl.LineItems[i] = LineItem{
			WeightUnits:         helpers.Or(item.WeightUnits, "lbs"), // Required - default
			Weight:              lineItemWeight,                      // Required - Use updated weight
			Stackable:           item.Stackable,
			Turnable:            item.Turnable,
			ScheduleBCode:       item.ScheduleBCode,                  // Can be empty
			PickupStopNumber:    item.PickupStopNumber,               // Required
			PackagingUnits:      item.PackagingUnits,                 // Can be empty
			PackagingUnitCount:  item.PackagingUnitCount,             // Can be 0
			NmfcNumber:          item.NmfcNumber,                     // Can be empty
			NmfcClass:           helpers.Or(item.NmfcClass, "50"),    // Required - default per doc
			LinearFeet:          item.LinearFeet,                     // Can be 0
			ItemNumber:          helpers.Or(item.ItemNumber, "Item"), // Required - default per doc
			ItemDescription:     helpers.Or(itemDescription, "Item"), // Required - Use updated description
			HtsCode:             item.HtsCode,                        // Can be empty
			HsCode:              item.HsCode,                         // Can be empty
			HazMatPackingGroup:  hazMatPackingGroup,                  // nil if empty
			HazMatNumber:        hazMatNumber,                        // nil if empty
			HazMatContact:       hazMatContact,                       // nil if empty
			HazMatClassDivision: hazMatClassDivision,                 // nil if empty
			HazardousMaterials:  item.HazardousMaterials,
			HandlingUnitWidth:   handlingWidth,                             // Use actual value from current load
			HandlingUnits:       helpers.Or(item.HandlingUnits, "Pallets"), // Required - default per doc
			HandlingUnitLength:  handlingLength,                            // Use actual value from current load
			HandlingUnitHeight:  handlingHeight,                            // Use actual value from current load
			HandlingUnitCount:   handlingUnitCount,                         // Required - Use updated count
			DimensionUnits:      helpers.Or(item.DimensionUnits, "in"),     // Required - default per doc
			DeliveryStopNumber:  item.DeliveryStopNumber,                   // Required
		}
	}

	// Preserve accessorials
	req.Wsl.Accessorials = currentLoad.Accessorials

	// Set LinearFeet from current load (convert float64 to *string)
	req.Wsl.LinearFeet = nil
	if currentLoad.LinearFeet > 0 {
		linearFeetStr := fmt.Sprintf("%f", currentLoad.LinearFeet)
		req.Wsl.LinearFeet = &linearFeetStr
	}

	return req
}

func (r Revenova) RevenovaLoadToDrumkitLoad(ctx context.Context, loadResp GetLoadData) (load models.Load) {
	// Set basic load information
	load.ServiceID = r.tms.ServiceID
	load.TMSID = r.tms.ID
	load.ExternalTMSID = loadResp.LoadID
	load.Status = loadResp.LoadStatus
	load.FreightTrackingID = loadResp.LoadNumber

	// Set mode based on ModeName
	switch loadResp.ModeName {
	case "Truckload", "TL":
		load.Mode = models.TLMode
	case "LTL":
		load.Mode = models.LTLMode
	case "Reefer", "Refrigerated":
		load.Mode = models.RefrigeratedMode
	case "Flatbed":
		load.Mode = models.FlatbedMode
	default:
		load.Mode = models.TLMode // default to TL
	}

	// Set specifications
	load.Specifications.TotalWeight = models.ValueUnit{
		Val:  float32(loadResp.TotalWeight),
		Unit: loadResp.WeightUnits,
	}

	// Set total distance
	load.Specifications.TotalDistance = models.ValueUnit{
		Val:  float32(loadResp.DistanceMiles),
		Unit: "mi",
	}

	// Set transport type based on mode
	load.Specifications.TransportType = loadResp.ModeName
	// Note: GetLoadData doesn't have TemperatureControlled field, but we can infer from ModeName
	if loadResp.ModeName == "Reefer" || loadResp.ModeName == "Refrigerated" {
		load.Specifications.IsRefrigerated = true
	}

	// Set PO numbers if available
	if loadResp.PoNumber != nil {
		load.PONums = *loadResp.PoNumber
	}

	// Extract customer information from the first stop
	// In Revenova, the customer is identified by the companyName in the stops
	if len(loadResp.Stops) > 0 {
		firstStop := loadResp.Stops[0]
		load.Customer.Name = firstStop.Location.CompanyName
		load.Customer.AddressLine1 = firstStop.Location.ShippingAddress
		load.Customer.City = firstStop.Location.ShippingCity
		load.Customer.State = firstStop.Location.ShippingStateProvince
		load.Customer.Zipcode = firstStop.Location.ShippingPostalCode
		// Default to "US" if country is empty
		if firstStop.Location.ShippingCountry != "" {
			load.Customer.Country = firstStop.Location.ShippingCountry
		} else {
			load.Customer.Country = "US"
		}
		load.Customer.Phone = firstStop.ShippingContact.Phone
		load.Customer.Email = firstStop.ShippingContact.Email
		contactName := strings.TrimSpace(firstStop.ShippingContact.FirstName + " " + firstStop.ShippingContact.LastName)
		load.Customer.Contact = contactName

		// Populate billTo with the same customer information (like TAI does)
		load.BillTo.Name = firstStop.Location.CompanyName
		load.BillTo.AddressLine1 = firstStop.Location.ShippingAddress
		load.BillTo.City = firstStop.Location.ShippingCity
		load.BillTo.State = firstStop.Location.ShippingStateProvince
		load.BillTo.Zipcode = firstStop.Location.ShippingPostalCode
		// Default to "US" if country is empty
		if firstStop.Location.ShippingCountry != "" {
			load.BillTo.Country = firstStop.Location.ShippingCountry
		} else {
			load.BillTo.Country = "US"
		}
		load.BillTo.Phone = firstStop.ShippingContact.Phone
		load.BillTo.Email = firstStop.ShippingContact.Email
		load.BillTo.Contact = contactName
	}

	// Populate rate data from Revenova API response (like other TMS integrations)
	// Only use actual values from API, do not make up calculations
	load.RateData.CustomerTotalCharge = models.ValueUnit{
		Val:  float32(loadResp.CargoValue),
		Unit: loadResp.CurrencyCode,
	}

	// Note: Revenova API doesn't provide carrier cost or profit data
	// Leave these fields as nil/zero instead of making up values
	// If the API provides these values in the future, they should be mapped here

	// Set declared value
	load.DeclaredValueUSD = float32(loadResp.CargoValue)

	// Populate stops array from Revenova API response (like other TMS integrations)
	load.Stops = make([]models.Stop, len(loadResp.Stops))
	for i, data := range loadResp.Stops {
		stopType := "delivery"
		if data.IsPickup {
			stopType = "pickup"
		}

		load.Stops[i] = models.Stop{
			StopType:   stopType,
			StopNumber: data.StopNumber - 1, // Convert to 0-based indexing
			Address: models.Address{
				AddressLine1: data.Location.ShippingAddress,
				City:         data.Location.ShippingCity,
				State:        data.Location.ShippingStateProvince,
				Zip:          data.Location.ShippingPostalCode,
				Country:      data.Location.ShippingCountry,
			},
			Contact:   strings.TrimSpace(data.ShippingContact.FirstName + " " + data.ShippingContact.LastName),
			Phone:     data.ShippingContact.Phone,
			Email:     data.ShippingContact.Email,
			RefNumber: data.Location.CompanyName, // Use company name as ref number
		}

		// Set ready time for pickup stops
		if data.IsPickup && data.ExpectedDate != nil {
			if readyTime, err := time.Parse("2006-01-02", *data.ExpectedDate); err == nil {
				load.Stops[i].ReadyTime = models.NullTime{Time: readyTime, Valid: true}
			}
		}

		// Set must deliver for delivery stops
		if data.IsDropOff && data.ExpectedDate != nil {
			if mustDeliver, err := time.Parse("2006-01-02", *data.ExpectedDate); err == nil {
				load.Stops[i].MustDeliver = models.NullTime{Time: mustDeliver, Valid: true}
			}
		}
	}

	// Process stops for pickup/consignee mapping
	for _, data := range loadResp.Stops {
		if data.IsPickup {
			// Handle pickup stop
			if data.PickupDeliveryNumber != nil {
				load.Pickup.ExternalTMSID = *data.PickupDeliveryNumber
			}

			// Set contact information - use company name as the main name
			load.Pickup.Name = data.Location.CompanyName
			load.Pickup.Email = data.ShippingContact.Email
			load.Pickup.Phone = data.ShippingContact.Phone
			fullName := data.ShippingContact.FirstName + " " + data.ShippingContact.LastName
			load.Pickup.Contact = strings.TrimSpace(fullName)

			// Set address information
			load.Pickup.AddressLine1 = data.Location.ShippingAddress
			load.Pickup.City = data.Location.ShippingCity
			load.Pickup.State = data.Location.ShippingStateProvince
			// Default to "US" if country is empty
			if data.Location.ShippingCountry != "" {
				load.Pickup.Country = data.Location.ShippingCountry
			} else {
				load.Pickup.Country = "US"
			}
			load.Pickup.Zipcode = data.Location.ShippingPostalCode

			// Process reference number if available
			if data.References != nil {
				re := regexp.MustCompile(`\d+`)
				matches := re.FindAllString(*data.References, -1)
				if len(matches) > 0 {
					load.Pickup.RefNumber = matches[len(matches)-1]
				}
			}

			// Set timezone
			var err error
			load.Pickup.Timezone, err = timezone.GetTimezone(
				ctx,
				data.Location.ShippingCity,
				data.Location.ShippingStateProvince,
				data.Location.ShippingCountry,
			)
			if err != nil {
				log.Warn(ctx, "error in finding timezone according to location details",
					zap.String("city", data.Location.ShippingCity),
					zap.String("state", data.Location.ShippingStateProvince),
					zap.String("country", data.Location.ShippingCountry),
					zap.Error(err))
			}

			// Set business hours if available
			if data.ShippingReceivingHours != "" {
				load.Pickup.BusinessHours = data.ShippingReceivingHours
			}

			// Parse appointment time
			load.Pickup.ApptStartTime, load.Pickup.ApptEndTime, err = parseAppointmentTime(
				ctx,
				data.AppointmentTime,
				data.ExpectedDate,
			)
			if err != nil {
				log.WarnNoSentry(
					ctx,
					"error in parsing pickup appointment time",
					zap.String("appointmentTime", data.AppointmentTime),
					zap.Error(err),
				)
			}

			// Parse expected pickup time
			if data.ExpectedDate != nil {
				expectedPickupTime, err := time.Parse(time.DateOnly, *data.ExpectedDate)
				if err != nil {
					log.Warn(
						ctx,
						"unable to parse expected pickup date",
						zap.String("dateString", *data.ExpectedDate),
						zap.Error(err),
					)
				} else {
					load.Pickup.ReadyTime = models.NullTime{
						Time:  expectedPickupTime,
						Valid: true,
					}

					load.Carrier.ExpectedPickupTime = models.NullTime{
						Time:  expectedPickupTime,
						Valid: true,
					}
				}
			}

			// Set appointment notes if available
			if data.Instructions != nil {
				load.Pickup.ApptNote = *data.Instructions
			}
		} else if data.IsDropOff {
			// Handle delivery stop (consignee)
			// Set consignee information - use company name as the main name
			load.Consignee.Name = data.Location.CompanyName
			load.Consignee.Email = data.ShippingContact.Email
			load.Consignee.Phone = data.ShippingContact.Phone
			load.Consignee.Contact = strings.TrimSpace(
				data.ShippingContact.FirstName + " " + data.ShippingContact.LastName)
			load.Consignee.AddressLine1 = data.Location.ShippingAddress
			load.Consignee.City = data.Location.ShippingCity
			load.Consignee.State = data.Location.ShippingStateProvince
			// Default to "US" if country is empty
			if data.Location.ShippingCountry != "" {
				load.Consignee.Country = data.Location.ShippingCountry
			} else {
				load.Consignee.Country = "US"
			}
			load.Consignee.Zipcode = data.Location.ShippingPostalCode

			// Set timezone
			var err error
			load.Consignee.Timezone, err = timezone.GetTimezone(
				ctx,
				data.Location.ShippingCity,
				data.Location.ShippingStateProvince,
				data.Location.ShippingCountry,
			)
			if err != nil {
				log.Warn(
					ctx,
					"error in finding consignee timezone",
					zap.String("city", data.Location.ShippingCity),
					zap.String("state", data.Location.ShippingStateProvince),
					zap.String("country", data.Location.ShippingCountry),
					zap.Error(err),
				)
			}

			// Set business hours if available
			if data.ShippingReceivingHours != "" {
				load.Consignee.BusinessHours = data.ShippingReceivingHours
			}

			// Process reference number if available
			if data.References != nil {
				re := regexp.MustCompile(`\d+`)
				matches := re.FindAllString(*data.References, -1)
				if len(matches) > 0 {
					load.Consignee.RefNumber = matches[len(matches)-1]
				}
			}

			// Parse appointment time for consignee
			load.Consignee.ApptStartTime, load.Consignee.ApptEndTime, err = parseAppointmentTime(
				ctx,
				data.AppointmentTime,
				data.ExpectedDate,
			)
			if err != nil {
				log.WarnNoSentry(
					ctx,
					"error in parsing consignee appointment time",
					zap.String("appointmentTime", data.AppointmentTime),
					zap.Error(err),
				)
			}

			// Set appointment notes if available
			if data.Instructions != nil {
				load.Consignee.ApptNote = *data.Instructions
			}

			// Set carrier information
			if loadResp.Carrier != "" {
				load.Carrier.Name = loadResp.Carrier
			}

			// Set carrier status and notes if available
			if data.CarrierStatus != nil {
				// Store carrier status in notes (Revenova specific field)
				load.Carrier.Notes = *data.CarrierStatus
			}

			// Parse departure time
			if data.DepartureTime != nil {
				if dispatchTime, ok := parseDepartureTime(ctx, *data.DepartureTime, data.DepartureDate); ok {
					load.Carrier.DispatchedTime = models.NullTime{
						Time:  dispatchTime,
						Valid: true,
					}
				}
			}

			// Parse carrier ETA if available
			if data.CarrierEtaDate != nil && *data.CarrierEtaDate != "" {
				if data.CarrierEtaTime != nil && *data.CarrierEtaTime != "" {
					// Combine date and time
					etaDateTime, err := time.Parse(dateTimeFormat, *data.CarrierEtaDate+" "+*data.CarrierEtaTime)
					if err == nil {
						// Store in carrier notes or a custom field
						load.Carrier.DispatchedTime = models.NullTime{
							Time:  etaDateTime,
							Valid: true,
						}
					}
				}
			}

			// Parse expected delivery time
			if data.ExpectedDate != nil {
				expectedDate, err := time.Parse(time.DateOnly, *data.ExpectedDate)
				if err != nil {
					log.Warn(
						ctx, "unable to parse expected delivery date",
						zap.String("expectedDate", *data.ExpectedDate),
						zap.Error(err),
					)
					continue
				}

				load.Consignee.MustDeliver = models.NullTime{
					Time:  expectedDate,
					Valid: true,
				}

				// Set expected delivery time - combine with appointment time if available
				deliveryTime := expectedDate
				if data.AppointmentTime != "" {
					if apptTime, err := time.Parse(timeOnlyWithoutSecondsFormat, data.AppointmentTime); err == nil {
						// Combine date and time
						deliveryTime = time.Date(
							expectedDate.Year(), expectedDate.Month(), expectedDate.Day(),
							apptTime.Hour(), apptTime.Minute(), 0, 0,
							expectedDate.Location(),
						)
					}
				}
				load.Carrier.ExpectedDeliveryTime = models.NullTime{
					Time:  deliveryTime,
					Valid: true,
				}
			}
		}
	}

	// Populate commodities from line items
	load.Commodities = make([]models.Commodity, len(loadResp.LineItems))
	load.Specifications.NumCommodities = len(loadResp.LineItems)
	for i, lineItem := range loadResp.LineItems {
		load.Commodities[i] = models.Commodity{
			Description:       lineItem.ItemDescription,
			HandlingQuantity:  lineItem.HandlingUnitCount,
			PackagingType:     lineItem.HandlingUnits,
			WeightTotal:       lineItem.Weight,
			HazardousMaterial: lineItem.HazardousMaterials,
			FreightClass:      lineItem.NmfcClass,
			NMFC:              lineItem.NmfcNumber,
			Length:            lineItem.HandlingUnitLength,
			Width:             lineItem.HandlingUnitWidth,
			Height:            lineItem.HandlingUnitHeight,
		}

		// Set hazmat if any line item has it
		if lineItem.HazMatNumber != nil && *lineItem.HazMatNumber != "" {
			load.Specifications.Hazmat = true
		}
	}

	// Process accessorials
	for _, spec := range loadResp.Accessorials {
		switch spec.AccessorialName {
		case "Lift Gate Pick Up", "Liftgate Pickup":
			load.Specifications.LiftgatePickup = true
		case "Lift Gate Delivery", "Liftgate Delivery":
			load.Specifications.LiftgateDelivery = true
		case "Inside Pick Up", "Inside Pickup":
			load.Specifications.InsidePickup = true
		case "Inside Delivery":
			load.Specifications.InsideDelivery = true
		case "Extra Labor Pick Up", "Extra Labor Delivery":
			load.Specifications.Labor = true
		}
	}

	return load
}

func (r Revenova) DrumkitLoadToRevenovaLoad(load models.Load) (req PostLoad) {
	// pickup
	pickupNames := strings.Split(load.Pickup.Name, " ")

	// Extract pickup date from the load data
	var pickupExpectedDate *string
	var pickupApptTime *string

	// Priority order: ApptStartTime > ReadyTime > current date
	switch {
	case load.Pickup.ApptStartTime.Valid:
		date := load.Pickup.ApptStartTime.Time.Format(time.DateOnly)
		pickupExpectedDate = &date
		// Extract time component for appointment
		timeStr := load.Pickup.ApptStartTime.Time.Format(timeOnlyWithoutSecondsFormat)
		pickupApptTime = &timeStr
	case load.Pickup.ReadyTime.Valid:
		date := load.Pickup.ReadyTime.Time.Format(time.DateOnly)
		pickupExpectedDate = &date
	default:
		// Fallback to current date if no date provided
		date := time.Now().Format(time.DateOnly)
		pickupExpectedDate = &date
	}

	// Parse contact name if provided, otherwise use location name as fallback
	pickupFirstName := pickupNames[0]
	pickupLastName := helpers.Or(func() string {
		if len(pickupNames) > 1 {
			return pickupNames[1]
		}
		return pickupNames[0]
	}(), pickupNames[0])

	if len(pickupNames) > 0 {
		pickupFirstName = pickupNames[0]
		if len(pickupNames) > 1 {
			pickupLastName = pickupNames[1]
		} else {
			pickupLastName = pickupNames[0] // Use last part of location name
		}
	}

	pickup := Stop{
		StopNumber:             1,                        // required
		ShippingReceivingHours: "09:00-22:00",            // required
		IsPickup:               true,                     // required
		IsDropOff:              false,                    // required
		IsGeolocation:          false,                    // optional
		ExpectedDate:           pickupExpectedDate,       // required - use actual pickup date
		AppointmentTime:        pickupApptTime,           // optional - use actual appointment time
		AppointmentRequired:    load.Pickup.ApptRequired, // optional
		ShippingContact: ShippingContact{
			Phone:     load.Pickup.Phone, // Use empty string if phone is empty
			Email:     load.Pickup.Email, // Use empty string if email is empty
			FirstName: pickupFirstName,   // Parse contact name or use location name
			LastName:  pickupLastName,    // Parse contact name or use location name
		},
		Location: Location{
			ShippingCity:          load.Pickup.City,                      // required
			ShippingStateProvince: load.Pickup.State,                     // required
			ShippingCountry:       helpers.Or(load.Pickup.Country, "US"), // required
			ShippingPostalCode:    load.Pickup.Zipcode,                   // required
			ShippingAddress:       load.Pickup.AddressLine1,
			// Use customer name for pickup location company name (this is how Revenova identifies the customer)
			CompanyName: helpers.Or(load.Customer.Name, helpers.Or(load.Pickup.Name, "Pickup Location")), // required
		},
	}

	// carrier/delivery stop
	carrierNames := strings.Split(load.Carrier.Name, " ")

	// Extract delivery date from the load data
	var deliveryExpectedDate *string
	var deliveryApptTime *string

	// Priority order: Consignee.ApptStartTime > Consignee.MustDeliver > Carrier.ExpectedDeliveryTime > pickup date
	switch {
	case load.Consignee.ApptStartTime.Valid:
		date := load.Consignee.ApptStartTime.Time.Format(time.DateOnly)
		deliveryExpectedDate = &date
		// Extract time component for appointment
		timeStr := load.Consignee.ApptStartTime.Time.Format(timeOnlyWithoutSecondsFormat)
		deliveryApptTime = &timeStr
	case load.Consignee.MustDeliver.Valid:
		date := load.Consignee.MustDeliver.Time.Format(time.DateOnly)
		deliveryExpectedDate = &date
	case load.Carrier.ExpectedDeliveryTime.Valid:
		date := load.Carrier.ExpectedDeliveryTime.Time.Format(time.DateOnly)
		deliveryExpectedDate = &date
	default:
		// Fallback to pickup date if no delivery date provided
		deliveryExpectedDate = pickupExpectedDate
	}

	// Parse contact name if provided, otherwise use location name as fallback
	consigneeContactParts := strings.Fields(strings.TrimSpace(load.Consignee.Contact))
	consigneeFirstName := carrierNames[0]
	consigneeLastName := helpers.Or(func() string {
		if len(carrierNames) > 1 {
			return carrierNames[1]
		}
		return carrierNames[0]
	}(), carrierNames[0])

	if len(consigneeContactParts) > 0 {
		consigneeFirstName = consigneeContactParts[0]
		if len(consigneeContactParts) > 1 {
			consigneeLastName = consigneeContactParts[1]
		} else {
			consigneeLastName = carrierNames[len(carrierNames)-1] // Use last part of carrier name
		}
	}

	consignee := Stop{
		StopNumber:             2,                           // required
		ShippingReceivingHours: "09:00-22:00",               // required
		IsPickup:               false,                       // required
		IsDropOff:              true,                        // required
		IsGeolocation:          false,                       // optional
		ExpectedDate:           deliveryExpectedDate,        // required - use actual delivery date
		AppointmentTime:        deliveryApptTime,            // optional - use actual appointment time
		AppointmentRequired:    load.Consignee.ApptRequired, // optional
		ShippingContact: ShippingContact{
			Phone:     load.Consignee.Phone, // Use empty string if phone is empty
			Email:     load.Consignee.Email, // Use empty string if email is empty
			FirstName: consigneeFirstName,   // Parse contact name or use carrier name
			LastName:  consigneeLastName,    // Parse contact name or use carrier name
		},
		Location: Location{
			// required - use consignee city, not carrier dispatch city
			ShippingCity: load.Consignee.City,
			// required - use consignee state, not carrier dispatch state
			ShippingStateProvince: load.Consignee.State,
			ShippingCountry:       helpers.Or(load.Consignee.Country, "US"), // required
			ShippingPostalCode:    load.Consignee.Zipcode,                   // required
			ShippingAddress:       load.Consignee.AddressLine1,
			// required - use customer name for delivery location company name
			CompanyName: helpers.Or(load.Customer.Name,
				helpers.Or(load.Consignee.Name, "Delivery Location")),
		},
	}

	// Get weight from TotalWeight - handle both "val" and "value" fields
	weight := load.Specifications.TotalWeight.Val

	// If Val is 0 but ValueStr string is provided, parse the string value
	if weight == 0 && load.Specifications.TotalWeight.ValueStr != "" {
		if val, err := strconv.ParseFloat(load.Specifications.TotalWeight.ValueStr, 32); err == nil {
			weight = float32(val)
		}
	}

	// Use commodity data if provided, otherwise use specifications
	var lineItem LineItem
	if len(load.Commodities) > 0 {
		// Use first commodity data
		commodity := load.Commodities[0]
		lineItem = LineItem{
			WeightUnits:         "lbs",
			Weight:              commodity.WeightTotal,
			Stackable:           false,
			Turnable:            false,
			ScheduleBCode:       "",
			PickupStopNumber:    1,
			PackagingUnits:      "",
			PackagingUnitCount:  commodity.Quantity,
			NmfcNumber:          commodity.NMFC,
			NmfcClass:           helpers.Or(commodity.FreightClass, "50"),
			LinearFeet:          0.0,
			ItemNumber:          helpers.Or(commodity.ReferenceNumber, "Item 1"),
			ItemDescription:     helpers.Or(commodity.Description, "Freight"),
			HtsCode:             commodity.HarmonizedCode,
			HsCode:              "",
			HazMatPackingGroup:  nil,
			HazMatNumber:        nil,
			HazMatContact:       nil,
			HazMatClassDivision: nil,
			HazardousMaterials:  commodity.HazardousMaterial,
			HandlingUnitWidth:   commodity.Width,
			HandlingUnits:       helpers.Or(commodity.PackagingType, "Pallets"),
			HandlingUnitLength:  commodity.Length,
			HandlingUnitHeight:  commodity.Height,
			HandlingUnitCount:   commodity.HandlingQuantity,
			DimensionUnits:      "in",
			DeliveryStopNumber:  2,
		}
	} else {
		// Use specifications if no commodities provided
		handlingUnitCount := load.Specifications.TotalOutPalletCount
		if handlingUnitCount == 0 {
			handlingUnitCount = 1 // Default
		}

		// Parse total pieces
		totalPieces := 0
		if load.Specifications.TotalPieces.ValueStr != "" {
			if val, err := strconv.Atoi(load.Specifications.TotalPieces.ValueStr); err == nil {
				totalPieces = val
			}
		}

		lineItem = LineItem{
			WeightUnits:         "lbs",
			Weight:              float64(weight),
			Stackable:           false,
			Turnable:            false,
			ScheduleBCode:       "",
			PickupStopNumber:    1,
			PackagingUnits:      "",
			PackagingUnitCount:  totalPieces,
			NmfcNumber:          "",
			NmfcClass:           "50",
			LinearFeet:          0.0,
			ItemNumber:          "Item 1",
			ItemDescription:     helpers.Or(load.Specifications.Commodities, "Freight"),
			HtsCode:             "",
			HsCode:              "",
			HazMatPackingGroup:  nil,
			HazMatNumber:        nil,
			HazMatContact:       nil,
			HazMatClassDivision: nil,
			HazardousMaterials:  load.Specifications.Hazmat,
			HandlingUnitWidth:   0, // Do not hardcode dimensions - leave as 0 if not provided
			HandlingUnits:       "Pallets",
			HandlingUnitLength:  0,                 // Do not hardcode dimensions - leave as 0 if not provided
			HandlingUnitHeight:  0,                 // Do not hardcode dimensions - leave as 0 if not provided
			HandlingUnitCount:   handlingUnitCount, // Use actual pallet count from request
			DimensionUnits:      "in",
			DeliveryStopNumber:  2,
		}
	}

	req.Wsl.WeightUnits = "lbs"                                        // required
	req.Wsl.TotalWeight = int(weight)                                  // required - use the same weight value
	req.Wsl.TemperatureControlled = load.Specifications.IsRefrigerated // required - use actual value
	req.Wsl.Stops = []Stop{pickup, consignee}                          // required
	req.Wsl.ProNumber = nil                                            // optional
	req.Wsl.PoNumber = nil                                             // optional
	req.Wsl.OrderDate = *pickupExpectedDate                            // required - use actual pickup date
	// Map Drumkit load mode to Revenova mode name
	// Support both short codes ("TL") and full names ("Truckload") from frontend
	var modeName string
	modeStr := strings.ToLower(strings.TrimSpace(string(load.Mode)))

	switch modeStr {
	case "tl", "truckload", "ftl", "full truckload":
		modeName = "Truckload"
	case "ltl", "less than truckload":
		modeName = "LTL"
	case "refrigerated", "reefer":
		modeName = "Reefer"
	case "flatbed", "flat bed":
		modeName = "Flatbed"
	case "dry van", "dryvan", "van":
		modeName = "Dry Van"
	default:
		// Check exact match with constants
		switch load.Mode {
		case models.TLMode:
			modeName = "Truckload"
		case models.LTLMode:
			modeName = "LTL"
		case models.RefrigeratedMode:
			modeName = "Reefer"
		case models.FlatbedMode:
			modeName = "Flatbed"
		case models.DryVanMode:
			modeName = "Dry Van"
		default:
			modeName = "LTL" // fallback
		}
	}
	req.Wsl.ModeName = modeName // required
	// Use default load description
	req.Wsl.LoadPostingDescription = "Load created via Drumkit API" // optional
	req.Wsl.LoadNumber = "Temp Load Name"                           // required
	req.Wsl.LineItems = []LineItem{lineItem}                        // required
	req.Wsl.LinearFeet = nil                                        // optional
	req.Wsl.HazardousMaterials = load.Specifications.Hazmat         // Use actual value from load
	// Use actual values from load data instead of hardcoded values
	req.Wsl.DistanceMiles = 0            // Leave as 0 if not provided - do not hardcode
	req.Wsl.DistanceKilometers = 0       // Leave as 0 if not provided - do not hardcode
	req.Wsl.CargoValue = 0               // Leave as 0 if not provided - do not hardcode
	req.Wsl.ContainerTrailerNumber = ""  // Leave empty if not provided - do not hardcode
	req.Wsl.BillOfLadingNumber = nil     // optional
	req.Wsl.Accessorials = []Accessory{} // optional

	return req
}

// getBestAddress returns the best available address from a Salesforce Account
// Prefers Billing address, falls back to Shipping address
func (r *Revenova) getBestAddress(account SalesforceAccount) struct {
	Street  string
	City    string
	State   string
	Zipcode string
	Country string
} {
	result := struct {
		Street  string
		City    string
		State   string
		Zipcode string
		Country string
	}{}

	// Prefer Billing address first
	result.Street = account.BillingStreet
	result.City = account.BillingCity
	result.State = account.BillingStateCode
	if result.State == "" {
		result.State = account.BillingState
	}
	result.Zipcode = account.BillingPostalCode
	result.Country = account.BillingCountryCode
	if result.Country == "" {
		result.Country = account.BillingCountry
	}

	// Fallback to Shipping address if Billing is empty
	if result.Street == "" {
		result.Street = account.ShippingStreet
	}
	if result.City == "" {
		result.City = account.ShippingCity
	}
	if result.State == "" {
		result.State = account.ShippingStateCode
		if result.State == "" {
			result.State = account.ShippingState
		}
	}
	if result.Zipcode == "" {
		result.Zipcode = account.ShippingPostalCode
	}
	if result.Country == "" {
		result.Country = account.ShippingCountryCode
		if result.Country == "" {
			result.Country = account.ShippingCountry
		}
	}

	// Final fallback for country
	if result.Country == "" {
		result.Country = "US"
	}

	return result
}

// getCustomerIDFromLoad queries the load record to get the associated customer ID
func (r *Revenova) getCustomerIDFromLoad(ctx context.Context, loadID string) (string, error) {
	// Query the load record to get the customer ID
	soqlQuery := fmt.Sprintf("SELECT rtms__Customer__c FROM rtms__Load__c WHERE Id='%s' LIMIT 1", loadID)

	queryParams := make(url.Values)
	queryParams.Set("q", soqlQuery)

	type LoadCustomerResponse struct {
		TotalSize int  `json:"totalSize"`
		Done      bool `json:"done"`
		Records   []struct {
			CustomerID string `json:"rtms__Customer__c"`
		} `json:"records"`
	}

	var response LoadCustomerResponse
	if err := r.get(ctx, salesforceQueryPath, queryParams, &response, s3backup.TypeLoads); err != nil {
		return "", fmt.Errorf("failed to query customer ID from load: %w", err)
	}

	if len(response.Records) == 0 || response.Records[0].CustomerID == "" {
		return "", fmt.Errorf("no customer ID found for load: %s", loadID)
	}

	log.Info(ctx, "Found customer ID from load record",
		zap.String("loadID", loadID),
		zap.String("customerID", response.Records[0].CustomerID))

	return response.Records[0].CustomerID, nil
}

// UpdateLoadCustomer updates a load with customer information using PATCH
func (r *Revenova) UpdateLoadCustomer(ctx context.Context, loadID, customerID string) error {
	ctx, metaSpan := otel.StartSpan(ctx, "UpdateLoadCustomerRevenova", otel.IntegrationAttrs(r.tms))
	var err error
	defer func() { metaSpan.End(err) }()

	log.Info(ctx, "updating load with customer",
		zap.String("loadID", loadID),
		zap.String("customerID", customerID))

	// Prepare the PATCH request body
	patchData := map[string]string{
		"rtms__Customer__c": customerID,
	}

	// Make PATCH request to update the load using the existing patch method
	path := fmt.Sprintf("%s/%s", salesforceLoadObjectPath, loadID)

	log.Info(ctx, "sending PATCH request to update load customer",
		zap.String("path", path),
		zap.String("customerID", customerID))

	// Use the existing patch method from revenova.go
	err = r.patch(ctx, path, nil, patchData, nil, nil, s3backup.TypeLoads)
	if err != nil {
		log.Error(ctx, "failed to update load customer", zap.Error(err))
		return fmt.Errorf("failed to update load customer: %w", err)
	}

	log.Info(ctx, "successfully updated load with customer",
		zap.String("loadID", loadID),
		zap.String("customerID", customerID))

	return nil
}

// isSalesforceID checks if a string is a Salesforce ID
// Salesforce IDs are 15 or 18 characters, alphanumeric, case-sensitive
func (r *Revenova) isSalesforceID(id string) bool {
	// Salesforce IDs are either 15 or 18 characters long
	if len(id) != 15 && len(id) != 18 {
		return false
	}

	// Check if all characters are alphanumeric
	for _, char := range id {
		if (char < 'a' || char > 'z') && (char < 'A' || char > 'Z') && (char < '0' || char > '9') {
			return false
		}
	}

	return true
}

// getLoadIDByLoadNumber queries Salesforce to find the Load ID by the load number (Name field)
func (r *Revenova) getLoadIDByLoadNumber(ctx context.Context, loadNumber string) (string, error) {
	// Build SOQL query to find load by Name field
	soqlQuery := fmt.Sprintf("SELECT Id FROM rtms__Load__c WHERE Name='%s' LIMIT 1", loadNumber)

	queryParams := make(url.Values)
	queryParams.Set("q", soqlQuery)

	type LoadIDResponse struct {
		TotalSize int  `json:"totalSize"`
		Done      bool `json:"done"`
		Records   []struct {
			ID string `json:"Id"`
		} `json:"records"`
	}

	var response LoadIDResponse
	if err := r.get(ctx, salesforceQueryPath, queryParams, &response, s3backup.TypeLoads); err != nil {
		return "", fmt.Errorf("failed to query load by number: %w", err)
	}

	if len(response.Records) == 0 || response.Records[0].ID == "" {
		return "", fmt.Errorf("no load found with number: %s", loadNumber)
	}

	log.Info(ctx, "Found Load ID by load number",
		zap.String("loadNumber", loadNumber),
		zap.String("loadID", response.Records[0].ID))

	return response.Records[0].ID, nil
}
