package revenova

import (
	tmsutil "github.com/drumkitai/drumkit/common/integrations/tms/util"
	"github.com/drumkitai/drumkit/common/models"
)

// DefaultLoadAttributes defines the default field attributes for Revenova TMS
var DefaultLoadAttributes = models.LoadAttributes{
	FreightTrackingID: models.FieldAttributes{IsReadOnly: true},
	LoadCoreInfoAttributes: models.LoadCoreInfoAttributes{
		Status:           models.FieldAttributes{IsReadOnly: false},
		Mode:             models.FieldAttributes{IsReadOnly: false},
		MoreThanTwoStops: models.FieldAttributes{IsNotSupported: true},
		PONums:           models.FieldAttributes{IsReadOnly: false},
		Operator:         models.FieldAttributes{IsNotSupported: true},

		Customer: models.CustomerAttributes{
			CompanyCoreInfoAttributes: models.CompanyCoreInfoAttributes{
				Name:         models.FieldAttributes{IsReadOnly: false},
				AddressLine1: models.FieldAttributes{IsReadOnly: false},
				AddressLine2: models.FieldAttributes{IsReadOnly: false},
				City:         models.FieldAttributes{IsReadOnly: false},
				State:        models.FieldAttributes{IsReadOnly: false},
				Zipcode:      models.FieldAttributes{IsReadOnly: false},
				Country:      models.FieldAttributes{IsReadOnly: false},
				Phone:        models.FieldAttributes{IsReadOnly: false},
				Email:        models.FieldAttributes{IsReadOnly: false},
				Contact:      models.FieldAttributes{IsReadOnly: false},
			},
		},

		Pickup: models.PickupAttributes{
			CompanyCoreInfoAttributes: models.CompanyCoreInfoAttributes{
				Name:         models.FieldAttributes{IsReadOnly: false},
				AddressLine1: models.FieldAttributes{IsReadOnly: false},
				AddressLine2: models.FieldAttributes{IsReadOnly: false},
				City:         models.FieldAttributes{IsReadOnly: false},
				State:        models.FieldAttributes{IsReadOnly: false},
				Zipcode:      models.FieldAttributes{IsReadOnly: false},
				Country:      models.FieldAttributes{IsReadOnly: false},
				Phone:        models.FieldAttributes{IsReadOnly: false},
				Email:        models.FieldAttributes{IsReadOnly: false},
				Contact:      models.FieldAttributes{IsReadOnly: false},
			},
		},

		Consignee: models.ConsigneeAttributes{
			CompanyCoreInfoAttributes: models.CompanyCoreInfoAttributes{
				Name:         models.FieldAttributes{IsReadOnly: false},
				AddressLine1: models.FieldAttributes{IsReadOnly: false},
				AddressLine2: models.FieldAttributes{IsReadOnly: false},
				City:         models.FieldAttributes{IsReadOnly: false},
				State:        models.FieldAttributes{IsReadOnly: false},
				Zipcode:      models.FieldAttributes{IsReadOnly: false},
				Country:      models.FieldAttributes{IsReadOnly: false},
				Phone:        models.FieldAttributes{IsReadOnly: false},
				Email:        models.FieldAttributes{IsReadOnly: false},
				Contact:      models.FieldAttributes{IsReadOnly: false},
			},
		},

		Carrier: models.CarrierAttributes{
			Name:  models.FieldAttributes{IsReadOnly: false},
			Phone: models.FieldAttributes{IsReadOnly: false},
			Email: models.FieldAttributes{IsReadOnly: false},
		},

		Specifications: models.SpecificationsAttributes{
			TotalWeight:   models.FieldAttributes{IsReadOnly: false},
			TotalVolume:   models.FieldAttributes{IsNotSupported: true},
			Commodities:   models.FieldAttributes{IsReadOnly: false},
			Hazmat:        models.FieldAttributes{IsReadOnly: false},
			TransportType: models.FieldAttributes{IsReadOnly: false},
		},
	},
}

// GetDefaultLoadAttributes returns the default load attributes for Revenova
func (r Revenova) GetDefaultLoadAttributes() models.LoadAttributes {
	attrs := DefaultLoadAttributes
	tmsutil.ApplyTMSFeatureFlags(&r.tms, &attrs)
	return attrs
}
