package revenova

import (
	"context"
	"fmt"
	"net/url"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	tmsCustomerDB "github.com/drumkitai/drumkit/common/rds/tms_customers"
	"github.com/drumkitai/drumkit/common/redis"
)

const (
	salesforceQueryPath = "/services/data/v59.0/query"
	customerBatchSize   = 200 // Salesforce FIELDS function limit
)

// GetCustomers retrieves customers from Salesforce/Revenova API
// It handles pagination properly using Salesforce's nextRecordsUrl
func (r *Revenova) GetCustomers(ctx context.Context) ([]models.TMSCustomer, error) {
	ctx, metaSpan := otel.StartSpan(ctx, "GetCustomersRevenova", otel.IntegrationAttrs(r.tms))
	var err error
	defer func() { metaSpan.End(err) }()

	log.Info(ctx, "revenova.GetCustomers - fetching customers from Salesforce API")

	// First, get the total count of customers to know how many we expect
	totalCount, err := r.getCustomerCount(ctx)
	if err != nil {
		log.Error(ctx, "failed to get customer count", zap.Error(err))
		return nil, err
	}

	log.Info(ctx, "total customers in Salesforce", zap.Int("totalCount", totalCount))

	if totalCount == 0 {
		log.Info(ctx, "no customers found in Salesforce")
		return []models.TMSCustomer{}, nil
	}

	var allCustomers []models.TMSCustomer
	var lastID string // Cursor for pagination
	batchNumber := 0

	// Use cursor-based pagination with Id (NO OFFSET LIMIT!)
	// This approach works for unlimited records by using WHERE Id > 'lastId'
	for {
		batchNumber++
		log.Info(ctx, "fetching customer batch",
			zap.Int("batchNumber", batchNumber),
			zap.Int("fetchedSoFar", len(allCustomers)),
			zap.Int("totalExpected", totalCount),
			zap.String("lastID", lastID))

		// Fetch batch using cursor (lastID)
		customers, err := r.getCustomerBatchWithCursor(ctx, lastID, customerBatchSize)
		if err != nil {
			log.Error(ctx, "error fetching customer batch", zap.Error(err))
			// Save state for resuming
			if err := redis.SetIntegrationState(
				ctx,
				r.tms.ID,
				redis.CustomerJob,
				"",
				lastID,
			); err != nil {
				log.Error(ctx, "failed to set integration state", zap.Error(err))
			}
			return allCustomers, fmt.Errorf("failed to get customer batch: %w", err)
		}

		log.Info(ctx, "received customer batch",
			zap.Int("count", len(customers)))

		if len(customers) == 0 {
			log.Info(ctx, "no more customers - pagination complete!")
			break
		}

		// Convert to TMSCustomer models
		tmsCustomers := make([]models.TMSCustomer, 0, len(customers))
		for _, customer := range customers {
			tmsCustomer := ToCustomerModel(r.tms.ID, customer)
			tmsCustomers = append(tmsCustomers, tmsCustomer)
		}

		log.Info(ctx, "upserting customer batch to database", zap.Int("count", len(tmsCustomers)))

		// Upsert customers to database in batches (this handles duplicates automatically)
		if err = tmsCustomerDB.RefreshTMSCustomers(ctx, &tmsCustomers); err != nil {
			log.Error(ctx, "failed to upsert customers to database", zap.Error(err))
			// Save state for resuming
			if err := redis.SetIntegrationState(
				ctx,
				r.tms.ID,
				redis.CustomerJob,
				"",
				lastID,
			); err != nil {
				log.Error(ctx, "failed to set integration state", zap.Error(err))
			}
			return allCustomers, fmt.Errorf("failed to upsert customers: %w", err)
		}

		allCustomers = append(allCustomers, tmsCustomers...)

		// Update cursor to the last ID from this batch
		if len(customers) > 0 {
			lastID = customers[len(customers)-1].ID
		}

		log.Info(ctx, "successfully processed customer batch",
			zap.Int("batchSize", len(customers)),
			zap.Int("totalProcessed", len(allCustomers)),
			zap.Int("remaining", totalCount-len(allCustomers)),
			zap.String("newLastID", lastID))

		// If we got fewer records than the limit, we're done
		if len(customers) < customerBatchSize {
			log.Info(ctx, "received partial batch - completed!",
				zap.Int("totalFetched", len(allCustomers)),
				zap.Int("totalExpected", totalCount))
			break
		}
	}

	// Clear redis state after successful completion
	if err := redis.DeleteKey(ctx, fmt.Sprintf("integration-id-%d-%s", r.tms.ID, redis.CustomerJob)); err != nil {
		log.Error(ctx, "failed to clear redis state after successful completion", zap.Error(err))
	}

	log.Info(ctx, "successfully retrieved all customers", zap.Int("count", len(allCustomers)))
	return allCustomers, nil
}

// getCustomerBatchWithCursor fetches a batch of customers using cursor-based pagination
// This avoids Salesforce's 2000 OFFSET limit by using WHERE Id > 'lastID'
func (r *Revenova) getCustomerBatchWithCursor(
	ctx context.Context,
	lastID string,
	limit int,
) ([]SalesforceAccount, error) {
	queryParams := make(url.Values)

	var soqlQuery string
	if lastID == "" {
		// First query - no WHERE clause
		soqlQuery = fmt.Sprintf(`SELECT Id, Name, Phone,
			BillingStreet, BillingCity, BillingState, BillingPostalCode, BillingCountry,
			ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, ShippingCountry,
			rtms__TMS_Type__c
			FROM Account
			ORDER BY Id ASC
			LIMIT %d`, limit)
	} else {
		// Subsequent queries - use WHERE Id > lastID
		soqlQuery = fmt.Sprintf(`SELECT Id, Name, Phone,
			BillingStreet, BillingCity, BillingState, BillingPostalCode, BillingCountry,
			ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, ShippingCountry,
			rtms__TMS_Type__c
			FROM Account
			WHERE Id > '%s'
			ORDER BY Id ASC
			LIMIT %d`, lastID, limit)
	}

	queryParams.Set("q", soqlQuery)

	log.Info(ctx, "executing Salesforce SOQL query (cursor-based)",
		zap.String("query", soqlQuery),
		zap.String("cursor", lastID),
		zap.Int("limit", limit))

	var resp SalesforceQueryResponse
	if err := r.get(ctx, salesforceQueryPath, queryParams, &resp, s3backup.TypeCustomers); err != nil {
		log.Error(ctx, "Salesforce query failed", zap.Error(err))
		return nil, fmt.Errorf("failed to query customers: %w", err)
	}

	log.Info(ctx, "Salesforce query response",
		zap.Int("totalSize", resp.TotalSize),
		zap.Int("recordCount", len(resp.Records)),
		zap.Bool("done", resp.Done))

	return resp.Records, nil
}

// getCustomerCount gets the total count of all accounts
func (r *Revenova) getCustomerCount(ctx context.Context) (int, error) {
	queryParams := make(url.Values)

	// Build the COUNT query - count ALL accounts
	soqlQuery := "SELECT COUNT() FROM Account"

	queryParams.Set("q", soqlQuery)

	log.Info(ctx, "getting customer count", zap.String("query", soqlQuery))

	var countResp SalesforceCountResponse
	if err := r.get(ctx, salesforceQueryPath, queryParams, &countResp, s3backup.TypeCustomers); err != nil {
		log.Error(ctx, "failed to get customer count", zap.Error(err))
		return 0, fmt.Errorf("failed to get customer count: %w", err)
	}

	// For COUNT queries, Salesforce returns the count in records[0].expr0
	if len(countResp.Records) > 0 {
		count := countResp.Records[0].Expr0
		log.Info(ctx, "customer count from Salesforce", zap.Int("count", count))
		return count, nil
	}

	// Fallback to totalSize if no records
	log.Info(ctx, "customer count from totalSize", zap.Int("count", countResp.TotalSize))
	return countResp.TotalSize, nil
}

// GetCustomerByID fetches a single customer by ID from Salesforce (like Turvo does)
// This is used to get complete customer details when the load API returns incomplete data
func (r *Revenova) GetCustomerByID(ctx context.Context, customerID string) (SalesforceAccount, error) {
	ctx, metaSpan := otel.StartSpan(ctx, "GetCustomerByIDRevenova", otel.IntegrationAttrs(r.tms))
	var err error
	defer func() { metaSpan.End(err) }()

	log.Info(ctx, "Fetching customer by ID from Salesforce", zap.String("customerID", customerID))

	// Use Salesforce REST API to query Account by ID
	// SELECT FIELDS(ALL) returns all available fields for the account
	soqlQuery := fmt.Sprintf("SELECT FIELDS(ALL) FROM Account WHERE Id='%s' LIMIT 1", customerID)

	queryParams := make(url.Values)
	queryParams.Set("q", soqlQuery)

	var response SalesforceQueryResponse
	if err := r.get(ctx, salesforceQueryPath, queryParams, &response, s3backup.TypeCustomers); err != nil {
		log.Error(ctx, "Failed to query customer from Salesforce", zap.Error(err))
		return SalesforceAccount{}, fmt.Errorf("failed to query customer: %w", err)
	}

	if len(response.Records) == 0 {
		return SalesforceAccount{}, fmt.Errorf("no customer found with ID: %s", customerID)
	}

	log.Info(ctx, "Successfully fetched customer from Salesforce",
		zap.String("customerName", response.Records[0].Name),
		zap.String("customerID", response.Records[0].ID))

	return response.Records[0], nil
}

// ToCustomerModel converts a Salesforce Account to a TMSCustomer model
func ToCustomerModel(tmsID uint, account SalesforceAccount) models.TMSCustomer {
	// Prefer shipping address, fallback to billing address
	addressLine1 := account.ShippingStreet
	city := account.ShippingCity
	state := account.ShippingStateCode
	if state == "" {
		state = account.ShippingState
	}
	zipcode := account.ShippingPostalCode
	country := account.ShippingCountryCode
	if country == "" {
		country = account.ShippingCountry
	}

	// Fallback to billing address if shipping is empty
	if addressLine1 == "" {
		addressLine1 = account.BillingStreet
	}
	if city == "" {
		city = account.BillingCity
	}
	if state == "" {
		state = account.BillingStateCode
		if state == "" {
			state = account.BillingState
		}
	}
	if zipcode == "" {
		zipcode = account.BillingPostalCode
	}
	if country == "" {
		country = account.BillingCountryCode
		if country == "" {
			country = account.BillingCountry
		}
	}

	// Determine email - prefer customer support, then billing contact
	email := account.CustomerSupportEmail
	if email == "" {
		email = account.BillingContactEmail
	}

	return models.TMSCustomer{
		TMSIntegrationID: tmsID,
		ExternalID:       account.ID,
		CompanyCoreInfo: models.CompanyCoreInfo{
			ExternalTMSID: account.ID,
			Name:          account.Name,
			AddressLine1:  addressLine1,
			City:          city,
			State:         state,
			Zipcode:       zipcode,
			Country:       country,
			Phone:         account.Phone,
			Email:         email,
		},
	}
}
