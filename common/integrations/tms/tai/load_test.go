package tai

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestMapStopToPickup(t *testing.T) {
	stop := Stop{
		CompanyName:     "Test Company",
		StreetAddress:   "123 Main St",
		City:            "Test City",
		State:           "TS",
		ZipCode:         "12345",
		Country:         "US",
		ContactName:     "<PERSON>",
		Phone:           "555-1234",
		Email:           "<EMAIL>",
		ReferenceNumber: "REF123",
		Notes:           "Test notes",
		StopType:        "First Pickup",
		ShipmentStopReferenceNumbers: []ReferenceNumber{
			{
				ReferenceType: "Assigned Branch",
				Value:         "BRANCH001",
			},
			{
				ReferenceType: "Reference Number",
				Value:         "REF456",
			},
		},
	}

	pickup := mapStopToPickup(stop)

	assert.Equal(t, "Test Company", pickup.Name)
	assert.Equal(t, "123 Main St", pickup.AddressLine1)
	assert.Equal(t, "Test City", pickup.City)
	assert.Equal(t, "TS", pickup.State)
	assert.Equal(t, "12345", pickup.Zipcode)
	assert.Equal(t, "US", pickup.Country)
	assert.Equal(t, "<PERSON>", pickup.Contact)
	assert.Equal(t, "555-1234", pickup.Phone)
	assert.Equal(t, "<EMAIL>", pickup.Email)
	assert.Equal(t, "REF123", pickup.RefNumber)
	assert.Equal(t, "Test notes", pickup.ApptNote)

	assert.Len(t, pickup.AdditionalReferences, 2)
	assert.Equal(t, "Assigned Branch", pickup.AdditionalReferences[0].Qualifier)
	assert.Equal(t, "BRANCH001", pickup.AdditionalReferences[0].Number)
	assert.Equal(t, "Reference Number", pickup.AdditionalReferences[1].Qualifier)
	assert.Equal(t, "REF456", pickup.AdditionalReferences[1].Number)
}

func TestMapStopToConsignee(t *testing.T) {
	stop := Stop{
		CompanyName:     "Test Consignee",
		StreetAddress:   "456 Oak Ave",
		City:            "Test City",
		State:           "TS",
		ZipCode:         "54321",
		Country:         "US",
		ContactName:     "Jane Smith",
		Phone:           "555-5678",
		Email:           "<EMAIL>",
		ReferenceNumber: "CONS123",
		Notes:           "Consignee notes",
		StopType:        "Last Drop",
		ShipmentStopReferenceNumbers: []ReferenceNumber{
			{
				ReferenceType: "Assigned Branch",
				Value:         "BRANCH002",
			},
		},
	}

	consignee := mapStopToConsignee(stop)

	assert.Equal(t, "Test Consignee", consignee.Name)
	assert.Equal(t, "456 Oak Ave", consignee.AddressLine1)
	assert.Equal(t, "Test City", consignee.City)
	assert.Equal(t, "TS", consignee.State)
	assert.Equal(t, "54321", consignee.Zipcode)
	assert.Equal(t, "US", consignee.Country)
	assert.Equal(t, "Jane Smith", consignee.Contact)
	assert.Equal(t, "555-5678", consignee.Phone)
	assert.Equal(t, "<EMAIL>", consignee.Email)
	assert.Equal(t, "CONS123", consignee.RefNumber)
	assert.Equal(t, "Consignee notes", consignee.ApptNote)

	assert.Len(t, consignee.AdditionalReferences, 1)
	assert.Equal(t, "Assigned Branch", consignee.AdditionalReferences[0].Qualifier)
	assert.Equal(t, "BRANCH002", consignee.AdditionalReferences[0].Number)
}

func TestMapStopToPickupWithoutReferences(t *testing.T) {
	stop := Stop{
		CompanyName:   "Test Company",
		StreetAddress: "123 Main St",
		City:          "Test City",
		State:         "TS",
		ZipCode:       "12345",
		Country:       "US",
		StopType:      "First Pickup",
	}

	pickup := mapStopToPickup(stop)

	assert.Equal(t, "Test Company", pickup.Name)
	assert.Equal(t, "123 Main St", pickup.AddressLine1)

	assert.Len(t, pickup.AdditionalReferences, 0)
}

func TestMapStopToConsigneeWithoutReferences(t *testing.T) {
	stop := Stop{
		CompanyName:   "Test Consignee",
		StreetAddress: "456 Oak Ave",
		City:          "Test City",
		State:         "TS",
		ZipCode:       "54321",
		Country:       "US",
		StopType:      "Last Drop",
	}

	consignee := mapStopToConsignee(stop)

	assert.Equal(t, "Test Consignee", consignee.Name)
	assert.Equal(t, "456 Oak Ave", consignee.AddressLine1)

	assert.Len(t, consignee.AdditionalReferences, 0)
}
