package relay

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"regexp"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/PuerkitoBio/goquery"
	"go.opentelemetry.io/otel/attribute"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/helpers/timezone"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
)

// When no reference date is available, use a reasonable window (this is for ready time and must deliver by years)
// Allow dates up to 30 days in the past and up to 365 days in the future
const (
	pastWindow   = -30 * 24 * time.Hour
	futureWindow = 365 * 24 * time.Hour
)

// NOTE: freightTrackingID can be either the Relay Load ID (PRO #)
// or the Booking ID
func (r *Relay) GetLoad(
	ctx context.Context,
	freightTrackingID string,
) (load models.Load, attrs models.LoadAttributes, err error) {

	spanAttrs := append(otel.IntegrationAttrs(r.tms), attribute.String("freight_tracking_id", freightTrackingID))

	ctx, metaSpan := otel.StartSpan(ctx, "GetLoadRelay", spanAttrs)
	defer func() { metaSpan.End(err) }()

	attrs = r.planningBoardAttributes()

	// check if load exists in TMS already
	foundLoad, err := loadDB.GetLoadByFreightIDAndTMSID(ctx, r.tms.ID, freightTrackingID)
	if err != nil {
		foundLoad, err = loadDB.GetLoadByExternalTMSIDAndTMSID(ctx, r.tms.ID, freightTrackingID)
		if err != nil {
			foundLoad, _, err = r.getLoadFromLoadBoard(ctx, freightTrackingID)
			if err != nil {
				log.Infof(ctx, "Relay load with ID %s not found on load board", freightTrackingID)
			}
		}
	}

	if len(foundLoad.FreightTrackingID) > 0 {
		load, err = r.parseLoadDetailHTML(ctx, foundLoad.ExternalTMSID)
		// NOTE: We intentionally do NOT preserve ready time and must deliver from foundLoad here
		// because the planning board modal (via tryGetDatesFromPlanningBoard) provides more
		// reliable date information with better year assignment logic
	} else {
		load, err = r.parseLoadDetailHTML(ctx, freightTrackingID)
	}

	if err != nil {
		return load, attrs, fmt.Errorf("parseLoadDetailHTML error: %w", err)
	}

	// Our understanding is if there is no carrier booked/operator assigned, reference numbers and rate details
	// are not shown on the load detail page. In that case, fetch PO nums from Load Board via websocket.
	// Some Optimization: Get from load board only if we're missing ref #s to avoid 3 lookups per fetch.
	if strings.TrimSpace(load.PONums) == "" && strings.TrimSpace(load.Customer.RefNumber) == "" {
		loadBoardRes, _, err := r.getLoadFromLoadBoard(ctx, load.ExternalTMSID)
		// Fail-open, but a 404 is *not* expected since the load should still be on the load board
		if err != nil {
			log.Warn(ctx, "error getting ref #s from load board", zap.Error(err))
		} else {
			load.PONums = loadBoardRes.PONums
			load.Customer.RefNumber = loadBoardRes.Customer.RefNumber
			load.Pickup.RefNumber = loadBoardRes.Pickup.RefNumber
			load.Consignee.RefNumber = loadBoardRes.Consignee.RefNumber
			// NOTE: We intentionally do NOT copy ready time and must deliver from load board here
			// because the planning board modal (via tryGetDatesFromPlanningBoard) provides more
			// reliable date information with better year assignment logic
		}

	}

	err = r.parseCarrierInfoHTML(ctx, &load)
	// It's possible for a load to be off the load board but not have a tracking page because the original carrier
	// was bounced (i.e. removed from the load).
	if err != nil && !errtypes.IsEntityNotFoundError(err) {
		return load, attrs, fmt.Errorf("parseCarrierInfoHTML error: %w", err)
	}
	attrs = r.trackingBoardAttributes()

	// Try to get ready time and deliver by date from planning board if not already set
	if !load.Pickup.ReadyTime.Valid || !load.Consignee.MustDeliver.Valid {
		if err := r.tryGetDatesFromPlanningBoard(ctx, &load); err != nil {
			log.WarnNoSentry(ctx, "Failed to get dates from planning board", zap.Error(err))
		}
	}

	return load, attrs, nil
}

// tryGetDatesFromPlanningBoard attempts to get the ready time and deliver by date from the planning board
// We need to manually replicate the planning board action to capture the WebSocket response
// that contains the modal data with the deliver by date
func (r *Relay) tryGetDatesFromPlanningBoard(ctx context.Context, load *models.Load) error {
	// Log available reference dates before parsing
	log.Debug(
		ctx,
		"Starting tryGetDatesFromPlanningBoard",
		zap.String("loadID", load.ExternalTMSID),
		zap.Bool("hasPickupReadyTime", load.Pickup.ReadyTime.Valid),
		zap.Bool("hasPickupApptStartTime", load.Pickup.ApptStartTime.Valid),
		zap.Bool("hasConsigneeMustDeliver", load.Consignee.MustDeliver.Valid),
		zap.Bool("hasConsigneeApptStartTime", load.Consignee.ApptStartTime.Valid),
	)

	// First, get planning board HTML to capture CSRF
	respBody, err := r.getHTML(ctx, "planning_board", nil, "")
	if err != nil {
		return fmt.Errorf("failed to get planning board HTML: %w", err)
	}

	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(respBody))
	if err != nil {
		return fmt.Errorf("failed to parse planning board HTML: %w", err)
	}

	csrf := strings.TrimSpace(doc.Find(`meta[name="csrf-token"]`).AttrOr("content", ""))
	if csrf == "" {
		return errors.New("parsed empty csrf token")
	}

	// Get WebSocket connection details
	phxMain := doc.Find(`[data-phx-main]`)
	phxSession := strings.TrimSpace(phxMain.AttrOr("data-phx-session", ""))
	phxStatic := strings.TrimSpace(phxMain.AttrOr("data-phx-static", ""))
	phxID := strings.TrimSpace(phxMain.AttrOr("id", ""))
	if phxID == "" {
		return errors.New("parsed empty phxID")
	}
	phxID = "lv:" + phxID

	// Set up WebSocket connection parameters
	queryParams := url.Values{}
	queryParams.Set("timezone", "America/New_York")
	queryParams.Set("_csrf_token", csrf)
	queryParams.Set("_live_referer", "undefined")
	queryParams.Set("vsn", "2.0.0")
	queryParams.Set("_mounts", "0")

	// Create WebSocket messages
	connectMsgObj := make([]any, 5)
	connectMsgObj[0] = "4"
	connectMsgObj[1] = "4"
	connectMsgObj[2] = phxID
	connectMsgObj[3] = "phx_join"
	connectMsgObj[4] = map[string]any{
		"url": r.baseURL + "/planning_board",
		"params": map[string]any{
			"_csrf_token": csrf,
			"timezone":    "America/New_York",
			"_mounts":     0,
		},
		"session": phxSession,
		"static":  phxStatic,
	}

	searchObj := make([]any, 5)
	searchObj[0] = "4"
	searchObj[1] = "8"
	searchObj[2] = phxID
	searchObj[3] = "event"
	searchObj[4] = map[string]any{
		"type":  "form",
		"event": "filter_update",
		"value": "filter%5Bsearch_string%5D=" + load.ExternalTMSID,
	}

	modalObj := make([]any, 5)
	modalObj[0] = "4"
	modalObj[1] = "9"
	modalObj[2] = phxID
	modalObj[3] = "event"
	modalObj[4] = map[string]any{
		"type":  "click",
		"event": "modal",
		"value": map[string]string{
			"type":                   "SchedulePlan",
			"relay_reference_number": load.ExternalTMSID,
		},
	}

	connectMsg := wsPayload{
		OperationName:        "Planning Board - Connect",
		Message:              connectMsgObj,
		NumExpectedResponses: 1,
	}
	searchMsg := wsPayload{
		OperationName:        "Planning Board - Search load",
		Message:              searchObj,
		NumExpectedResponses: 2,
	}
	modalMsg := wsPayload{
		OperationName:        "Planning Board - Open appt modal",
		Message:              modalObj,
		NumExpectedResponses: 1,
	}

	// Send WebSocket messages and capture the response
	modalResp, err := r.sendWebSocketMessages(ctx, false, "planning_board/live/websocket",
		queryParams, false, connectMsg, searchMsg, modalMsg)
	if err != nil {
		return fmt.Errorf("failed to send WebSocket messages: %w", err)
	}

	// Parse both ready time and deliver by date from the modal response
	modalContent := string(modalResp)
	readyTime, deliverByDate, err := r.parseDatesFromModalResponse(ctx, modalContent, load)
	if err != nil {
		log.WarnNoSentry(ctx, "Failed to parse dates from modal response",
			zap.String("loadID", load.ExternalTMSID),
			zap.Error(err),
		)
	} else {
		// Convert to UTC from local timezone before storing
		// Always update with latest dates from Relay (source of truth)
		if readyTime.Valid {
			if load.Pickup.Timezone != "" {
				if loc, err := time.LoadLocation(load.Pickup.Timezone); err == nil {
					// The date was created in local time, convert to UTC
					localTime := time.Date(
						readyTime.Time.Year(), readyTime.Time.Month(), readyTime.Time.Day(),
						0, 0, 0, 0, loc,
					)
					utcTime := localTime.UTC()
					readyTime.Time = utcTime
				}
			}
			load.Pickup.ReadyTime = readyTime
			log.Debug(
				ctx,
				"Updated pickup ready time from planning board",
				zap.String("loadID", load.ExternalTMSID),
				zap.Time("readyTime", readyTime.Time),
			)
		}
		if deliverByDate.Valid {
			if load.Consignee.Timezone != "" {
				if loc, err := time.LoadLocation(load.Consignee.Timezone); err == nil {
					// The date was created in local time, convert to UTC
					localTime := time.Date(
						deliverByDate.Time.Year(), deliverByDate.Time.Month(), deliverByDate.Time.Day(),
						0, 0, 0, 0, loc,
					)
					utcTime := localTime.UTC()
					deliverByDate.Time = utcTime
				}
			}
			load.Consignee.MustDeliver = deliverByDate
			log.Debug(ctx, "Updated consignee must deliver date from planning board",
				zap.String("loadID", load.ExternalTMSID),
				zap.Time("mustDeliver", deliverByDate.Time))
		}
	}

	return nil
}

// parseDatesFromModalResponse parses the ready time and deliver by date from the WebSocket modal response
func (r *Relay) parseDatesFromModalResponse(
	ctx context.Context, modalContent string, load *models.Load,
) (models.NullTime, models.NullTime, error) {
	// The WebSocket response contains JSON data with both dates in MM/DD format
	// First date: pickup ready time, Last date: delivery date (deliver by)

	// Find all date patterns in MM/DD format
	dateRegex := regexp.MustCompile(`"(\d{1,2}/\d{1,2})"`)
	allMatches := dateRegex.FindAllStringSubmatch(modalContent, -1)

	if len(allMatches) >= 2 {
		var readyTime, deliverByDate models.NullTime

		// Get reference dates from load for intelligent year assignment
		pickupReferenceDate := r.getPickupReferenceDate(load)
		deliveryReferenceDate := r.getDeliveryReferenceDate(load)

		// Parse the first date as ready time
		firstDateStr := allMatches[0][1]
		log.Debug(
			ctx,
			"Parsing ready date from modal",
			zap.String("loadID", load.ExternalTMSID),
			zap.String("dateStr", firstDateStr),
			zap.Time("pickupReferenceDate", pickupReferenceDate),
			zap.Bool("hasPickupReference", !pickupReferenceDate.IsZero()),
		)
		parsedReadyDate, err := r.parseMonthDayWithYear(
			ctx, firstDateStr, pickupReferenceDate,
		)
		if err != nil {
			log.Warn(
				ctx,
				"Failed to parse ready date",
				zap.String("loadID", load.ExternalTMSID),
				zap.String("dateStr", firstDateStr),
				zap.Error(err),
			)
		} else {
			log.Debug(
				ctx,
				"Parsed ready date from modal",
				zap.String("loadID", load.ExternalTMSID),
				zap.Time("parsedReadyDate", parsedReadyDate),
			)
			readyTime = models.NullTime{Time: parsedReadyDate, Valid: true}
		}

		// Parse the last date as deliver by date
		lastIndex := len(allMatches) - 1
		lastDateStr := allMatches[lastIndex][1]
		log.Debug(
			ctx, "Parsing deliver by date from modal",
			zap.String("loadID", load.ExternalTMSID),
			zap.String("dateStr", lastDateStr),
			zap.Time("deliveryReferenceDate", deliveryReferenceDate),
			zap.Bool("hasDeliveryReference", !deliveryReferenceDate.IsZero()),
		)
		parsedDeliverDate, err := r.parseMonthDayWithYear(
			ctx, lastDateStr, deliveryReferenceDate,
		)
		if err != nil {
			log.Warn(
				ctx,
				"Failed to parse deliver by date",
				zap.String("loadID", load.ExternalTMSID),
				zap.String("dateStr", lastDateStr),
				zap.Error(err),
			)
		} else {
			log.Debug(
				ctx,
				"Parsed deliver by date from modal",
				zap.String("loadID", load.ExternalTMSID),
				zap.Time("parsedDeliverDate", parsedDeliverDate),
			)
			deliverByDate = models.NullTime{Time: parsedDeliverDate, Valid: true}
		}

		return readyTime, deliverByDate, nil
	}

	return models.NullTime{}, models.NullTime{}, errors.New("dates not found in modal response")
}

// getPickupReferenceDate returns a reference date for pickup from the load's existing dates
func (r *Relay) getPickupReferenceDate(load *models.Load) time.Time {
	// Try to get existing pickup-related dates as reference
	if load.Pickup.ReadyTime.Valid && !load.Pickup.ReadyTime.Time.IsZero() {
		return load.Pickup.ReadyTime.Time
	}
	if load.Pickup.ApptStartTime.Valid && !load.Pickup.ApptStartTime.Time.IsZero() {
		return load.Pickup.ApptStartTime.Time
	}
	if load.Carrier.ExpectedPickupTime.Valid && !load.Carrier.ExpectedPickupTime.Time.IsZero() {
		return load.Carrier.ExpectedPickupTime.Time
	}
	if load.Carrier.PickupStart.Valid && !load.Carrier.PickupStart.Time.IsZero() {
		return load.Carrier.PickupStart.Time
	}
	// No reference date available
	return time.Time{}
}

// getDeliveryReferenceDate returns a reference date for delivery from the load's existing dates
func (r *Relay) getDeliveryReferenceDate(load *models.Load) time.Time {
	// Try to get existing delivery-related dates as reference
	if load.Consignee.MustDeliver.Valid && !load.Consignee.MustDeliver.Time.IsZero() {
		return load.Consignee.MustDeliver.Time
	}
	if load.Consignee.ApptStartTime.Valid && !load.Consignee.ApptStartTime.Time.IsZero() {
		return load.Consignee.ApptStartTime.Time
	}
	if load.Carrier.ExpectedDeliveryTime.Valid && !load.Carrier.ExpectedDeliveryTime.Time.IsZero() {
		return load.Carrier.ExpectedDeliveryTime.Time
	}
	if load.Carrier.DeliveryStart.Valid && !load.Carrier.DeliveryStart.Time.IsZero() {
		return load.Carrier.DeliveryStart.Time
	}
	// No reference date available
	return time.Time{}
}

// parseMonthDayWithYear parses a MM/DD date string and assigns the most appropriate year
// based on a reference date. If no reference date is provided, uses a reasonable window
// around the current date.
func (r *Relay) parseMonthDayWithYear(
	ctx context.Context, dateStr string, referenceDate time.Time,
) (time.Time, error) {
	// Parse the month and day
	var parsedDate time.Time
	var err error

	parsedDate, err = time.Parse("1/2", dateStr)
	if err != nil {
		parsedDate, err = time.Parse("01/02", dateStr)
		if err != nil {
			return time.Time{}, fmt.Errorf("failed to parse date %s: %w", dateStr, err)
		}
	}

	month := parsedDate.Month()
	day := parsedDate.Day()
	now := time.Now().UTC()

	// If we have a reference date, find the year that produces a date closest to it
	if !referenceDate.IsZero() {
		refYear := referenceDate.Year()

		// Try the reference year and adjacent years (±1)
		candidates := []time.Time{
			time.Date(refYear-1, month, day, 0, 0, 0, 0, time.UTC),
			time.Date(refYear, month, day, 0, 0, 0, 0, time.UTC),
			time.Date(refYear+1, month, day, 0, 0, 0, 0, time.UTC),
		}

		// Find the candidate closest to the reference date
		bestCandidate := candidates[1] // default to reference year
		minDiff := absDuration(candidates[1].Sub(referenceDate))

		for i, candidate := range candidates {
			diff := absDuration(candidate.Sub(referenceDate))
			if diff < minDiff {
				minDiff = diff
				bestCandidate = candidate
			}
			// Log each candidate for debugging
			log.Debug(ctx,
				fmt.Sprintf("Year candidate %d: %s (diff: %v)", i, candidate.Format("2006-01-02"), diff))
		}

		log.Debug(ctx,
			fmt.Sprintf("Selected date: %s based on reference: %s",
				bestCandidate.Format("2006-01-02"), referenceDate.Format("2006-01-02 15:04:05")))

		return bestCandidate, nil
	}

	log.Debug(
		ctx,
		"No reference date available, using window logic",
		zap.String("dateStr", dateStr),
		zap.Time("now", now),
	)

	// Try current year and next year
	currentYearDate := time.Date(now.Year(), month, day, 0, 0, 0, 0, time.UTC)
	nextYearDate := time.Date(now.Year()+1, month, day, 0, 0, 0, 0, time.UTC)

	// Check if current year date is within reasonable window
	currentYearDiff := currentYearDate.Sub(now)
	log.Debug(
		ctx,
		"Current year candidate",
		zap.String("date", currentYearDate.Format("2006-01-02")),
		zap.Duration("diff", currentYearDiff),
	)

	if currentYearDiff >= pastWindow && currentYearDiff <= futureWindow {
		log.Debug(ctx, "Selected current year (within window)")
		return currentYearDate, nil
	}

	// Check if next year date is within reasonable window
	nextYearDiff := nextYearDate.Sub(now)
	log.Debug(
		ctx,
		"Next year candidate",
		zap.String("date", nextYearDate.Format("2006-01-02")),
		zap.Duration("diff", nextYearDiff),
	)

	if nextYearDiff >= pastWindow && nextYearDiff <= futureWindow {
		log.Debug(ctx, "Selected next year (within window)")
		return nextYearDate, nil
	}

	// If neither is in reasonable window, choose the one closest to now
	if absDuration(currentYearDiff) < absDuration(nextYearDiff) {
		log.Debug(ctx, "Selected current year (closest)")
		return currentYearDate, nil
	}
	log.Debug(ctx, "Selected next year (closest)")
	return nextYearDate, nil
}

// absDuration returns the absolute value of a duration
func absDuration(d time.Duration) time.Duration {
	if d < 0 {
		return -d
	}
	return d
}

func (r *Relay) UpdateLoad(
	ctx context.Context,
	curLoad *models.Load,
	updatedLoad *models.Load,
) (_ models.Load, attrs models.LoadAttributes, err error) {

	spanAttrs := append(otel.IntegrationAttrs(r.tms),
		attribute.String("externalTMSID", curLoad.ExternalTMSID))

	ctx, metaSpan := otel.StartSpan(ctx, "UpdateLoadRelay", spanAttrs)
	defer func() { metaSpan.End(err) }()

	attrs = r.GetDefaultLoadAttributes()

	// Optimization: Call each update function only if necessary. We intentionally do not use goroutines
	// to avoid race conditions of making multiple updates to the same load at the same time.
	// NOTE: These conditions should be updated as Relay PUT functions are updated/added
	if strings.EqualFold(curLoad.Operator, updatedLoad.Operator) {
		log.Debug(ctx, "no Operator diff, skipping schedulePlan")
	} else {
		if err = r.assignOperator(ctx, updatedLoad); err != nil {
			return models.Load{}, attrs, fmt.Errorf("assignOperator error: %w", err)
		}
		// NOTE: On Drumkit, to remove the operator, user sets the field to "". But on success, Relay returns
		// No Assignment. So that the user doesn't see a "partial update" warning on the front-end,
		// we manually reassign Operator to the value we will parse from Relay
		if updatedLoad.Operator == "" {
			updatedLoad.Operator = "No Assignment"
		}

		// Pause so Relay can update load detail page before we re-parse it
		time.Sleep(200 * time.Millisecond)
	}

	if len(curLoad.Pickup.Diff(updatedLoad.Pickup)) > 0 || len(curLoad.Consignee.Diff(updatedLoad.Consignee)) > 0 {
		if err = r.schedulePlan(ctx, updatedLoad); err != nil {
			return models.Load{}, attrs, fmt.Errorf("schedule plan error: %w", err)
		}
	} else {
		log.Debug(ctx, "no Pickup/Consignee diff, skipping schedulePlan")
	}

	if len(curLoad.Carrier.Diff(updatedLoad.Carrier)) == 0 {
		log.Debug(ctx, "no Carrier diff, skipping tracking page updates")

		return r.GetLoad(ctx, updatedLoad.ExternalTMSID)
	}

	// Check if load is on tracking board and thus, if carrier/equipment info is editable
	_, err = r.getHTML(ctx, "tracking/tracking_load_detail/"+url.PathEscape(updatedLoad.FreightTrackingID),
		nil, "")
	if err != nil {
		// If tracking page is 404, then re-parse load and return
		if errtypes.IsEntityNotFoundError(err) {
			return r.GetLoad(ctx, updatedLoad.ExternalTMSID)
		}

		// Fail-open and try to update tracking info
		log.WarnNoSentry(ctx, "error checking if tracking page exists", zap.Error(err))
	}
	attrs = r.trackingBoardAttributes()

	if err = r.addDriverEquipmentInfo(ctx, updatedLoad); err != nil {
		return models.Load{}, attrs, fmt.Errorf("addDriverEquipmentInfo error: %w", err)
	}

	// NOTE: On Relay, dispatching and adding driver info are separate actions.
	// But NFI requested for adding driver info to trigger dispatching, so we do as long as
	// DispatchSource is provided
	if !curLoad.Carrier.ExpectedPickupTime.Equal(updatedLoad.Carrier.ExpectedPickupTime) ||
		!curLoad.Carrier.DispatchedTime.Equal(updatedLoad.Carrier.DispatchedTime) ||
		!strings.EqualFold(curLoad.Carrier.DispatchCity, updatedLoad.Carrier.DispatchCity) ||
		!strings.EqualFold(curLoad.Carrier.DispatchState, updatedLoad.Carrier.DispatchState) ||
		!strings.EqualFold(curLoad.Carrier.DispatchSource, updatedLoad.Carrier.DispatchSource) {

		if err = r.dispatchDriver(ctx, curLoad, updatedLoad); err != nil {
			return models.Load{}, attrs, fmt.Errorf("dispatchDriver error: %w", err)
		}
	}

	// Check if we need to update carrier info
	if !strings.EqualFold(curLoad.Carrier.Dispatcher, updatedLoad.Carrier.Dispatcher) ||
		!strings.EqualFold(curLoad.Carrier.Phone, updatedLoad.Carrier.Phone) ||
		!strings.EqualFold(curLoad.Carrier.Email, updatedLoad.Carrier.Email) ||
		!strings.EqualFold(curLoad.Carrier.Notes, updatedLoad.Carrier.Notes) {
		if err = r.addCarrierContactInfo(ctx, updatedLoad); err != nil {
			return models.Load{}, attrs, fmt.Errorf("addCarrierContactInfo error: %w", err)
		}
	}

	return r.GetLoad(ctx, updatedLoad.ExternalTMSID)
}

// TODO: Switch to API and filter by time.
//
// Relay has different boards for different phases of a load -- Load Board for when it's first created,
// Planning Board for planning/scheduling appointments, tracking board for tracking its progress.
// When a load is first created, it lives on the Load and Planning Board. Once a carrier is booked/assigned,
// the shipment is no longer on the load board.
//
// // This function isn't guaranteed to get *every* new load if
// 1) The load is created and before 10 minute refresh, it's moved off the load board
// 2) The highest ID in the DB is inaccurate because some loads were previously skipped
func (r *Relay) GetLoadIDs(ctx context.Context, query models.SearchLoadsQuery) (res []string, err error) {
	// MESSAGE 1: Connect
	payload1 := make([]any, 5)
	payload1[0] = "3"                 // JoinRef
	payload1[1] = "3"                 // Ref
	payload1[2] = "load_board_v2:all" // Topic
	payload1[3] = "phx_join"          // Event
	payload1[4] = map[string]any{
		"apiVersion": nil,
		"current_user": map[string]any{
			"user_id":                651,
			"name":                   "Jinyan Zang",
			"email_address":          "<EMAIL>",
			"email":                  "<EMAIL>",
			"advertisement_enabled?": false,
		},
		"target_load_board_location_filter": "all",
		"ready_date_filter":                 "all",
		"pickup_date_filter":                "all",
		"assignment_filter":                 "all",
		"filter_text":                       "",
		"sort_desc":                         true,
		"sort_by":                           "load_number",
	}
	connectMsg := wsPayload{OperationName: "Load Board - Connect", Message: payload1, NumExpectedResponses: 1}

	// MESSAGE 2: Get load board table
	payload2 := make([]any, 5)
	payload2[0] = "3"
	payload2[1] = "4"
	payload2[2] = "load_board_v2:all"    // Topic
	payload2[3] = "load_board:reconnect" // Event
	payload2[4] = map[string]any{
		"target_load_board_location_filter": "all",
		"ready_date_filter":                 "all",
		"pickup_date_filter":                "all",
		"assignment_filter":                 "all",
		"filter_text":                       "",
		"sort_desc":                         true, // Sort in desc order
		"sort_by":                           "load_number",
	}
	message2 := wsPayload{OperationName: "Load Board - Show table", Message: payload2, NumExpectedResponses: 1}

	queryParams := url.Values{}
	queryParams.Set("token", "undefined")
	queryParams.Set("vsn", "2.0.0")

	_, err = r.sendWebSocketMessages(ctx, false, "socket/websocket", queryParams, false, connectMsg)
	if err != nil {
		return nil, fmt.Errorf("error connecting to load board websocket: %w", err)
	}

	respBody, err := r.sendWebSocketMessages(ctx, true, "socket/websocket", queryParams, false, message2)
	if err != nil {
		return nil, fmt.Errorf("error getting load board: %w", err)
	}

	var arrayList []any
	err = json.Unmarshal(respBody, &arrayList)
	if err != nil {
		return nil, fmt.Errorf("error marshaling into JSON list: %w", err)
	}

	if count := len(arrayList); count < 5 {
		log.WarnNoSentry(ctx, "unexpected response body", zap.Any("responseBody", arrayList))

		return nil, fmt.Errorf("expected 5 items in response body, received %d, view logs for details", count)
	}

	loadData, err := json.Marshal(arrayList[4])
	if err != nil {
		return nil, fmt.Errorf("error marshaling 5th item in JSON array: %w", err)
	}

	var loadResp LoadResp
	err = json.Unmarshal(loadData, &loadResp)
	if err != nil {
		return nil, fmt.Errorf("error unmarshaling data rows into LoadResp: %w", err)
	}

	if loadResp.Data.TotalRows == 0 {
		return res, errtypes.EntityNotFoundError(r.tms, "load board", "")
	}

	for _, row := range loadResp.Data.Loads {
		idToCheck := fmt.Sprint(row.LoadNumber)
		if idToCheck < query.FromFreightTrackingID {
			log.Infof(ctx, "breaking because ID %s is less than %s", idToCheck, query.FromFreightTrackingID)
			break
		}
		res = append(res, idToCheck)

	}

	return res, err
}

func (r *Relay) GetLoadsByIDType(
	ctx context.Context,
	id, idType string,
) (_ []models.Load, _ models.LoadAttributes, err error) {

	spanAttrs := append(otel.IntegrationAttrs(r.tms),
		attribute.String("id", id), attribute.String("idType", idType))

	ctx, metaSpan := otel.StartSpan(ctx, "GetLoadsByIDTypeRelay", spanAttrs)
	defer func() { metaSpan.End(err) }()

	switch idType {
	case LoadNumber:
		load, attrs, err := r.GetLoad(ctx, id)
		return []models.Load{load}, attrs, err

	case BookingID:
		load, attrs, err := r.getLoadByBookingID(ctx, id)
		return []models.Load{load}, attrs, err

	default:
		return nil, r.GetDefaultLoadAttributes(), fmt.Errorf("unrecognized ID type: %s", idType)
	}
}

func (r *Relay) getLoadByBookingID(
	ctx context.Context,
	bookingID string,
) (load models.Load, attrs models.LoadAttributes, err error) {

	spanAttrs := append(otel.IntegrationAttrs(r.tms),
		attribute.String("bookingID", bookingID))

	ctx, metaSpan := otel.StartSpan(ctx, "getLoadByBookingID", spanAttrs)
	defer func() { metaSpan.End(err) }()

	// Initialize load
	attrs = r.GetDefaultLoadAttributes()
	load.ServiceID = r.tms.ServiceID
	load.FreightTrackingID = bookingID

	// Get carrier info and Relay load number from tracking page first
	if err = r.parseCarrierInfoHTML(ctx, &load); err != nil {
		return load, attrs, fmt.Errorf("parseCarrierInfoHTML error: %w", err)
	}

	if strings.TrimSpace(load.ExternalTMSID) == "" {
		return load, attrs, errors.New("unable to parse loadNumber from tracking page")
	}
	// Now that we have the loadNumber aka ExternalTMSID, get the rest of the details

	load, attrs, err = r.GetLoad(ctx, load.ExternalTMSID)

	return load, attrs, err
}

func (r *Relay) parseCarrierInfoHTML(ctx context.Context, load *models.Load) (err error) {
	// Get tracking page using booking ID aka freightTrackingID, e.g. 8081291
	respBody, err := r.getHTML(ctx, "tracking/tracking_load_detail/"+
		url.PathEscape(load.FreightTrackingID), nil, s3backup.TypeLoads)
	if err != nil {
		return fmt.Errorf("error fetching tracking page: %w", err)
	}

	return r.mapCarrierInfoHTML(ctx, respBody, load)
}

// Get load board page using Relay Load # or PRO # (generally starts with 3)
func (r *Relay) parseLoadDetailHTML(ctx context.Context, relayProNum string) (models.Load, error) {

	respBody, err := r.getHTML(ctx, "sourcing/load_board_load_detail/"+
		url.PathEscape(relayProNum), nil, s3backup.TypeLoads)
	if err != nil {
		return models.Load{}, fmt.Errorf("error fetching load detail page: %w", err)
	}

	return r.mapLoadDetailHTML(ctx, respBody)
}

// Main difference between load detail page vs. tracking page is 1) PO nums (though both list shipment ID)
// and 2) operator assignment
func (r *Relay) mapLoadDetailHTML(ctx context.Context, respBody []byte) (load models.Load, err error) {
	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(respBody))
	if err != nil {
		return load, fmt.Errorf("error reading document: %w", err)
	}

	load.TMSID = r.tms.ID
	load.ServiceID = r.tms.ServiceID
	load.ExternalTMSID = strings.TrimSpace(doc.Find(".relay-reference-number").Text())
	load.FreightTrackingID = strings.TrimSpace(doc.Find("h4.text-info.pl-3.d-inline").Text())

	// Mode
	modeText := strings.TrimSpace(doc.Find(
		".load-detail-header .d-flex.align-items-center").Eq(0).Find(".ml-3.pt-1.d-inline").Text())
	loadMode := models.StringToLoadMode(modeText)
	if loadMode == "" {
		log.WarnNoSentry(
			ctx,
			"Unknown Relay load mode",
			zap.String("loadID", load.ExternalTMSID),
			zap.String("mode", modeText),
		)
	}
	load.Mode = loadMode

	// Customer
	load.Customer.Name = strings.ToTitle(
		strings.TrimSpace(doc.Find(`div[data-toggle="tooltip"] .text-dark.ml-2`).Text()))

	// Operator
	assigneeText := doc.Find(".load-detail-header div:nth-child(2) div:nth-child(1) div:nth-child(2)").Text()
	prefix := "Assigned to"
	index := strings.Index(assigneeText, prefix)
	if index != -1 {
		// Extract the substring after "Assigned to"
		load.Operator = strings.TrimSpace(assigneeText[index+len(prefix):])
	} else {
		load.Operator = strings.TrimSpace(assigneeText)
	}

	stops := doc.Find(".load-detail-stops .stop-detail")
	// Enable multi-stop loads on front-end
	load.MoreThanTwoStops = stops.Length() > 2
	if load.MoreThanTwoStops {
		log.Info(ctx, "load with more than 2 stops",
			zap.String("externalTMSID", load.ExternalTMSID), zap.String("freightTrackingID", load.FreightTrackingID))
	}
	// Pickup
	{
		pickup := stops.Eq(0)

		if label := strings.TrimSpace(pickup.Find(".stop-label").Text()); label != "Shipper" {
			return load, fmt.Errorf("unexpected label of first .stop-detail element to be 'Shipper', got %s", label)
		}

		parseCompanyCoreInfo(pickup, &load.Pickup.CompanyCoreInfo)
		load.Pickup.RefNumber = pickup.Find("button").AttrOr("data-content", "")

		pickupLoc, err := timezone.GetLocationByCity(ctx, load.Pickup.City, load.Pickup.State, load.Pickup.Country)
		if err != nil {
			log.Warn(ctx, "error fetching pickup time.Location", zap.Error(err))
		} else {
			load.Pickup.Timezone = pickupLoc.String()

			// NOTE: Appt block on Load Detail page can be either ready time or start time,
			// while only confirmed appointments appear on Tracking page so we parse from there instead
			// To avoid confusion and inconsistencies, we don't support Relay's ReadyTime for now,
			load.Pickup.ApptStartTime, err = parseApptTime(pickup, ".stop-appt", pickupLoc)
			if err != nil {
				log.Warn(ctx, "error parsing pickup appt start time", zap.Error(err))
			}
			load.Pickup.ApptEndTime, err = parseApptTime(pickup, ".stop-appt-2", pickupLoc)
			if err != nil {
				log.Warn(ctx, "error parsing pickup appt end time", zap.Error(err))
			}
		}
	}

	// Delivery
	{
		dropoff := stops.Eq(-1)
		if label := strings.TrimSpace(dropoff.Find(".stop-label").Text()); label != "Receiver" {
			return load, fmt.Errorf("unexpected label of last .stop-detail to be 'Receiver', got %s", label)
		}

		parseCompanyCoreInfo(dropoff, &load.Consignee.CompanyCoreInfo)
		load.Consignee.RefNumber = dropoff.Find("button").AttrOr("data-content", "")

		dropoffLoc, err := timezone.GetLocationByCity(ctx,
			load.Consignee.City, load.Consignee.State, load.Consignee.Country)
		if err != nil {
			log.Warn(ctx, "error fetching dropoff time.Location", zap.Error(err))
		} else {
			load.Consignee.Timezone = dropoffLoc.String()
			load.Consignee.ApptStartTime, err = parseApptTime(dropoff, ".stop-appt", dropoffLoc)
			if err != nil {
				log.Warn(ctx, "error parsing dropoff appt start time", zap.Error(err))
			}
			load.Consignee.ApptEndTime, err = parseApptTime(dropoff, ".stop-appt-2", dropoffLoc)
			if err != nil {
				log.Warn(ctx, "error parsing dropoff appt end time", zap.Error(err))
			}
		}
	}

	// Load details
	{
		loadDetails := doc.Find(".load-detail-booking .label-value-group")
		loadDetails.Each(func(_ int, s *goquery.Selection) {
			label := strings.TrimSpace(s.Find(".label").Text())
			switch strings.ToLower(label) {
			case "reference numbers":
				// It's already a comma-separated list
				load.PONums = strings.ReplaceAll(strings.TrimSpace(s.Find(".value").Text()), " ", "")
				refSplits := strings.Split(strings.TrimSpace(s.Find(".value").Text()), ",")

				// In mapLoadDetailHTML, there's evidence that the first of "reference numbers" is the shipment ID.
				// But if there's a tracking page for the load, we re-parse the value to be certain.
				if len(refSplits) > 0 {
					load.Customer.RefNumber = strings.TrimSpace(refSplits[0])
				}
			case "rate confirmation sent":
				load.Carrier.RateConfirmationSent = strings.ToLower(
					strings.TrimSpace(s.Find(".value").Text())) == "yes"
			case "carrier":
				load.Carrier.Name = strings.TrimSpace(s.Find(".value").Text())
			case "recipients":
				load.Carrier.Email = strings.TrimSpace(s.Find(".value").Text())
			case "total carrier rate":
				currencyType := strings.TrimSpace(s.Find(".value .mr-1").Text())
				if currencyType == "USD" {
					priceWithDollarSign := strings.TrimSpace(s.Find(".value .font-size-largest").Text())
					cleanedValue := strings.ReplaceAll(priceWithDollarSign, "$", "")
					cleanedValue = strings.ReplaceAll(cleanedValue, ",", "")

					// Convert the cleaned value to float32
					var floatValue float64
					floatValue, err = strconv.ParseFloat(cleanedValue, 32)
					if err == nil {
						load.DeclaredValueUSD = float32(floatValue)
					}
				}
			}
		})
	}

	// Cargo details
	{
		cargoDetails := doc.Find(".load-detail-cargo .label-value-group")
		cargoDetails.Each(func(_ int, s *goquery.Selection) {
			label := strings.TrimSpace(s.Find(".label").Text())
			switch label {
			case "Miles":
				milesString := strings.TrimSpace(s.Find(".value").Text())
				if milesString != "" {
					floatValue, parseErr := strconv.ParseFloat(milesString, 32)
					if parseErr == nil {
						load.Specifications.TotalDistance = models.ValueUnit{
							Val: float32(floatValue), Unit: models.MilesUnit}
					}
					err = parseErr
				}
			case "Weight":
				weightString := strings.TrimSpace(s.Find(".value").Text())
				if weightString != "" {
					floatValue, parseErr := strconv.ParseFloat(weightString, 32)
					if parseErr == nil {
						// Pounds is the only unit available on the create/edit load page
						load.Specifications.TotalWeight = models.ValueUnit{
							Val: float32(floatValue), Unit: models.LbsUnit}
					}
					err = parseErr
				}
			case "Pallets":
				palletsString := strings.TrimSpace(s.Find(".value").Text())
				if palletsString != "" {
					intValue, parseErr := strconv.Atoi(palletsString)
					if parseErr == nil {
						load.Specifications.TotalOutPalletCount = intValue
					}
					err = parseErr
				}

			case "Pieces":
				piecesStr := strings.TrimSpace(s.Find(".value").Text())
				if piecesStr != "" {
					intValue, parseErr := strconv.Atoi(piecesStr)
					if parseErr == nil {
						// Unit based on relaytms.com/planning_board/stop_management/{load#}
						load.Specifications.TotalPieces = models.ValueUnit{Val: float32(intValue), Unit: "boxes"}
					}
				}

			case "Commodity":
				list := s.Find(".value ul")
				if list.Length() > 0 {
					items := list.Find("li")
					var commodities []string
					items.Each(func(_ int, s *goquery.Selection) {
						commodities = append(commodities, strings.TrimSpace(s.Text()))
					})
					load.Specifications.Commodities = strings.Join(commodities, ",")
				} else {
					load.Specifications.Commodities = strings.TrimSpace(s.Find(".value").Text())
				}
			}

		})
	}

	// Rate details
	{
		rateDetails := doc.Find(".load-detail-money .label-value-group")
		rateDetails.Each(func(_ int, s *goquery.Selection) {
			label := strings.TrimSpace(s.Find(".label").Text())
			switch label {
			case "Customer Rate":
				customerRate := s.Find(".customer-rate-breakdown tr")
				customerRate.Each(func(_ int, s *goquery.Selection) {
					rateType := strings.TrimSpace(s.Find(".text-muted").Text())
					switch rateType {
					case "Linehaul":
						// rate := strings.TrimSpace(s.Find(".text-right").Text())
						// cleanedValue := strings.ReplaceAll(rate, "$", "")
						// floatValue, parseErr := strconv.ParseFloat(cleanedValue, 32)
						// if parseErr == nil {
						// 	load.RateData.CustomerLHRateUSD = float32(floatValue /
						// 		float64(load.Specifications.TotalDistance.Val))
						// }
					case "Fuel surcharge":
						// rate := strings.TrimSpace(s.Find(".text-right").Text())
						// cleanedValue := strings.ReplaceAll(rate, "$", "")
						// if cleanedValue != "" {
						// 	// floatValue, parseErr := strconv.ParseFloat(cleanedValue, 32)
						// 	// if parseErr == nil {
						// 	// 	load.RateData.CarrierLHRateUSD = float32(floatValue) /
						// 	// 		load.Specifications.TotalDistance.Val
						// 	// }
						// }
					}
				})
			case "Max Buy":
				currencyType := strings.TrimSpace(s.Find("small").Text())
				if currencyType == "USD" {
					totalBuyingValue := strings.TrimSpace(s.Find("span.value").Text())
					cleanedValue := strings.ReplaceAll(totalBuyingValue, "$", "")
					if cleanedValue != "" {
						cleanedValue = strings.ReplaceAll(cleanedValue, ",", "")
						// Convert the cleaned value to float32
						floatValue, parseErr := strconv.ParseFloat(cleanedValue, 32)
						if parseErr == nil {
							load.RateData.CarrierMaxRate = float32(floatValue)
						}
						err = parseErr
					}
				}
			}
		})
	}

	return load, err
}

func (r *Relay) mapCarrierInfoHTML(ctx context.Context, respBody []byte, load *models.Load) error {
	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(respBody))
	if err != nil {
		return fmt.Errorf("error reading document: %w", err)
	}

	load.TMSID = r.tms.ID
	load.ServiceID = r.tms.ServiceID
	load.ExternalTMSID = strings.TrimSpace(doc.Find(".relay-reference-number").Text())
	load.FreightTrackingID = strings.TrimSpace(doc.Find(".text-info.pl-3.d-inline").Text())
	load.Status = parseStatus(doc)
	load.Notes = parseNotes(ctx, doc)

	// Appointment block on Load Detail page may be ready date OR actual appointment.
	// But only confirmed appointments appear on tracking page so we use that as the reliable source
	stops := doc.Find(".stop-detail")
	// Pickup
	{
		pickup := stops.Eq(0)

		if label := strings.TrimSpace(pickup.Find(".stop-label").Text()); label != "Origin" {
			return fmt.Errorf("unexpected label of first .stop-detail element to be 'Origin', got %s", label)
		}

		// Parse timestamps only if mapLoadBoardHTML was called first
		// different for getLoadByBookingID which has to get Tracking page first
		if load.Pickup.City != "" {
			pickupLoc, err := timezone.GetLocationByCity(ctx, load.Pickup.City, load.Pickup.State, load.Pickup.Country)
			if err != nil {
				log.Warn(ctx, "error fetching pickup time.Location", zap.Error(err))
			} else {
				load.Pickup.Timezone = pickupLoc.String()

				load.Pickup.ApptStartTime, err = parseApptTime(pickup, ".stop-appt", pickupLoc)
				if err != nil {
					log.Warn(ctx, "error parsing pickup appt start time", zap.Error(err))
				}
				load.Pickup.ApptEndTime, err = parseApptTime(pickup, ".stop-appt-2", pickupLoc)
				if err != nil {
					log.Warn(ctx, "error parsing pickup appt end time", zap.Error(err))
				}
			}
		}
	}

	// Delivery
	{
		// -2 because -1 is check call section
		dropoff := stops.Eq(-2)
		if label := strings.TrimSpace(dropoff.Find(".stop-label").Text()); label != "Destination" {
			return fmt.Errorf("unexpected label of last .stop-detail to be 'Destination', got %s", label)
		}

		// Parse timestamps only if mapLoadBoardHTML was called first
		// different for getLoadByBookingID which has to get Tracking page first
		if load.Consignee.City != "" {
			dropoffLoc, err := timezone.GetLocationByCity(ctx,
				load.Consignee.City, load.Consignee.State, load.Consignee.Country)
			if err != nil {
				log.Warn(ctx, "error fetching dropoff time.Location", zap.Error(err))
			} else {
				load.Consignee.Timezone = dropoffLoc.String()

				load.Consignee.ApptStartTime, err = parseApptTime(dropoff, ".stop-appt", dropoffLoc)
				if err != nil {
					log.Warn(ctx, "error parsing dropoff appt start time", zap.Error(err))
				}
				load.Consignee.ApptEndTime, err = parseApptTime(dropoff, ".stop-appt-2", dropoffLoc)
				if err != nil {
					log.Warn(ctx, "error parsing dropoff appt end time", zap.Error(err))
				}
			}
		}
	}

	moneyDiv := doc.Find(".load-detail-money .label-value-group")

	moneyDiv.Each(func(_ int, s *goquery.Selection) {
		label := strings.TrimSpace(s.Find(".label").Text())
		value := strings.TrimSpace(s.Find(".value").Text())

		// In mapLoadDetailHTML, there's evidence that that the first of the list of "reference numbers"
		// is the shipment ID. But if there's a tracking page for the load, we re-parse the value with certainty.
		if strings.ToLower(label) == "shipment id" {
			load.Customer.RefNumber = value
		}
	})

	carrierDiv := doc.Find(".load-detail-cargo .label-value-group")

	carrierDiv.Each(func(_ int, s *goquery.Selection) {
		label := strings.TrimSpace(s.Find(".label").Text())
		value := strings.TrimSpace(s.Find(".value").Text())

		switch strings.ToLower(label) {
		// In mapLoadDetailHTML, there's evidence that that the first of the list of "reference numbers"
		// is the shipment ID. But if there's a tracking page for the load, we re-parse the value with certainty.
		case "shipment id":
			load.Customer.RefNumber = value

		case "carrier":
			load.Carrier.Name = value

		case "driver":
			driverDetails := s.Find(".value")
			if driverDetails.Length() > 0 {
				driverNames := strings.Split(strings.TrimSpace(driverDetails.Eq(0).Text()), ",")
				if len(driverNames) == 1 {
					load.Carrier.FirstDriverName = driverNames[0]
				}
				if len(driverNames) >= 2 {
					load.Carrier.FirstDriverName = strings.TrimSpace(driverNames[1]) + " " +
						strings.TrimSpace(driverNames[0])
				}
				load.Carrier.FirstDriverPhone = strings.TrimSpace(s.Find("span").Eq(0).Text())
			}
			if driverDetails.Length() > 1 {
				load.Carrier.SecondDriverName = strings.TrimSpace(driverDetails.Eq(1).Text())
				load.Carrier.SecondDriverPhone = strings.TrimSpace(s.Find("span").Eq(1).Text())
			}

		case "truck/trailer number":
			load.Carrier.ExternalTMSTruckID = value
			load.Carrier.ExternalTMSTrailerID = strings.TrimSpace(s.Find("span").Text())
		}

	})
	contactDetails := doc.Find(".load-detail-contact .label-value-group")
	contactDetails.Each(func(_ int, s *goquery.Selection) {
		label := strings.TrimSpace(s.Find(".label").Text())
		value := strings.TrimSpace(s.Find(".value").Text())

		switch strings.ToLower(label) {
		case "tracking contact":
			load.Carrier.Dispatcher = value
			load.Carrier.Phone = strings.TrimSpace(s.Find("span").Eq(0).Text())
			load.Carrier.Email = strings.TrimSpace(s.Find("span").Eq(1).Text())

		case "contact notes":
			load.Carrier.Notes = value
		}
	})

	checkCalls, err := r.parseCheckCallHTML(ctx, respBody, load.ID, load.FreightTrackingID)
	if err != nil {
		// Fail-open
		log.WarnNoSentry(ctx, "error parsing check calls", zap.Error(err))
		return nil
	}

	index := slices.IndexFunc(checkCalls, func(cc models.CheckCall) bool {
		return strings.EqualFold(cc.Status, "driver dispatched")
	})
	if index > -1 {
		dispatchCC := checkCalls[index]
		load.Carrier.DispatchCity = dispatchCC.City
		load.Carrier.DispatchState = dispatchCC.State
		load.Carrier.DispatchSource = dispatchCC.Source

		eta, err := timezone.DenormalizeUTC(dispatchCC.NextStopETAWithoutTimezone.Time, load.Pickup.Timezone)
		if err != nil {
			log.WarnNoSentry(ctx, "error denormalizing expectedPickup",
				zap.Error(err), zap.String("timezone", load.Pickup.Timezone))
		}
		load.Carrier.ExpectedPickupTime = models.ToValidNullTime(eta)

		loc, err := timezone.GetTimezone(ctx, dispatchCC.City, dispatchCC.State, "")
		if err != nil {
			log.WarnNoSentry(ctx, "error getting dispatched timezone, falling back to TZ-agnostic", zap.Error(err))
			load.Carrier.DispatchedTime = models.ToValidNullTime(dispatchCC.DateTimeWithoutTimezone.Time)

			return nil
		}
		dispTime, err := timezone.DenormalizeUTC(dispatchCC.DateTimeWithoutTimezone.Time, loc)
		if err != nil {
			log.WarnNoSentry(ctx, "error denormalizing dispatchedTime, falling back to TZ-agnostic",
				zap.Error(err), zap.String("timezone", loc))
			load.Carrier.DispatchedTime = models.ToValidNullTime(dispatchCC.DateTimeWithoutTimezone.Time)
			return nil
		}
		load.Carrier.DispatchedTime = models.ToValidNullTime(dispTime)

	}

	return nil
}

// parseCompanyCoreInfo parses common company core information from the given selection.
func parseCompanyCoreInfo(selection *goquery.Selection, company *models.CompanyCoreInfo) {
	company.Name = strings.TrimSpace(selection.Find(".location-address .title").Text())

	address := selection.Find(".location-address .subtitle")
	company.AddressLine1 = strings.TrimSpace(address.Find(".address-1").Text())
	company.AddressLine2 = strings.TrimSpace(address.Find(".address-2").Text())

	cityStateZip := strings.TrimSpace(selection.Find(".city-state-zip").Text())
	cityStateZip = strings.ReplaceAll(cityStateZip, " ", " ")

	cityStateZipArray := strings.Split(cityStateZip, ",")

	if len(cityStateZipArray) == 2 {
		company.City = strings.TrimSpace(cityStateZipArray[0])
		stateZip := strings.Split(strings.TrimSpace(cityStateZipArray[1]), " ")

		if len(stateZip) >= 2 {
			company.State = strings.TrimSpace(stateZip[0])
			// Handle Canada (e.g. K8N 0A3)
			company.Zipcode = strings.TrimSpace(strings.Join(stateZip[1:], " "))
		}
	}
}

// parseApptTime parses the appointment time for the given selection.
func parseApptTime(selection *goquery.Selection, className string, tz *time.Location) (models.NullTime, error) {
	apptString := strings.TrimSpace(strings.ReplaceAll(selection.Find(className).Text(), "\x0a", ""))
	if apptString == "" {
		return models.NullTime{}, nil
	}

	// If on tracking page, appt reference is appended which we don't want confused as the TZ
	// (e.g. "8/16/24 09:00                                                                  27932")
	// so we extract just the date using regex
	matches := timeNoTZRegex.FindStringSubmatch(apptString)
	if len(matches) > 0 {
		apptString = matches[0]
	}

	return parseCustomDateTime(apptString, false, tz)
}

func parseStatus(trackingDoc *goquery.Document) (status string) {
	// Check first if the load was cancelled
	trackingDoc.Find("div.load-detail-money .label-value-group").Each(func(_ int, s *goquery.Selection) {
		if label := strings.TrimSpace(s.Find(".label").Text()); label == "Customer Money" {
			// Evidence supports that if 1) there's a tracking page but 2) no customer or vendor charges,
			// then the load was cancelled
			if strings.ToLower(strings.TrimSpace(s.Find(".value").Text())) == "no charges" && status == "" {
				status = "Cancelled"
			}
		}
	})
	if status != "" {
		return status
	}

	// If not cancelled, then the most recent check call is its status
	trackingDoc.Find("div.stop-detail .tracking-update .update-title").Each(func(_ int, s *goquery.Selection) {
		recentStatus := strings.TrimSpace(s.Text())
		if recentStatus != "Note Captured" && status == "" {
			status = recentStatus
			return
		}
	})

	// If no check calls but there's a tracking page for it, then it's only been Booked
	if status == "" {
		return "Booked"
	}

	prefix := "Marked"
	index := strings.Index(status, prefix)
	if index != -1 {
		// Extract the substring after "Marked" if it exists (e.g. Marked Delivered, Marked Loaded)
		return strings.TrimSpace(status[index+len(prefix):])
	}

	return status
}
