package c3reservations

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/httplog"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/models"
)

func (c *C3Reservations) MakeAppointment(
	ctx context.Context,
	req models.MakeAppointmentRequest,
	opts ...models.SchedulingOption,
) (models.Appointment, error) {
	options := &models.SchedulingOptions{}
	options.Apply(opts...)

	return c.MakeAppointmentWithCyclops(ctx, req, options)
}

func (c *C3Reservations) MakeAppointmentWithCyclops(
	ctx context.Context,
	req models.MakeAppointmentRequest,
	_ *models.SchedulingOptions,
) (models.Appointment, error) {

	formData := req.FormData

	if req.ReservationType != "" {
		if formData == nil {
			formData = map[string]any{}
		}
		formData["reservationType"] = req.ReservationType
	}

	// Format time in warehouse timezone to match how GetOpenSlots parses times
	var startTimeStr string
	if req.WarehouseTimezone != "" {
		loc, err := time.LoadLocation(req.WarehouseTimezone)
		if err != nil {
			return models.Appointment{}, fmt.Errorf("invalid warehouse timezone %s: %w", req.WarehouseTimezone, err)
		}
		// Convert to warehouse timezone and format without timezone suffix
		localTime := req.StartTime.In(loc)
		startTimeStr = localTime.Format("2006-01-02T15:04:05")
	} else {
		// Fallback to UTC format if no timezone provided
		startTimeStr = req.StartTime.Format("2006-01-02T15:04:05.000Z")
	}

	appointmentData := map[string]any{
		"appointmentTime": startTimeStr,
		"loadId":          req.PONums,
		"duration":        60,
		"notes":           req.Notes,
	}

	cyclopsReq := MakeAppointmentRequest{
		CyclopsBaseRequest: models.CyclopsBaseRequest{
			Integration: models.CyclopsSchedulingIntegration,
			Platform:    C3ReservationsPlatform,
			Action:      models.ActionMakeAppointment,
			UserID:      c.scheduler.Username,
			Mode:        models.CyclopsModeSelenium,
			Credentials: models.CyclopsCredentials{
				Username: c.creds.Username,
				Password: c.creds.Password,
			},
			Tenant: c.scheduler.Tenant,
		},
		Appointment: appointmentData,
		FormData:    formData,
	}

	reqBody, err := json.Marshal(cyclopsReq)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to marshal request: %w", err)
	}

	url, err := helpers.GetCyclopsURL()
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to get url: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		url,
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Accept", "application/json")

	resp, err := otel.TracingHTTPClient().Do(httpReq)
	if err != nil {
		httplog.LogHTTPRequestFailed(ctx, c.scheduler, err)
		return models.Appointment{}, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()
	httplog.LogHTTPResponseCode(ctx, c.scheduler, resp.StatusCode)

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to read response: %w", err)
	}

	var res MakeAppointmentResponse
	if err = json.Unmarshal(body, &res); err != nil {
		return models.Appointment{}, fmt.Errorf("failed to unmarshal body: %w", err)
	}

	if !res.Success {
		return models.Appointment{}, &models.CyclopsError{
			Message: res.Message,
			Errors:  res.Errors,
		}
	}

	// Convert Cyclops appointment to models.Appointment
	appt, err := convertToAppointment(res.Appointment, req.WarehouseTimezone)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to convert appointment: %w", err)
	}

	// Add additional fields from request
	appt.PONums = req.PONums
	appt.StartTime = req.StartTime
	appt.ExternalWarehouseID = req.WarehouseID

	return appt, nil
}
