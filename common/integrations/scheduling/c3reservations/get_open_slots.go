package c3reservations

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/httplog"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

func (c *C3Reservations) GetOpenSlots(
	ctx context.Context,
	loadTypeID string,
	req models.GetOpenSlotsRequest,
) ([]models.Slot, error) {
	return c.GetOpenSlotsWithCyclops(ctx, loadTypeID, req)
}

func (c *C3Reservations) GetOpenSlotsWithCyclops(
	ctx context.Context,
	loadTypeID string,
	req models.GetOpenSlotsRequest,
) ([]models.Slot, error) {

	formData := req.FormData

	if req.ReservationType != "" {
		if formData == nil {
			formData = map[string]any{}
		}
		formData["reservationType"] = req.ReservationType
	}

	cyclopsReq := GetSlotsRequest{
		CyclopsBaseRequest: models.CyclopsBaseRequest{
			Integration: models.CyclopsSchedulingIntegration,
			Platform:    C3ReservationsPlatform,
			Action:      models.ActionGetOpenSlots,
			UserID:      c.scheduler.Username,
			Mode:        models.CyclopsModeSelenium,
			Credentials: models.CyclopsCredentials{
				Username: c.creds.Username,
				Password: c.creds.Password,
			},
			Tenant: c.scheduler.Tenant,
		},
		StartDate:  req.Start.Format("2006-01-02"),
		EndDate:    req.End.Format("2006-01-02"),
		ProID:      loadTypeID, // Use loadTypeID as proId
		FilterType: req.FilterType,
		FormData:   formData,
	}

	reqBody, err := json.Marshal(cyclopsReq)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	url, err := helpers.GetCyclopsURL()
	if err != nil {
		return nil, fmt.Errorf("failed to get url: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		url,
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Accept", "application/json")

	resp, err := otel.TracingHTTPClient().Do(httpReq)
	if err != nil {
		httplog.LogHTTPRequestFailed(ctx, c.scheduler, err)
		return nil, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()
	httplog.LogHTTPResponseCode(ctx, c.scheduler, resp.StatusCode)

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	var res GetSlotsResponse
	if err = json.Unmarshal(body, &res); err != nil {
		return nil, fmt.Errorf("failed to unmarshal body: %w", err)
	}

	if !res.Success {
		return nil, &models.CyclopsError{
			Message: res.Message,
			Errors:  res.Errors,
		}
	}

	// Extract warehouse timezone from request if available
	warehouseTimezone := ""
	if req.Warehouse != nil && req.Warehouse.WarehouseTimezone != "" {
		warehouseTimezone = req.Warehouse.WarehouseTimezone
	} else {
		log.Warn(
			ctx,
			"No warehouse timezone available for C3Reservations, falling back to UTC",
			zap.String("warehouseID", req.WarehouseID),
		)
	}

	return convertToSlots(ctx, res.Appointments, warehouseTimezone), nil
}

func convertToSlots(ctx context.Context, appointments []AppointmentData, warehouseTimezone string) []models.Slot {
	ctx = log.With(ctx, zap.String("source", string(models.C3ReservationsSource)))

	slots := make([]models.Slot, 0)
	for _, appt := range appointments {
		if appt.Status != "AVAILABLE" && appt.Status != "" {
			continue
		}

		var times []time.Time

		var t time.Time
		var err error

		if warehouseTimezone != "" {
			// Parse time in warehouse timezone
			loc, locErr := time.LoadLocation(warehouseTimezone)
			if locErr != nil {
				log.Warn(
					ctx,
					"Invalid warehouse timezone, falling back to UTC",
					zap.String("timezone", warehouseTimezone),
					zap.Error(locErr),
				)
				t, err = time.Parse("2006-01-02T15:04:05", appt.ScheduledTime)
			} else {
				t, err = time.ParseInLocation("2006-01-02T15:04:05", appt.ScheduledTime, loc)
				log.Debug(
					ctx,
					"Parsed C3Reservations appointment time in warehouse timezone",
					zap.String("scheduledTime", appt.ScheduledTime),
					zap.String("timezone", warehouseTimezone),
					zap.String("parsedTime", t.Format("2006-01-02T15:04:05Z07:00")),
				)
			}
		} else {
			// Fallback to UTC parsing if no timezone provided
			t, err = time.Parse("2006-01-02T15:04:05", appt.ScheduledTime)
		}

		if err != nil {
			log.Infof(
				ctx,
				"Invalid time format for C3Reservations warehouse, ScheduledTime %s: %v",
				appt.ScheduledTime,
				zap.Error(err),
			)

			continue
		}
		times = append(times, t)

		if len(times) > 0 {
			slot := models.Slot{
				Dock: models.Dock{
					ID: appt.AppointmentID,
				},
				StartTimes: times,
				Capacity:   appt.Capacity,
			}
			slots = append(slots, slot)
		}
	}

	return slots
}
