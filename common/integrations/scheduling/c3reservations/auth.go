package c3reservations

import (
	"context"
	"errors"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/models"
)

func New(ctx context.Context, scheduler models.Integration) (*C3Reservations, error) {
	if scheduler.Username == "" {
		return nil, errors.New("missing required username")
	}

	password, err := crypto.DecryptAESGCM(ctx, string(scheduler.EncryptedPassword), nil)
	if err != nil {
		return nil, errors.New("failed to decrypt password")
	}

	creds := &Credentials{
		Username: scheduler.Username,
		Password: password,
	}

	return &C3Reservations{scheduler: scheduler, creds: creds}, nil
}

func (c *C3Reservations) OnboardScheduler(_ context.Context) (models.OnboardSchedulerResponse, error) {
	return models.OnboardSchedulerResponse{}, errtypes.NotImplemented(models.C3Reservations, "OnboardScheduler")
}
