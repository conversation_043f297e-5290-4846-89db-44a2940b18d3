package c3reservations

import (
	"fmt"
	"time"

	"github.com/drumkitai/drumkit/common/models"
)

const (
	C3ReservationsPlatform = "C3Reservations"
)

type (
	GetSlotsRequest struct {
		models.CyclopsBaseRequest
		StartDate       string         `json:"startDate"`
		EndDate         string         `json:"endDate"`
		ProID           string         `json:"proId,omitempty"`
		ValidatedPOData map[string]any `json:"validatedPOData,omitempty"`
		FilterType      string         `json:"filterType,omitempty"`
		LocationID      string         `json:"locationId,omitempty"`
		FormData        map[string]any `json:"formData,omitempty"`
	}

	GetSlotsResponse struct {
		models.CyclopsBaseResponse
		Appointments []AppointmentData          `json:"appointments"`
		PlatformData models.CyclopsPlatformData `json:"platformData"`
	}

	AppointmentData struct {
		AppointmentID string                       `json:"appointmentId"`
		Duration      int                          `json:"duration"`
		Notes         string                       `json:"notes"`
		ScheduledTime string                       `json:"scheduledTime"`
		Status        string                       `json:"status"`
		Warehouse     *models.CyclopsWarehouseInfo `json:"warehouse,omitempty"`
		Extended      any                          `json:"extended,omitempty"`
		Capacity      int                          `json:"capacity"`
	}

	MakeAppointmentRequest struct {
		models.CyclopsBaseRequest
		Appointment map[string]any `json:"appointment"`
		FormData    map[string]any `json:"formData,omitempty"`
	}

	MakeAppointmentResponse struct {
		models.CyclopsBaseResponse
		Appointment models.CyclopsAppointment `json:"appointment"`
	}
)

func convertToAppointment(appt models.CyclopsAppointment, warehouseTimezone string) (models.Appointment, error) {
	// Early return for empty or placeholder scheduled times
	if appt.ScheduledTime == "" || appt.ScheduledTime == "-" {
		return models.Appointment{
			ExternalID: appt.AppointmentID,
			StartTime:  time.Time{},
			Status:     appt.Status,
		}, nil
	}

	var scheduledTime time.Time
	var err error

	// Try different time parsing layouts in order of preference
	layouts := []struct {
		layout   string
		useLocal bool
	}{
		{time.RFC3339, false},
		{time.RFC3339Nano, false},
		{"2006-01-02T15:04:05", true},
	}

	for _, l := range layouts {
		if l.useLocal && warehouseTimezone != "" {
			if loc, locErr := time.LoadLocation(warehouseTimezone); locErr == nil {
				scheduledTime, err = time.ParseInLocation(l.layout, appt.ScheduledTime, loc)
			} else {
				scheduledTime, err = time.Parse(l.layout, appt.ScheduledTime)
			}
		} else {
			scheduledTime, err = time.Parse(l.layout, appt.ScheduledTime)
		}

		if err == nil {
			break
		}
	}

	if err != nil {
		return models.Appointment{}, fmt.Errorf("invalid scheduled time format: %w", err)
	}

	// Normalize to warehouse timezone if provided
	if warehouseTimezone != "" {
		if loc, locErr := time.LoadLocation(warehouseTimezone); locErr == nil {
			scheduledTime = scheduledTime.In(loc)
		}
	}

	return models.Appointment{
		ExternalID: appt.AppointmentID,
		StartTime:  scheduledTime,
		Status:     appt.Status,
	}, nil
}
