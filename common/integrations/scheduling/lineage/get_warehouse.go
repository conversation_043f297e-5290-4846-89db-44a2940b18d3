package lineage

import (
	"context"
	"fmt"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/models"
)

func (l *Lineage) GetWarehouse(ctx context.Context, warehouseID string) (models.Warehouse, error) {
	ctx, span := otel.StartSpan(ctx, "GetWarehouse", nil)
	defer func() { span.End(nil) }()

	sessionID, err := l.getTurvoSessionID(ctx)
	if err != nil {
		return models.Warehouse{}, fmt.Errorf("failed to get Turvo session ID: %w", err)
	}

	locations, err := l.fetchLineageLocations(ctx, sessionID)
	if err != nil {
		return models.Warehouse{}, fmt.Errorf("failed to fetch Lineage locations: %w", err)
	}

	for _, loc := range locations {
		if loc.ID == warehouseID {
			if loc.Addresses.Deleted || !loc.Addresses.Active {
				return models.Warehouse{}, fmt.Errorf(
					"warehouse %s is inactive or deleted",
					warehouseID,
				)
			}

			warehouse := convertLineageLocationToWarehouse(loc)
			return warehouse, nil
		}
	}

	return models.Warehouse{}, fmt.Errorf("warehouse %s not found", warehouseID)
}
