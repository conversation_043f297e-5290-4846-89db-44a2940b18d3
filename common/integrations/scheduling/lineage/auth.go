package lineage

import (
	"context"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/models"
)

func New(ctx context.Context, scheduler models.Integration) (*Lineage, error) {
	var creds *Credentials

	// Lineage warehouses don't need credentials (public API)
	// But appointments still use CYCLOPS with credentials
	if scheduler.Username != "" && len(scheduler.EncryptedPassword) > 0 {
		password, err := crypto.DecryptAESGCM(ctx, string(scheduler.EncryptedPassword), nil)
		if err != nil {
			return nil, err
		}

		creds = &Credentials{
			Username: scheduler.Username,
			Password: password,
		}
	}

	return &Lineage{scheduler: scheduler, creds: creds}, nil
}

func (l *Lineage) OnboardScheduler(_ context.Context) (models.OnboardSchedulerResponse, error) {
	return models.OnboardSchedulerResponse{}, errtypes.NotImplemented(models.Lineage, "OnboardScheduler")
}
