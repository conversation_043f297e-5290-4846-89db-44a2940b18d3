package lineage

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/httplog"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/models"
)

type (
	CyclopsMakeAppointmentRequest struct {
		models.CyclopsBaseRequest
		Appointments []CyclopsAppointmentData `json:"appointments"`
	}

	CyclopsAppointmentData struct {
		AppointmentID   string                       `json:"appointmentId"`
		PoID            string                       `json:"poId"`
		AppointmentDate string                       `json:"appointmentDate"`
		Duration        int                          `json:"duration"`
		Status          string                       `json:"status"`
		Notes           string                       `json:"notes"`
		Warehouse       *models.CyclopsWarehouseInfo `json:"warehouse,omitempty"`
		ProNumber       string                       `json:"proNumber"`
		DoorType        string                       `json:"doorType"`
		UnloadType      string                       `json:"unloadType"`
		Commodity       string                       `json:"commodity"`
		ContainerNumber string                       `json:"containerNumber"`
		LinkLoadID      string                       `json:"linkLoadId"`
		DepotValue      string                       `json:"depotValue"`
		Uom             string                       `json:"uom"`
		QtyCount        int                          `json:"qtyCount"`
	}
)

func (l *Lineage) MakeAppointment(
	ctx context.Context,
	req models.MakeAppointmentRequest,
	_ ...models.SchedulingOption,
) (models.Appointment, error) {

	ctx, span := otel.StartSpan(ctx, "MakeAppointment", nil)
	defer func() { span.End(nil) }()

	if l.creds == nil {
		return models.Appointment{}, errors.New("credentials required for appointment operations")
	}

	if req.RequestType == "" {
		req.RequestType = string(models.RequestTypePickup)
	}

	reqType := models.RequestType(req.RequestType)
	if !reqType.IsValid() {
		return models.Appointment{}, fmt.Errorf("invalid request type: %s", req.RequestType)
	}

	var appointments []CyclopsAppointmentData
	if len(req.Appointments) > 0 {
		for _, apptReq := range req.Appointments {
			appointmentData, err := l.buildAppointmentData(apptReq)
			if err != nil {
				return models.Appointment{}, fmt.Errorf(
					"failed to build appointment data for %s: %w",
					apptReq.FreightTrackingID,
					err,
				)
			}
			appointments = append(appointments, appointmentData)
		}
	} else {
		return models.Appointment{}, errors.New("appointments not received")
	}

	cyclopsReq := CyclopsMakeAppointmentRequest{
		CyclopsBaseRequest: models.CyclopsBaseRequest{
			Integration: models.CyclopsSchedulingIntegration,
			Platform:    LineagePlatform,
			Action:      models.ActionMakeAppointment,
			UserID:      l.scheduler.Username,
			Credentials: models.CyclopsCredentials{
				Username: l.creds.Username,
				Password: l.creds.Password,
			},
		},
		Appointments: appointments,
	}

	reqBody, err := json.Marshal(cyclopsReq)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to marshal request: %w", err)
	}

	cyclopsURL, err := helpers.GetCyclopsURL()
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to get url: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		cyclopsURL,
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Accept", "application/json")

	resp, err := otel.TracingHTTPClient().Do(httpReq)
	if err != nil {
		httplog.LogHTTPRequestFailed(ctx, l.scheduler, err)
		return models.Appointment{}, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()
	httplog.LogHTTPResponseCode(ctx, l.scheduler, resp.StatusCode)

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to read response: %w", err)
	}

	var multiApptResp CyclopsMultipleAppointmentResponse
	if err = json.Unmarshal(body, &multiApptResp); err != nil {
		return models.Appointment{}, fmt.Errorf("failed to unmarshal appointments response: %w", err)
	}

	if !multiApptResp.Success {
		return models.Appointment{}, &models.CyclopsError{
			Message: multiApptResp.Message,
			Errors:  multiApptResp.Errors,
		}
	}

	convertedAppointments, err := convertToAppointments(multiApptResp)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to convert appointments: %w", err)
	}

	if len(convertedAppointments) == 0 {
		return models.Appointment{}, errors.New("no appointments returned from cyclops")
	}

	return convertedAppointments[0], nil
}

func (l *Lineage) buildAppointmentData(
	apptReq models.AppointmentData,
) (CyclopsAppointmentData, error) {

	startTime, err := time.Parse("2006-01-02T15:04:05", apptReq.Start)
	if err != nil {
		if startTime, err = time.Parse(time.RFC3339, apptReq.Start); err != nil {
			return CyclopsAppointmentData{}, fmt.Errorf("invalid start time format: %s", apptReq.Start)
		}
	}

	appointmentData := CyclopsAppointmentData{
		PoID:            apptReq.FreightTrackingID,
		AppointmentDate: startTime.Format("2006-01-02"),
		ProNumber:       apptReq.ProNumber,
		DoorType:        apptReq.DoorType,
		UnloadType:      apptReq.UnloadType,
		Commodity:       apptReq.Commodity,
		ContainerNumber: apptReq.ContainerNumber,
		LinkLoadID:      apptReq.LinkLoadID,
		DepotValue:      apptReq.DepotValue,
		Uom:             apptReq.Uom,
		QtyCount:        apptReq.QtyCount,
		Notes:           apptReq.Notes,
		Warehouse: &models.CyclopsWarehouseInfo{
			ZipCode: apptReq.ZipCode,
		},
	}

	return appointmentData, nil
}

func (l *Lineage) MakeAppointmentWithLoad(
	_ context.Context,
	_ models.MakeAppointmentRequest,
	_ models.Load,
) (models.Appointment, error) {

	return models.Appointment{}, errtypes.NotImplemented(models.Lineage, "MakeAppointmentWithLoad")
}
