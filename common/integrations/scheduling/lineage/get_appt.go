package lineage

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/httplog"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/models"
)

func (l *Lineage) GetAppointment(
	ctx context.Context,
	id string,
) (models.Appointment, error) {

	ctx, span := otel.StartSpan(ctx, "GetAppointment", nil)
	defer func() { span.End(nil) }()

	if l.creds == nil {
		return models.Appointment{}, errors.New("credentials required for appointment operations")
	}

	cyclopsReq := models.CyclopsGetAppointmentRequest{
		CyclopsBaseRequest: models.CyclopsBaseRequest{
			Integration: models.CyclopsSchedulingIntegration,
			Platform:    LineagePlatform,
			Action:      models.ActionGetAppointment,
			UserID:      l.scheduler.Username,
			Credentials: models.CyclopsCredentials{
				Username: l.creds.Username,
				Password: l.creds.Password,
			},
		},
		Appointment: models.CyclopsAppointmentData{
			AppointmentID: id,
		},
	}

	reqBody, err := json.Marshal(cyclopsReq)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to marshal request: %w", err)
	}

	cyclopsURL, err := helpers.GetCyclopsURL()
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to get url: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		cyclopsURL,
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Accept", "application/json")

	resp, err := otel.TracingHTTPClient().Do(httpReq)
	if err != nil {
		httplog.LogHTTPRequestFailed(ctx, l.scheduler, err)
		return models.Appointment{}, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()
	httplog.LogHTTPResponseCode(ctx, l.scheduler, resp.StatusCode)

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to read response: %w", err)
	}

	var apptResp models.CyclopsGetAppointmentResponse
	if err = json.Unmarshal(body, &apptResp); err != nil {
		return models.Appointment{}, fmt.Errorf("failed to unmarshal body: %w", err)
	}

	if !apptResp.Success {
		return models.Appointment{}, &models.CyclopsError{
			Message: apptResp.Message,
			Errors:  apptResp.Errors,
		}
	}

	if len(apptResp.Appointments) == 0 {
		return models.Appointment{}, &models.CyclopsError{
			Message: "no appointments found",
			Errors:  []string{"no appointments found"},
		}
	}

	return convertToAppointmentFromCyclopsAppointment(apptResp.Appointments[0])
}
