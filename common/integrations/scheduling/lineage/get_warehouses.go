package lineage

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strings"

	"github.com/drumkitai/drumkit/common/helpers/httplog"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/models"
)

const (
	turvoGuestURL      = "https://app.turvo.com/guest/appointments/lineage/"
	turvoLocationsURL  = "https://app.turvo.com/api/public/calendar/configuration/cps/locations"
	lineageSharingCode = "lineage"
)

type (
	Location struct {
		ID        string      `json:"id"`
		Name      string      `json:"name"`
		Addresses AddressInfo `json:"addresses"`
	}

	AddressInfo struct {
		Primary bool           `json:"primary"`
		Line1   string         `json:"line1"`
		Line2   string         `json:"line2"`
		Line3   string         `json:"line3"`
		City    LocationField  `json:"city"`
		State   LocationField  `json:"state"`
		Zip     string         `json:"zip"`
		Country CountryField   `json:"country"`
		GPS     GPSCoordinates `json:"gps"`
		Active  bool           `json:"active"`
		Deleted bool           `json:"deleted"`
	}

	LocationField struct {
		Name string `json:"name"`
	}

	CountryField struct {
		Name string `json:"name"`
		Code string `json:"code"`
	}

	GPSCoordinates struct {
		Coordinates []float64 `json:"coordinates"`
	}
)

func (l *Lineage) GetAllWarehouses(
	ctx context.Context,
	_ ...models.SchedulingOption,
) ([]models.Warehouse, error) {

	ctx, span := otel.StartSpan(ctx, "GetAllWarehouses", nil)
	defer func() { span.End(nil) }()

	sessionID, err := l.getTurvoSessionID(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get Turvo session ID: %w", err)
	}

	locations, err := l.fetchLineageLocations(ctx, sessionID)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch Lineage locations: %w", err)
	}

	var warehouses []models.Warehouse
	for _, loc := range locations {
		if loc.Addresses.Deleted || !loc.Addresses.Active {
			continue
		}

		warehouse := convertLineageLocationToWarehouse(loc)
		warehouses = append(warehouses, warehouse)
	}

	return warehouses, nil
}

func (l *Lineage) getTurvoSessionID(ctx context.Context) (string, error) {
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, turvoGuestURL, nil)
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := otel.TracingHTTPClient().Do(req)
	if err != nil {
		httplog.LogHTTPRequestFailed(ctx, l.scheduler, err)
		return "", fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()
	httplog.LogHTTPResponseCode(ctx, l.scheduler, resp.StatusCode)

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	for _, cookie := range resp.Cookies() {
		if cookie.Name == "dtCookie" {
			return cookie.Value, nil
		}
	}

	return "", errors.New("dtCookie not found in response")
}

func (l *Lineage) fetchLineageLocations(ctx context.Context, sessionID string) ([]Location, error) {
	url := fmt.Sprintf("%s?sharingCode=%s", turvoLocationsURL, lineageSharingCode)

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("x-turvo-sessionid", sessionID)
	req.Header.Set("Accept", "application/json")

	resp, err := otel.TracingHTTPClient().Do(req)
	if err != nil {
		httplog.LogHTTPRequestFailed(ctx, l.scheduler, err)
		return nil, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()
	httplog.LogHTTPResponseCode(ctx, l.scheduler, resp.StatusCode)

	if resp.StatusCode != http.StatusOK {
		body, readErr := io.ReadAll(resp.Body)
		if readErr != nil {
			return nil, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
		}
		return nil, fmt.Errorf(
			"unexpected status code: %d, body: %s",
			resp.StatusCode,
			string(body),
		)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	var locations []Location
	if err := json.Unmarshal(body, &locations); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	return locations, nil
}

func convertLineageLocationToWarehouse(loc Location) models.Warehouse {
	addr := loc.Addresses

	addressLine1 := addr.Line1
	if addr.Line2 != "" {
		addressLine1 = fmt.Sprintf("%s, %s", addr.Line1, addr.Line2)
	}
	if addr.Line3 != "" {
		addressLine1 = fmt.Sprintf("%s, %s", addressLine1, addr.Line3)
	}

	addressLine2 := strings.Join([]string{
		addr.City.Name,
		addr.State.Name,
		addr.Zip,
	}, ", ")

	fullAddress := strings.Join([]string{addressLine1, addressLine2}, ", ")
	fullIdentifier := fmt.Sprintf("%s - %s", loc.Name, fullAddress)

	return models.Warehouse{
		Source:                  models.LineageSource,
		WarehouseID:             loc.ID,
		WarehouseName:           loc.Name,
		WarehouseAddressLine1:   addressLine1,
		WarehouseAddressLine2:   addressLine2,
		WarehouseFullAddress:    fullAddress,
		WarehouseFullIdentifier: fullIdentifier,
	}
}
