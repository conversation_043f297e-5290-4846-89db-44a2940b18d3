package retalix

import (
	"context"
	"errors"

	"github.com/drumkitai/drumkit/common/models"
)

// ensureApptFormViewState encapsulates the common logic to obtain the appointment
// form view state, recovering from session errors by clearing session cookies and
// reauthenticating if needed.
func (r *Retalix) ensureApptFormViewState(ctx context.Context) error {
	if err := r.getApptFormViewState(ctx); err != nil {
		var retalixErr *Error
		if errors.As(err, &retalixErr) && retalixErr.Type == SessionError {
			// Clear only session cookies and retry once
			r.clearSessionCookies(ctx)
			if err = r.getApptFormViewState(ctx); err != nil {
				// If still failing, perform full re-auth and retry
				if rea := r.reauthenticateSession(ctx); rea != nil {
					return rea
				}
				if err = r.getApptFormViewState(ctx); err != nil {
					return err
				}
			}
			return nil
		}
		return err
	}

	return nil
}

// validatePOs runs the standard PO validation flow: ensure form state,
// post the form, force validation, and return the validated PO results.
func (r *Retalix) validatePOs(
	ctx context.Context,
	poNumbers []string,
	warehouse models.Warehouse,
) ([]models.ValidatedPONumber, error) {
	if err := r.ensureApptFormViewState(ctx); err != nil {
		return nil, err
	}

	if err := r.postApptFormForValidation(ctx, poNumbers, warehouse); err != nil {
		return nil, err
	}

	if err := r.beginForceApptFormValidation(ctx, warehouse); err != nil {
		return nil, err
	}

	return r.getValidatedApptForm(ctx, warehouse)
}
