package retalix

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/redis"
)

// SerializableCookie represents a cookie that can be stored in Redis
type SerializableCookie struct {
	Name     string    `json:"name"`
	Domain   string    `json:"domain"`
	Path     string    `json:"path"`
	Secure   bool      `json:"secure"`
	HTTPOnly bool      `json:"httpOnly"`
	Expires  time.Time `json:"expires"`
	ValueEnc []byte    `json:"valueEnc"` // Encrypted value
}

const (
	minTTL     = 5 * time.Minute
	maxTTL     = 2 * time.Hour
	defaultTTL = 30 * time.Minute
	ttlBuffer  = 2 * time.Minute
)

// buildCookieCacheKey returns the Redis key for storing Retalix session cookies
func buildCookieCacheKey(scheduler models.Integration) string {
	return fmt.Sprintf("retalix:cookies:%d:%d:%s", scheduler.ServiceID, scheduler.ID, scheduler.Username)
}

// serializeCookies extracts cookies from the jar and encrypts their values
func serializeCookies(ctx context.Context, jar http.CookieJar, u *url.URL) ([]SerializableCookie, error) {
	// Guard against nil jar or URL and return an empty slice
	if jar == nil || u == nil {
		return []SerializableCookie{}, nil
	}

	cookies := jar.Cookies(u)
	if len(cookies) == 0 {
		return []SerializableCookie{}, nil
	}

	serialized := make([]SerializableCookie, 0, len(cookies))
	for _, cookie := range cookies {
		// Encrypt the cookie value
		encryptedValue, err := crypto.EncryptAESGCM(ctx, cookie.Value, nil)
		if err != nil {
			return nil, fmt.Errorf("failed to encrypt cookie %s: %w", cookie.Name, err)
		}

		serialized = append(serialized, SerializableCookie{
			Name:     cookie.Name,
			Domain:   cookie.Domain,
			Path:     cookie.Path,
			Secure:   cookie.Secure,
			HTTPOnly: cookie.HttpOnly,
			Expires:  cookie.Expires,
			ValueEnc: []byte(encryptedValue),
		})
	}

	return serialized, nil
}

// rehydrateCookies reconstructs http.Cookie objects from serialized form and sets them in the jar
func rehydrateCookies(ctx context.Context, jar http.CookieJar, u *url.URL, serialized []SerializableCookie) error {
	if len(serialized) == 0 {
		return nil
	}

	cookies := make([]*http.Cookie, 0, len(serialized))
	for _, sc := range serialized {
		// Decrypt the cookie value
		decryptedValue, err := crypto.DecryptAESGCM(ctx, string(sc.ValueEnc), nil)
		if err != nil {
			return fmt.Errorf("failed to decrypt cookie %s: %w", sc.Name, err)
		}

		cookies = append(cookies, &http.Cookie{
			Name:     sc.Name,
			Value:    decryptedValue,
			Domain:   sc.Domain,
			Path:     sc.Path,
			Secure:   sc.Secure,
			HttpOnly: sc.HTTPOnly,
			Expires:  sc.Expires,
		})
	}

	jar.SetCookies(u, cookies)

	log.Info(
		ctx,
		"rehydrated cookies into jar",
		zap.String("url", u.Host),
		zap.Int("count", len(cookies)),
		zap.Any("cookieNames", cookieNames(cookies)),
	)

	return nil
}

// computeTTL calculates the cache TTL based on cookie expirations
func computeTTL(cookies []*http.Cookie) time.Duration {
	if len(cookies) == 0 {
		return defaultTTL
	}

	var minExpires time.Time
	hasExpires := false

	for _, cookie := range cookies {
		if !cookie.Expires.IsZero() {
			if !hasExpires || cookie.Expires.Before(minExpires) {
				minExpires = cookie.Expires
				hasExpires = true
			}
		}
	}

	if !hasExpires {
		return defaultTTL
	}

	// Calculate TTL with buffer
	ttl := time.Until(minExpires) - ttlBuffer

	// Bound to [minTTL, maxTTL]
	if ttl < minTTL {
		return minTTL
	}
	if ttl > maxTTL {
		return maxTTL
	}

	return ttl
}

// loadCookiesFromRedis retrieves and deserializes cookies from Redis
func loadCookiesFromRedis(ctx context.Context, key string) ([]SerializableCookie, bool, error) {
	cookies, found, err := redis.GetKey[[]SerializableCookie](ctx, key)
	if err != nil {
		// Treat missing keys as not found, not an error
		if errors.Is(err, redis.NilEntry) {
			return nil, false, nil
		}
		return nil, false, fmt.Errorf("failed to get cookies from redis: %w", err)
	}

	if !found {
		return nil, false, nil
	}

	return cookies, true, nil
}

// saveCookiesToRedis serializes and stores cookies in Redis with TTL
func saveCookiesToRedis(ctx context.Context, key string, cookies []SerializableCookie, ttl time.Duration) error {
	if len(cookies) == 0 {
		return nil
	}

	if err := redis.SetKey(ctx, key, cookies, ttl); err != nil {
		return fmt.Errorf("failed to save cookies to redis: %w", err)
	}

	log.Info(
		ctx,
		"saved cookies to redis",
		zap.String("key", key),
		zap.Int("count", len(cookies)),
		zap.Any("cookieNames", serialCookieNames(cookies)),
		zap.Duration("ttl", ttl),
	)

	return nil
}

// deleteCookiesFromRedis removes cached cookies from Redis
func deleteCookiesFromRedis(ctx context.Context, key string) error {
	if err := redis.DeleteKey(ctx, key); err != nil {
		log.Warn(ctx, "failed to delete cookies from redis", zap.String("key", key), zap.Error(err))

		return err
	}

	log.Info(ctx, "deleted cached session cookies")

	return nil
}

// InvalidateSession removes cached session cookies for this Retalix client
func (r *Retalix) InvalidateSession(ctx context.Context) {
	cacheKey := buildCookieCacheKey(r.scheduler)
	if err := deleteCookiesFromRedis(ctx, cacheKey); err != nil {
		log.Warn(ctx, "failed to invalidate session cookies", zap.Error(err))
	}
}

// clearSessionCookies removes likely server-session cookies from the jar while
// preserving authentication cookies. This avoids full re-login while resetting
// Retalix server-side form state tied to the session id.
func (r *Retalix) clearSessionCookies(ctx context.Context) {
	u, err := url.Parse(baseURL)
	if err != nil {
		log.Warn(ctx, "failed to parse base URL when clearing session cookies", zap.Error(err))
		return
	}
	existing := r.cookieJar.Cookies(u)
	if len(existing) == 0 {
		return
	}

	// Heuristics for session cookies (do NOT include auth cookies)
	// Common ASP.NET session cookie name is "ASP.NET_SessionId".
	isSessionCookie := func(name string) bool {
		n := strings.ToLower(name)
		if n == "asp.net_sessionid" {
			return true
		}
		// Fallback: anything that contains "sessionid" but not "auth"
		return strings.Contains(n, "sessionid") && !strings.Contains(n, "auth")
	}

	filtered := make([]*http.Cookie, 0, len(existing))
	removed := make([]string, 0)
	kept := make([]string, 0)

	// Build deletion cookies for session cookies and gather kept cookies
	var deleteCookies []*http.Cookie
	for _, c := range existing {
		if isSessionCookie(c.Name) {
			removed = append(removed, c.Name)
			// Expire the cookie to force deletion in the jar
			del := &http.Cookie{
				Name:    c.Name,
				Value:   "",
				Path:    c.Path,
				Domain:  c.Domain,
				Expires: time.Unix(0, 0),
				MaxAge:  -1,
			}
			deleteCookies = append(deleteCookies, del)
			continue
		}
		kept = append(kept, c.Name)
		filtered = append(filtered, c)
	}

	if len(deleteCookies) > 0 {
		r.cookieJar.SetCookies(u, deleteCookies)
	}

	log.Info(
		ctx,
		"cleared retalix session cookies",
		zap.String("host", u.Host),
		zap.Any("removed", removed),
		zap.Any("kept", kept),
	)

	// Persist filtered (non-session) cookies back to Redis to avoid rehydrating
	// stale session cookies on subsequent requests
	cacheKey := buildCookieCacheKey(r.scheduler)

	// Compute TTL from filtered cookies and serialize
	ttl := computeTTL(filtered)
	serialized, err := serializeCookies(ctx, r.cookieJar, u)
	if err != nil {
		log.Warn(ctx, "failed to serialize filtered cookies", zap.Error(err))
		return
	}
	if err := saveCookiesToRedis(ctx, cacheKey, serialized, ttl); err != nil {
		log.Warn(ctx, "failed to save filtered cookies to redis", zap.Error(err))
	}
}

// cookieNames returns only the names for logging purposes
func cookieNames(cs []*http.Cookie) []string {
	out := make([]string, 0, len(cs))
	for _, c := range cs {
		out = append(out, c.Name)
	}

	return out
}

// serialCookieNames returns only the names from SerializableCookie for logging
func serialCookieNames(cs []SerializableCookie) []string {
	out := make([]string, 0, len(cs))
	for _, c := range cs {
		out = append(out, c.Name)
	}

	return out
}
