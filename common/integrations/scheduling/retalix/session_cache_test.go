package retalix

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/cookiejar"
	"net/url"
	"os"
	"testing"
	"time"

	"github.com/go-redis/redismock/v9"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"golang.org/x/net/publicsuffix"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/redis"
)

func TestMain(m *testing.M) {
	// Set environment to dev to use hardcoded AES key for encryption tests
	os.Setenv("APP_ENV", "dev")
	m.Run()
	os.Unsetenv("APP_ENV")
}

func TestSerializeAndRehydrateCookies(t *testing.T) {
	ctx := context.Background()
	baseURLParsed, err := url.Parse(baseURL)
	require.NoError(t, err)

	tests := []struct {
		name    string
		cookies []*http.<PERSON>ie
	}{
		{
			name: "single cookie with expiration",
			cookies: []*http.Cookie{
				{
					Name:     "session_id",
					Value:    "test-session-123",
					Domain:   baseHost,
					Path:     "/",
					Secure:   true,
					HttpOnly: true,
					Expires:  time.Now().Add(1 * time.Hour),
				},
			},
		},
		{
			name: "multiple cookies",
			cookies: []*http.Cookie{
				{
					Name:     "session_id",
					Value:    "test-session-123",
					Domain:   baseHost,
					Path:     "/",
					Secure:   true,
					HttpOnly: true,
					Expires:  time.Now().Add(1 * time.Hour),
				},
				{
					Name:     "csrf_token",
					Value:    "csrf-token-456",
					Domain:   baseHost,
					Path:     "/",
					Secure:   true,
					HttpOnly: false,
					Expires:  time.Now().Add(2 * time.Hour),
				},
			},
		},
		{
			name: "cookie without expiration",
			cookies: []*http.Cookie{
				{
					Name:     "temp_cookie",
					Value:    "temp-value",
					Domain:   baseHost,
					Path:     "/",
					Secure:   false,
					HttpOnly: false,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a cookie jar and set the test cookies
			jar, err := cookiejar.New(&cookiejar.Options{PublicSuffixList: publicsuffix.List})
			require.NoError(t, err)
			jar.SetCookies(baseURLParsed, tt.cookies)

			// Serialize the cookies
			serialized, err := serializeCookies(ctx, jar, baseURLParsed)
			require.NoError(t, err)
			require.Len(t, serialized, len(tt.cookies))

			// Create a new jar and rehydrate the cookies
			newJar, err := cookiejar.New(&cookiejar.Options{PublicSuffixList: publicsuffix.List})
			require.NoError(t, err)

			err = rehydrateCookies(ctx, newJar, baseURLParsed, serialized)
			require.NoError(t, err)

			// Verify the cookies match
			rehydratedCookies := newJar.Cookies(baseURLParsed)
			require.Len(t, rehydratedCookies, len(tt.cookies))

			for i, original := range tt.cookies {
				rehydrated := rehydratedCookies[i]
				assert.Equal(t, original.Name, rehydrated.Name)
				assert.Equal(t, original.Value, rehydrated.Value)
				// Note: cookie jar returns cookies suitable for the Cookie header and does not
				// retain attributes like Secure, HttpOnly, or Expires on returned cookies.
				// Domain and Path may also be normalized/empty. We therefore only validate
				// name and value here.
			}
		})
	}
}

func TestComputeTTL(t *testing.T) {
	now := time.Now()

	tests := []struct {
		name     string
		cookies  []*http.Cookie
		expected time.Duration
	}{
		{
			name:     "no cookies",
			cookies:  []*http.Cookie{},
			expected: defaultTTL,
		},
		{
			name: "cookies without expiration",
			cookies: []*http.Cookie{
				{Name: "test", Value: "value"},
			},
			expected: defaultTTL,
		},
		{
			name: "single cookie with expiration",
			cookies: []*http.Cookie{
				{Name: "test", Value: "value", Expires: now.Add(1 * time.Hour)},
			},
			expected: 1*time.Hour - ttlBuffer,
		},
		{
			name: "multiple cookies - use earliest expiration",
			cookies: []*http.Cookie{
				{Name: "test1", Value: "value1", Expires: now.Add(2 * time.Hour)},
				{Name: "test2", Value: "value2", Expires: now.Add(30 * time.Minute)},
				{Name: "test3", Value: "value3", Expires: now.Add(1 * time.Hour)},
			},
			expected: 30*time.Minute - ttlBuffer,
		},
		{
			name: "TTL below minimum - should return minTTL",
			cookies: []*http.Cookie{
				{Name: "test", Value: "value", Expires: now.Add(3 * time.Minute)},
			},
			expected: minTTL,
		},
		{
			name: "TTL above maximum - should return maxTTL",
			cookies: []*http.Cookie{
				{Name: "test", Value: "value", Expires: now.Add(5 * time.Hour)},
			},
			expected: maxTTL,
		},
		{
			name: "mix of cookies with and without expiration",
			cookies: []*http.Cookie{
				{Name: "test1", Value: "value1"},
				{Name: "test2", Value: "value2", Expires: now.Add(1 * time.Hour)},
			},
			expected: 1*time.Hour - ttlBuffer,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := computeTTL(tt.cookies)
			// Allow small tolerance for timing differences
			assert.InDelta(t, tt.expected.Seconds(), result.Seconds(), 2.0)
		})
	}
}

func TestRedisIO(t *testing.T) {
	ctx := context.Background()
	baseURLParsed, err := url.Parse(baseURL)
	require.NoError(t, err)
	testKey := "retalix:cookies:123:1:<EMAIL>"

	t.Run("save and load cookies", func(t *testing.T) {
		// Create a mock Redis client
		mockRDB, mock := redismock.NewClientMock()
		originalRDB := redis.RDB
		redis.RDB = mockRDB
		defer func() {
			redis.RDB = originalRDB
			_ = mockRDB.Close()
		}()

		// Create test cookies
		jar, err := cookiejar.New(&cookiejar.Options{PublicSuffixList: publicsuffix.List})
		require.NoError(t, err)

		testCookies := []*http.Cookie{
			{
				Name:     "session_id",
				Value:    "test-session-value",
				Domain:   baseHost,
				Path:     "/",
				Secure:   true,
				HttpOnly: true,
				Expires:  time.Now().Add(1 * time.Hour),
			},
		}
		jar.SetCookies(baseURLParsed, testCookies)

		// Serialize cookies
		serialized, err := serializeCookies(ctx, jar, baseURLParsed)
		require.NoError(t, err)

		// Mock the save operation
		serializedJSON, err := json.Marshal(serialized)
		require.NoError(t, err)
		mock.ExpectSet(testKey, serializedJSON, 10*time.Minute).SetVal("OK")

		err = saveCookiesToRedis(ctx, testKey, serialized, 10*time.Minute)
		require.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())

		// Mock the load operation - return JSON-encoded value
		mock.ExpectGet(testKey).SetVal(string(serializedJSON))

		loaded, found, err := loadCookiesFromRedis(ctx, testKey)
		require.NoError(t, err)
		require.True(t, found)
		require.Len(t, loaded, 1)
		assert.NoError(t, mock.ExpectationsWereMet())

		// Verify the loaded cookie can be rehydrated
		newJar, err := cookiejar.New(&cookiejar.Options{PublicSuffixList: publicsuffix.List})
		require.NoError(t, err)

		err = rehydrateCookies(ctx, newJar, baseURLParsed, loaded)
		require.NoError(t, err)

		rehydratedCookies := newJar.Cookies(baseURLParsed)
		require.Len(t, rehydratedCookies, 1)
		assert.Equal(t, "session_id", rehydratedCookies[0].Name)
		assert.Equal(t, "test-session-value", rehydratedCookies[0].Value)
	})

	t.Run("load non-existent key", func(t *testing.T) {
		// Create a mock Redis client
		mockRDB, mock := redismock.NewClientMock()
		originalRDB := redis.RDB
		redis.RDB = mockRDB
		defer func() {
			redis.RDB = originalRDB
			_ = mockRDB.Close()
		}()

		// Mock returning redis.Nil for non-existent key - GetKey handles this internally
		// The redis wrapper GetKey returns (value, false, nil) for redis.Nil
		mock.ExpectGet("non-existent-key").RedisNil()

		loaded, found, err := loadCookiesFromRedis(ctx, "non-existent-key")
		require.NoError(t, err)
		require.False(t, found)
		require.Nil(t, loaded)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("delete cookies", func(t *testing.T) {
		// Create a mock Redis client
		mockRDB, mock := redismock.NewClientMock()
		originalRDB := redis.RDB
		redis.RDB = mockRDB
		defer func() {
			redis.RDB = originalRDB
			_ = mockRDB.Close()
		}()

		// Mock delete operation
		mock.ExpectDel(testKey).SetVal(1)

		err := deleteCookiesFromRedis(ctx, testKey)
		require.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestBuildCookieCacheKey(t *testing.T) {
	scheduler := models.Integration{
		ServiceID: 123,
		Username:  "<EMAIL>",
	}
	scheduler.ID = 1

	key := buildCookieCacheKey(scheduler)
	expected := "retalix:cookies:123:1:<EMAIL>"
	assert.Equal(t, expected, key)
}

func TestSerializableCookieJSONEncoding(t *testing.T) {
	// Test that SerializableCookie can be properly JSON encoded/decoded
	ctx := context.Background()
	original := SerializableCookie{
		Name:     "test_cookie",
		Domain:   baseHost,
		Path:     "/",
		Secure:   true,
		HTTPOnly: true,
		Expires:  time.Now().Add(1 * time.Hour).Truncate(time.Second),
		ValueEnc: []byte("encrypted_value"),
	}

	// Encode to JSON
	encoded, err := json.Marshal(original)
	require.NoError(t, err)

	// Decode from JSON
	var decoded SerializableCookie
	err = json.Unmarshal(encoded, &decoded)
	require.NoError(t, err)

	// Verify fields match
	assert.Equal(t, original.Name, decoded.Name)
	assert.Equal(t, original.Domain, decoded.Domain)
	assert.Equal(t, original.Path, decoded.Path)
	assert.Equal(t, original.Secure, decoded.Secure)
	assert.Equal(t, original.HTTPOnly, decoded.HTTPOnly)
	assert.WithinDuration(t, original.Expires, decoded.Expires, 1*time.Second)
	assert.Equal(t, original.ValueEnc, decoded.ValueEnc)

	// Test empty slice
	cookies := []SerializableCookie{}
	result, err := serializeCookies(ctx, nil, nil)
	require.NoError(t, err)
	assert.Equal(t, cookies, result)
}
