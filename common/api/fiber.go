package api

import (
	"context"
	"os"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
)

func RunServer(ctx context.Context, app *fiber.App, opts ...Option) <-chan error {
	errChan := make(chan error, 1)
	options := &Options{
		Port: "5000",
	}

	for _, opt := range opts {
		opt(options)
	}

	// set different port if it's an azure function app
	if val, ok := os.LookupEnv("FUNCTIONS_CUSTOMHANDLER_PORT"); ok {
		options.Port = val
	}

	go func() {
		err := app.Listen(":" + options.Port)
		if err != nil {
			log.Error(ctx, "fiber app terminated", zap.Error(err))
			err<PERSON>han <- err
		}
	}()
	return err<PERSON>han
}
