package api

import (
	"encoding/json"
	"fmt"
	"regexp"
)

var tokenPattern = regexp.MustCompile(`[^\s\\"]{40,}`)

func RedactObj(in any) any {
	body, err := json.Marshal(in)
	if err != nil {
		return fmt.<PERSON><PERSON><PERSON>("redactMap: json marshal input failed: %w", err)
	}

	redacted := redactTokens(string(body))

	result := make(map[string]any)
	if err := json.Unmarshal([]byte(redacted), &result); err != nil {
		return fmt.Errorf("redactMap: json unmarshal failed: %w", err)
	}

	return result
}

// Before logging request/response payloads, redact anything that looks like a token
func redactTokens(input string) string {
	return tokenPattern.ReplaceAllStringFunc(input, func(s string) string {
		return fmt.Sprintf("[redacted:len=%d]", len(s))
	})
}
