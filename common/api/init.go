package api

import (
	enLocale "github.com/go-playground/locales/en"
	ut "github.com/go-playground/universal-translator"
	"github.com/go-playground/validator/v10"
	"github.com/go-playground/validator/v10/translations/en"
)

func init() {
	fiberValidator = validator.New()

	// Build an english translator for validation errors
	// https://github.com/go-playground/validator/blob/master/_examples/translations/main.go
	english := enLocale.New()

	var found bool
	if errTranslator, found = ut.New(english, english).GetTranslator("en"); !found {
		panic("no en translator")
	}

	if err := en.RegisterDefaultTranslations(fiberValidator, errTranslator); err != nil {
		panic(err)
	}

	fiberValidator.RegisterTagNameFunc(tagName)
}
