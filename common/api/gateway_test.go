package api

import (
	"context"
	"io"
	"net/http"
	"strings"
	"testing"

	"github.com/aws/aws-lambda-go/events"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestGatewayToHTTPRequestSimpleGet(t *testing.T) {
	t.Parallel()

	request := &events.APIGatewayProxyRequest{
		HTTPMethod: http.MethodGet,
		MultiValueHeaders: map[string][]string{
			"accept":        {"application/json, text/plain, */*"},
			"origin":        {"https://mail.google.com"},
			"Authorization": {"Bearer token"},
			"User-Agent":    {"Mozilla/5.0 AppleWebKit/537.36 Chrome/********* Safari/537.36"},
		},
		Path: "/email/abc123",
	}

	result, err := GatewayToHTTPRequest(context.Background(), request)
	require.NoError(t, err)

	assert.Equal(t, request.HTTPMethod, result.Method)
	assert.Equal(t, "http://localhost:5000/email/abc123", result.URL.String())
	assert.Equal(t, "", result.URL.RawPath)
	assert.Equal(t, "/email/abc123", result.URL.EscapedPath())
	assert.Equal(t, "/email/abc123", result.URL.Path)
	assert.Equal(t, http.Header(request.MultiValueHeaders), result.Header)
	assert.Nil(t, result.Body)
}

// Test for Outlook's threadID's which contain special characters like /'s
func TestGatewayToHTTPRequestGetWithSpecialCharacters(t *testing.T) {
	t.Parallel()

	request := &events.APIGatewayProxyRequest{
		HTTPMethod: http.MethodGet,
		MultiValueHeaders: map[string][]string{
			"accept":        {"application/json, text/plain, */*"},
			"origin":        {"https://mail.google.com"},
			"Authorization": {"Bearer token"},
			"User-Agent":    {"Mozilla/5.0 AppleWebKit/537.36 Chrome/********* Safari/537.36"},
		},
		Path: "/email/abc123%2Fafdb%3D", // %2F = '/', %3D = '='
	}

	result, err := GatewayToHTTPRequest(context.Background(), request)
	require.NoError(t, err)

	assert.Equal(t, request.HTTPMethod, result.Method)
	assert.Equal(t, "http://localhost:5000/email/abc123%2Fafdb%3D", result.URL.String())
	assert.Equal(t, "/email/abc123%2Fafdb%3D", result.URL.RawPath)
	assert.Equal(t, "/email/abc123%2Fafdb%3D", result.URL.EscapedPath())
	assert.Equal(t, "/email/abc123/afdb=", result.URL.Path)
	assert.Equal(t, http.Header(request.MultiValueHeaders), result.Header)
	assert.Nil(t, result.Body)
}

func TestGatewayToHTTPRequestSimpleGetQueryParams(t *testing.T) {
	t.Parallel()

	request := &events.APIGatewayProxyRequest{
		HTTPMethod: http.MethodGet,
		MultiValueQueryStringParameters: map[string][]string{
			"email": {"<EMAIL>"},
		},
		Path: "/user",
	}

	result, err := GatewayToHTTPRequest(context.Background(), request)
	require.NoError(t, err)

	assert.Equal(t, request.HTTPMethod, result.Method)
	assert.Equal(t, "http://localhost:5000/user?email=user%40drumkit.ai", result.URL.String())
	assert.Nil(t, result.Header)
	assert.Nil(t, result.Body)
}

func TestGatewayToHTTPRequestPost(t *testing.T) {
	t.Parallel()

	request := &events.APIGatewayProxyRequest{
		Body:       `{"Code":"google-test-code"}`,
		HTTPMethod: http.MethodPost,
		Path:       "/user/signup",
	}

	result, err := GatewayToHTTPRequest(context.Background(), request)
	require.NoError(t, err)

	assert.Equal(t, request.HTTPMethod, result.Method)
	assert.Equal(t, "http://localhost:5000/user/signup", result.URL.String())
	assert.Nil(t, result.Header)

	body, err := io.ReadAll(result.Body)
	require.NoError(t, err)
	assert.Equal(t, request.Body, string(body))
}

func TestHTTPResponseToGateway(t *testing.T) {
	t.Parallel()

	resp := &http.Response{
		Body: io.NopCloser(strings.NewReader(`{"id":3,"email":"<EMAIL>}"`)),
		Header: map[string][]string{
			"Access-Control-Allow-Headers": {"Content-Type"},
			"Access-Control-Allow-Methods": {"*"},
			"Access-Control-Allow-Origin":  {"*"},
			"Content-Type":                 {"application/json"},
		},
		StatusCode: http.StatusOK,
	}

	result, err := HTTPResponseToGateway(resp)
	require.NoError(t, err)

	expected := &events.APIGatewayProxyResponse{
		Body:              `{"id":3,"email":"<EMAIL>}"`,
		MultiValueHeaders: resp.Header,
		StatusCode:        resp.StatusCode,
	}
	assert.Equal(t, expected, result)
}
