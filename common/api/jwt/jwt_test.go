package jwt

import (
	"testing"
	"time"

	"github.com/golang-jwt/jwt/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestBaseValidation(t *testing.T) {
	t.<PERSON>llel()

	err := baseValidation(jwt.RegisteredClaims{
		Issuer: issuer()},
		TokenTypeAccess,
		[]string{"refresh"},
	)
	assert.ErrorContains(t, err, "invalid type")
}

func TestParseBlank(t *testing.T) {
	t.<PERSON>llel()

	var claims jwt.RegisteredClaims
	assert.Error(t, parse("", &claims))
}

func TestParseInvalidSigningMethod(t *testing.T) {
	t.<PERSON>()

	// Generate a token with a weaker signing method
	var claims jwt.RegisteredClaims
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenStr, err := token.SignedString([]byte("test-key"))
	require.NoError(t, err)

	err = parse(tokenStr, &claims)
	assert.ErrorContains(t, err, "invalid token: signing method HS256 is invalid")
}

func TestParseExpired(t *testing.T) {
	t.Parallel()

	tokenStr, err := newSignedToken(AccessTokenClaims{
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(-time.Minute)),
		}})
	require.NoError(t, err)

	var claims AccessTokenClaims
	err = parse(tokenStr, &claims)
	assert.ErrorContains(t, err, "invalid token: token is expired")
}

func TestJWTAccessToken(t *testing.T) {
	testCases := []struct {
		name       string
		email      string
		opts       []Option
		wantErr    bool
		validateFn func(*testing.T, AccessTokenClaims)
	}{
		{
			name:  "basic dev token",
			email: "<EMAIL>",
			opts: []Option{
				WithAppEnv("dev"),
				WithJWT("test-secret"),
				WithServiceID(ptr(uint(1))),
			},
			validateFn: func(t *testing.T, claims AccessTokenClaims) {
				assert.Equal(t, "<EMAIL>", claims.Email)
				assert.Equal(t, "dev", claims.AppEnv)
				assert.Equal(t, TokenTypeAccess, claims.Type)
				assert.Equal(t, uint(1), *claims.ServiceID)
			},
		},
		{
			name:  "prod token",
			email: "<EMAIL>",
			opts: []Option{
				WithAppEnv("prod"),
				WithJWT("test-secret"),
				WithIssuer("https://api.drumkit.ai"),
				WithServiceID(ptr(uint(1))),
			},
			validateFn: func(t *testing.T, claims AccessTokenClaims) {
				assert.Equal(t, "prod", claims.AppEnv)
			},
		},
		{
			name:  "prod on-prem token",
			email: "<EMAIL>",
			opts: []Option{
				WithAppEnv("prod"),
				WithIsOnPrem(true),
				WithIssuer("https://redwoodlogistics.com"),
				WithJWT("test-secret"),
			},
			validateFn: func(t *testing.T, claims AccessTokenClaims) {
				assert.Equal(t, "prod", claims.AppEnv)
				assert.True(t, claims.IsOnPrem)
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Create token
			token, err := NewAccessToken(tc.email, tc.opts...)
			if tc.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)
			require.NotEmpty(t, token)

			// Parse and validate token
			claims, err := ParseAccessToken(token, tc.opts...)
			require.NoError(t, err)

			// Run custom validations
			if tc.validateFn != nil {
				tc.validateFn(t, claims)
			}

			// Validate expiration
			assert.True(t, claims.ExpiresAt.After(time.Now()))
			assert.True(t, claims.ExpiresAt.Before(time.Now().Add(AccessTokenDuration+time.Minute)))
		})
	}
}

func TestWebhookToken(t *testing.T) {
	token, err := NewWebhookToken(
		"<EMAIL>",
		WithAppEnv("prod"),
		WithJWT("test-secret"),
		WithIssuer("https://api.drumkit.ai"),
		WithServiceID(ptr(uint(1))),
	)

	require.NoError(t, err)
	require.NotEmpty(t, token)

	claims, err := ParseAccessToken(token, WithJWT("test-secret"))
	require.NoError(t, err)

	assert.Equal(t, TokenTypeWebhook, claims.Type)
	assert.True(t, claims.ExpiresAt.After(time.Now()))
	assert.True(t, claims.ExpiresAt.Before(time.Now().Add(WebhookTokenDuration+time.Minute)))
}

func TestInvalidTokens(t *testing.T) {
	testCases := []struct {
		name    string
		setupFn func() (string, []Option, error)
		errMsg  string
	}{
		{
			name: "wrong secret",
			setupFn: func() (string, []Option, error) {
				token, err := NewAccessToken("<EMAIL>", WithJWT("original-secret"))
				return token, []Option{WithJWT("wrong-secret")}, err
			},
			errMsg: "invalid token: signature is invalid",
		},
		{
			name: "missing email",
			setupFn: func() (string, []Option, error) {
				token, err := NewAccessToken("", WithJWT("test-secret"))
				return token, []Option{WithJWT("test-secret")}, err
			},
			errMsg: "missing email claim",
		},
		{
			name: "missing serviceID for non-onprem",
			setupFn: func() (string, []Option, error) {
				token, err := NewAccessToken("<EMAIL>",
					WithJWT("test-secret"),
					WithIsOnPrem(false),
				)
				return token, []Option{WithJWT("test-secret")}, err
			},
			errMsg: "missing serviceID claim",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			token, opts, err := tc.setupFn()
			if err != nil {
				assert.Contains(t, err.Error(), tc.errMsg)
				return
			}

			_, err = ParseAccessToken(token, opts...)
			require.Error(t, err)
			assert.Contains(t, err.Error(), tc.errMsg)
		})
	}
}

// Helper function to create uint pointer
func ptr(u uint) *uint {
	return &u
}
