package jwt

import (
	"fmt"

	"github.com/golang-jwt/jwt/v4"
)

// Opendock JWT interface
type OpendockTokenClaims struct {
	jwt.RegisteredClaims

	IsEmailVerified bool `json:"isEmailVerified"`

	InvalidLoginAttempts uint `json:"invalidLoginAttempts"`
}

func ParseOpendock(tokenStr string, claims *OpendockTokenClaims) error {
	token, err := jwt.ParseWithClaims(tokenStr, claims, func(token *jwt.Token) (any, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}

		return []byte(""), nil
	})

	if (err != nil || !token.Valid) && err.Error() != "signature is invalid" {
		return fmt.Errorf("invalid token: %w", err)
	}

	return nil
}
