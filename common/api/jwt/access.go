package jwt

import (
	"errors"
	"strings"
	"time"

	"github.com/golang-jwt/jwt/v4"
)

const (
	// Allow Drumkit users to be signed in for 30 days
	AccessTokenDuration = 30 * 24 * time.Hour
	// Allow webhook tokens to be used for 5 years
	WebhookTokenDuration = 5 * 365 * 24 * time.Hour
)

// AccessTokenClaims satisfies jwt.Claims interface.
type AccessTokenClaims struct {
	jwt.RegisteredClaims
	AppEnv    string `json:"appEnv"`
	Email     string `json:"email"`
	IsOnPrem  bool   `json:"isOnPrem"`
	ServiceID *uint  `json:"serviceID"`
	Type      string `json:"type"` // "access" or "webhook"
}

func (c AccessTokenClaims) Valid() error {
	err := baseValidation(
		c.RegisteredClaims,
		c.Type,
		[]string{TokenTypeAccess, TokenTypeWebhook},
	)
	if err != nil {
		return err
	}

	validIssuers := getValidIssuers(c.AppEnv, c.Issuer, c.<PERSON>)
	// Use the computed valid issuers for validation
	issuerValid := false
	for _, validIssuer := range validIssuers {
		if c.VerifyIssuer(validIssuer, true) {
			issuerValid = true
			break
		}
	}

	if !issuerValid {
		return errors.New("invalid issuer")
	}

	if c.Email == "" {
		return errors.New("missing email claim")
	}

	if !c.IsOnPrem && c.ServiceID == nil {
		return errors.New("missing serviceID claim")
	}

	return nil
}

func NewAccessToken(email string, opts ...Option) (string, error) {
	options := &Options{
		AppEnv:       "dev",
		IsOnPrem:     false,
		Issuer:       "https://api.drumkit.ai",
		JWT:          "", // the secret key to sign a JWT
		ServiceID:    nil,
		ValidIssuers: nil,
	}

	for _, opt := range opts {
		opt(options)
	}

	// Get the actual issuer first
	actualIssuer := issuer(WithAppEnv(options.AppEnv), WithIssuer(options.Issuer))

	// Then get valid issuers based on the actual issuer
	validIssuers := getValidIssuers(options.AppEnv, actualIssuer, options.IsOnPrem)
	options.ValidIssuers = &validIssuers

	return newSignedToken(
		AccessTokenClaims{
			RegisteredClaims: baseClaims(
				AccessTokenDuration,
				WithAppEnv(options.AppEnv),
				WithIssuer(options.Issuer),
			),
			AppEnv:    options.AppEnv,
			Email:     strings.ToLower(email),
			IsOnPrem:  options.IsOnPrem,
			ServiceID: options.ServiceID,
			Type:      TokenTypeAccess,
			// ValidIssuers: options.ValidIssuers,
		},
		WithJWT(options.JWT),
	)
}

func NewWebhookToken(email string, opts ...Option) (string, error) {
	options := &Options{
		AppEnv:       "dev",
		IsOnPrem:     false,
		Issuer:       "https://api.drumkit.ai",
		JWT:          "", // the secret key to sign a JWT
		ServiceID:    nil,
		ValidIssuers: nil,
	}

	for _, opt := range opts {
		opt(options)
	}

	// Get the actual issuer first
	actualIssuer := issuer(WithAppEnv(options.AppEnv), WithIssuer(options.Issuer))

	// Then get valid issuers based on the actual issuer
	validIssuers := getValidIssuers(options.AppEnv, actualIssuer, options.IsOnPrem)
	options.ValidIssuers = &validIssuers

	return newSignedToken(
		AccessTokenClaims{
			RegisteredClaims: baseClaims(
				WebhookTokenDuration,
				WithAppEnv(options.AppEnv),
				WithIssuer(options.Issuer),
			),
			AppEnv:    options.AppEnv,
			Email:     strings.ToLower(email),
			IsOnPrem:  options.IsOnPrem,
			ServiceID: options.ServiceID,
			Type:      TokenTypeWebhook,
		},
		WithJWT(options.JWT),
	)
}

func ParseAccessToken(tokenStr string, opts ...Option) (claims AccessTokenClaims, err error) {
	options := &Options{
		JWT: "",
	}

	for _, opt := range opts {
		opt(options)
	}

	err = parse(tokenStr, &claims, WithJWT(options.JWT))

	return
}

func getValidIssuers(appEnv, issuer string, isOnPrem bool) []string {
	validIssuers := []string{issuer}

	switch appEnv {
	case "prod":
		oldDrumkitDomain := "https://beacon-api.axleapi.com"
		redwoodDomain := "https://redwoodlogistics.com"
		if isOnPrem {
			return []string{redwoodDomain}
		}

		validIssuers = append(validIssuers, oldDrumkitDomain, redwoodDomain)
	case "staging":
		validIssuers = append(validIssuers, "https://api-staging.drumkit.ai")
	default:
		validIssuers = append(validIssuers, "http://localhost")
	}

	return validIssuers
}
