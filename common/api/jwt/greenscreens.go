package jwt

import (
	"fmt"

	"github.com/golang-jwt/jwt/v4"
)

// Greenscreens JWT interface
type GreenscreensTokenClaims struct {
	jwt.RegisteredClaims

	ClientID string `json:"clientId"`
}

func ParseGreenscreens(tokenStr string, claims *GreenscreensTokenClaims) error {
	token, _, err := jwt.NewParser().ParseUnverified(tokenStr, claims)

	if err != nil || token == nil {
		return fmt.Errorf("invalid token: %w", err)
	}

	return nil
}
