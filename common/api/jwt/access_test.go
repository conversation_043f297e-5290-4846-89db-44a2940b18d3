package jwt

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestAccessToken(t *testing.T) {
	t.<PERSON>llel()

	serviceID := uint(1)

	accessToken, err := NewAccessToken("<EMAIL>", WithServiceID(&serviceID))
	require.NoError(t, err)

	claims, err := ParseAccessToken(accessToken)
	require.NoError(t, err)

	assert.NotEmpty(t, claims.RegisteredClaims)
	expected := AccessTokenClaims{
		RegisteredClaims: claims.RegisteredClaims,
		AppEnv:           "dev",
		Email:            "<EMAIL>",
		ServiceID:        &serviceID,
		Type:             TokenTypeAccess,
	}

	assert.Equal(t, expected, claims)
}

func TestAccessTokenCaseInsensitive(t *testing.T) {
	t.Parallel()

	serviceID := uint(1)

	accessToken, err := NewAccessToken("<EMAIL>", WithServiceID(&serviceID))
	require.NoError(t, err)

	claims, err := ParseAccessToken(accessToken)
	require.NoError(t, err)

	assert.NotEmpty(t, claims.RegisteredClaims)
	expected := AccessTokenClaims{
		RegisteredClaims: claims.RegisteredClaims,
		AppEnv:           "dev",
		Email:            "<EMAIL>",
		ServiceID:        &serviceID,
		Type:             TokenTypeAccess,
	}

	assert.Equal(t, expected, claims)
}

func TestAccessTokenInProd(t *testing.T) {
	t.Parallel()

	serviceID := uint(2)

	accessToken, err := NewAccessToken(
		"<EMAIL>",
		WithAppEnv("prod"),
		WithServiceID(&serviceID),
	)
	require.NoError(t, err)

	claims, err := ParseAccessToken(accessToken)
	require.NoError(t, err)

	expectedServiceID := uint(2)

	assert.NotEmpty(t, claims.RegisteredClaims)
	expected := AccessTokenClaims{
		RegisteredClaims: claims.RegisteredClaims,
		AppEnv:           "prod",
		Email:            "<EMAIL>",
		IsOnPrem:         false,
		ServiceID:        &expectedServiceID,
		Type:             TokenTypeAccess,
		// ValidIssuers: &[]string{
		// 	"https://api.drumkit.ai",
		// 	"https://beacon-api.axleapi.com",
		// 	"https://redwoodlogistics.com",
		// },
	}

	assert.Equal(t, expected, claims)
}

func TestAccessTokenOnPremInDev(t *testing.T) {
	t.Parallel()

	accessToken, err := NewAccessToken("<EMAIL>", WithIsOnPrem(true))
	require.NoError(t, err)

	claims, err := ParseAccessToken(accessToken)
	require.NoError(t, err)

	assert.NotEmpty(t, claims.RegisteredClaims)
	expected := AccessTokenClaims{
		RegisteredClaims: claims.RegisteredClaims,
		AppEnv:           "dev",
		Email:            "<EMAIL>",
		IsOnPrem:         true,
		ServiceID:        nil,
		Type:             TokenTypeAccess,
	}

	assert.Equal(t, expected, claims)
}

func TestAccessTokenOnPremInProd(t *testing.T) {
	t.Parallel()

	accessToken, err := NewAccessToken(
		"<EMAIL>",
		WithAppEnv("prod"),
		WithIsOnPrem(true),
		WithIssuer("https://redwoodlogistics.com"),
	)
	require.NoError(t, err)

	claims, err := ParseAccessToken(accessToken)
	require.NoError(t, err)

	assert.NotEmpty(t, claims.RegisteredClaims)
	expected := AccessTokenClaims{
		RegisteredClaims: claims.RegisteredClaims,
		AppEnv:           "prod",
		Email:            "<EMAIL>",
		IsOnPrem:         true,
		ServiceID:        nil,
		Type:             TokenTypeAccess,
		// ValidIssuers:     &[]string{"https://redwoodlogistics.com"},
	}

	assert.Equal(t, expected, claims)
}

func TestAccessTokenMissingEmailClaim(t *testing.T) {
	t.Parallel()

	serviceID := uint(1)

	accessToken, err := NewAccessToken("", WithServiceID(&serviceID))
	require.NoError(t, err)

	_, err = ParseAccessToken(accessToken)
	assert.ErrorContains(t, err, "missing email claim")
}

func TestAccessTokenMissingServiceIDClaim(t *testing.T) {
	t.Parallel()

	accessToken, err := NewAccessToken("<EMAIL>", WithIsOnPrem(false))
	require.NoError(t, err)

	_, err = ParseAccessToken(accessToken)
	assert.ErrorContains(t, err, "missing serviceID claim")
}
