// Package jwt handles token parsing and generation for access tokens
package jwt

import (
	"context"
	"errors"
	"fmt"
	"slices"
	"time"

	"github.com/golang-jwt/jwt/v4"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
)

const (
	// TokenTypeAccess is the login/bearer token - the token used to authenticate with the API - either Drumkit's or
	// a customer's on-prem API.
	TokenTypeAccess = "access"
	// TokenTypeWebhook is the persistent token - the token used to authenticate with the API - either Drumkit's or
	// a customer's on-prem API.
	TokenTypeWebhook = "webhook"
)

// Every token includes issuer and expiration information.
func baseClaims(duration time.Duration, opts ...Option) jwt.RegisteredClaims {
	options := &Options{
		AppEnv: "",
		Issuer: "https://api.drumkit.ai",
	}

	for _, opt := range opts {
		opt(options)
	}

	now := time.Now()

	return jwt.RegisteredClaims{
		Issuer:    issuer(WithAppEnv(options.AppEnv), <PERSON><PERSON><PERSON><PERSON>(options.Issuer)),
		IssuedAt:  jwt.NewNumericDate(now),
		ExpiresAt: jwt.NewNumericDate(now.Add(duration)),
	}
}

// The API service associated with the given environment.
// Tokens are only valid for the environment in which they are generated.
func issuer(opts ...Option) string {
	options := &Options{
		AppEnv: "dev",
		Issuer: "https://api.drumkit.ai",
	}

	for _, opt := range opts {
		opt(options)
	}

	if options.AppEnv == "prod" {
		return options.Issuer
	}

	if options.AppEnv == "staging" {
		return "https://api-staging.drumkit.ai"
	}

	return "http://localhost"
}

func newSignedToken(claims jwt.Claims, opts ...Option) (string, error) {
	options := &Options{
		JWT: "",
	}

	for _, opt := range opts {
		opt(options)
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS512, claims)

	return token.SignedString([]byte(options.JWT))
}

func baseValidation(claims jwt.RegisteredClaims, tokenType string, expectedTypes []string) error {
	if err := claims.Valid(); err != nil {
		return err
	}

	if !slices.Contains(expectedTypes, tokenType) {
		return errors.New("invalid type")
	}

	return nil
}

func parse(tokenStr string, claims jwt.Claims, opts ...Option) error {
	options := &Options{
		JWT: "",
	}

	for _, opt := range opts {
		opt(options)
	}

	token, err := jwt.ParseWithClaims(
		tokenStr,
		claims,
		func(_ *jwt.Token) (any, error) {
			return []byte(options.JWT), nil
		},
		jwt.WithValidMethods([]string{"HS512"}),
	)

	if err != nil || !token.Valid {
		log.Error(
			context.Background(),
			"error parsing token",
			zap.Any("token", token),
			zap.Any("tokenStr", tokenStr),
			zap.Any("claims", claims),
			zap.Error(err),
		)
		return fmt.Errorf("invalid token: %w", err)
	}

	log.Info(context.Background(), "token correctly parsed", zap.Any("token", token))

	return nil
}
