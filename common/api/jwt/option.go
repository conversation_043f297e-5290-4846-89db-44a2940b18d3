package jwt

type (
	Options struct {
		AppEnv       string
		IsOnPrem     bool
		Issuer       string
		JWT          string
		ServiceID    *uint
		ValidIssuers *[]string
	}

	Option func(o *Options)
)

func WithAppEnv(env string) Option {
	return func(o *Options) {
		o.AppEnv = env
	}
}

func WithIsOnPrem(isOnPrem bool) Option {
	return func(o *Options) {
		o.IsOnPrem = isOnPrem
	}
}

func WithIssuer(issuer string) Option {
	return func(o *Options) {
		o.Issuer = issuer
	}
}

func WithJWT(jwt string) Option {
	return func(o *Options) {
		o.JWT = jwt
	}
}

func WithServiceID(id *uint) Option {
	return func(o *Options) {
		o.ServiceID = id
	}
}

func WithValidIssuers(issuers *[]string) Option {
	return func(o *Options) {
		o.ValidIssuers = issuers
	}
}
