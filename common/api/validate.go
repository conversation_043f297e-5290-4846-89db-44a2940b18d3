package api

import (
	"errors"
	"fmt"
	"reflect"
	"strings"

	ut "github.com/go-playground/universal-translator"
	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"
)

var (
	// These are designed to be created only once and cached
	fiberValidator *validator.Validate
	errTranslator  ut.Translator
)

// Parse and validate path, query, and JSON body parameters from a Fiber request.
//
// path is an optional pointer to a struct with `params` and `validate` tags for fiber.ParamsParser()
// query is an optional pointer to a struct with `query` and `validate` tags for fiber.QueryParser()
// body is an optional pointer to a struct with `json` and `validate` tags for fiber.BodyParser()
//
// An error is returned if either parsing or validation failed.
// An effort is made to make these error messages useful - the error is designed to be sent to the user.
func Parse(c *fiber.Ctx, path, query, body any) error {
	if path != nil {
		if err := c.Params<PERSON>er(path); err != nil {
			return fmt.Errorf("path parsing failed: %w", err)
		}

		if err := fiberValidator.Struct(path); err != nil {
			return fmt.Errorf("path validation failed: %w", friendlyValidationErr(err))
		}
	}

	if query != nil {
		if err := c.QueryParser(query); err != nil {
			return fmt.Errorf("query parsing failed: %w", err)
		}

		if err := fiberValidator.Struct(query); err != nil {
			return fmt.Errorf("query validation failed: %w", friendlyValidationErr(err))
		}
	}

	if body != nil {
		if err := c.BodyParser(body); err != nil {
			return fmt.Errorf("body parsing failed: %w", err)
		}

		if err := fiberValidator.Struct(body); err != nil {
			return fmt.Errorf("body validation failed: %w", friendlyValidationErr(err))
		}
	}

	return nil
}

func friendlyValidationErr(err error) error {
	var errs validator.ValidationErrors
	if !errors.As(err, &errs) {
		return err // some other error, return as-is (e.g. validator.InvalidValidationError)
	}

	errStrings := make([]string, 0, len(errs))
	for _, fieldError := range errs {
		errStrings = append(errStrings, fieldError.Translate(errTranslator))
	}

	return errors.New(strings.Join(errStrings, "; "))
}

// Read struct tags to determine the user-friendly field name for error messages
func tagName(field reflect.StructField) string {
	// The user-visible field name could be tagged with "params" (path), "query", or "json" (body)
	if name := field.Tag.Get("params"); name != "" {
		return name
	}

	if name := field.Tag.Get("query"); name != "" {
		return name
	}

	if name := field.Tag.Get("json"); name != "" {
		// json tags can have modifiers after a comma, e.g. `json:"fieldName,omitempty"`
		// if the field is tagged `json:"-"`, fallback to the actual struct name
		if result := strings.SplitN(name, ",", 2)[0]; result != "-" {
			return result
		}
	}

	// Otherwise, fallback to the actual struct name
	return field.Name
}
