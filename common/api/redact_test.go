package api

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestRedactTokensInput(t *testing.T) {
	t.<PERSON><PERSON>()

	input := `received APIGatewayProxyRequest:
{
    "resource": "/{path+}",
    "path": "/email/thread/18b20f44b053dbce",
    "httpMethod": "GET",
    "headers": {
        "Authorization": "Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3",
`

	expected := `received APIGatewayProxyRequest:
{
    "resource": "/{path+}",
    "path": "/email/thread/18b20f44b053dbce",
    "httpMethod": "GET",
    "headers": {
        "Authorization": "Bearer [redacted:len=67]",
`
	assert.Equal(t, expected, redactTokens(input))
}

func TestRedactTokensOutput(t *testing.T) {
	t.<PERSON>l()

	input := `returning APIGatewayProxyResponse: {
"statusCode":200,
"body":"{
	\"access_token\":\"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJTcwMDmmk6TRBY-hx1\",
	\"token_expiration\":1703189106,
	\"email\":\"<EMAIL>\",
	\"token_type\":\"Bearer\"}"}`

	expected := `returning APIGatewayProxyResponse: {
"statusCode":200,
"body":"{
	\"access_token\":\"[redacted:len=65]\",
	\"token_expiration\":1703189106,
	\"email\":\"<EMAIL>\",
	\"token_type\":\"Bearer\"}"}`
	assert.Equal(t, expected, redactTokens(input))
}
