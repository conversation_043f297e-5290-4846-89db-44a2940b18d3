package middleware

type (
	Options struct {
		AppEnv              string
		ContextKey          string
		CORSOrigins         string // a comma separated list of origins that may access the resource.
		IsOnPrem            bool
		JWT                 string
		ServiceIDContextKey string
		UserIDContextKey    string
	}

	Option func(o *Options)
)

func WithAppEnv(env string) Option {
	return func(o *Options) {
		o.AppEnv = env
	}
}

func WithContextKey(key string) Option {
	return func(o *Options) {
		o.ContextKey = key
	}
}

func WithCORSOrigins(origins string) Option {
	return func(o *Options) {
		o.CORSOrigins = origins
	}
}

func WithIsOnPrem(isOnPrem bool) Option {
	return func(o *Options) {
		o.IsOnPrem = isOnPrem
	}
}

func WithJWT(jwt string) Option {
	return func(o *Options) {
		o.JWT = jwt
	}
}

func WithServiceIDContextKey(key string) Option {
	return func(o *Options) {
		o.ServiceIDContextKey = key
	}
}

func WithUserIDContextKey(key string) Option {
	return func(o *Options) {
		o.UserIDContextKey = key
	}
}
