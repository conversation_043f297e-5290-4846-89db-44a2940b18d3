package middleware

import (
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
)

func CORS(opts ...Option) fiber.Handler {
	options := &Options{
		CORSOrigins: "http://localhost",
	}

	for _, opt := range opts {
		opt(options)
	}

	return cors.New(cors.Config{
		AllowOrigins:     options.CORSOrigins,
		AllowMethods:     cors.ConfigDefault.AllowMethods,
		AllowCredentials: false,
	})
}
