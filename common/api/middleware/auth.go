package middleware

import (
	"crypto/sha256"
	"encoding/base64"
	"errors"
	"fmt"
	"net/http"
	"slices"
	"strings"
	"time"

	fiber "github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/utils"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api/jwt"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds/user/useraccessor"
	utilsentry "github.com/drumkitai/drumkit/common/sentry"
)

var (
	contextKey          = ""
	serviceIDContextKey = ""
	userIDContextKey    = ""
)

// ParseToken ensures the JWT is valid and adds it to the Fiber context.
// This does not check the user against the DB, nor does it authorize the request.
func ParseToken(opts ...Option) fiber.Handler {
	options := &Options{
		ContextKey:          "",
		IsOnPrem:            false,
		JWT:                 "",
		ServiceIDContextKey: "",
		UserIDContextKey:    "",
	}

	for _, opt := range opts {
		opt(options)
	}

	contextKey = options.ContextKey
	serviceIDContextKey = options.ServiceIDContextKey
	userIDContextKey = options.UserIDContextKey

	return func(c *fiber.Ctx) error {
		ctx := c.UserContext()
		log.Debug(ctx, "to-be parsed untrimmed JWT token", zap.String("token", c.Get("Authorization")))

		token := utils.CopyString(strings.TrimPrefix(c.Get("Authorization"), "Bearer "))
		if token == "" {
			return c.Status(http.StatusUnauthorized).SendString("missing auth/bearer token")
		}

		log.Debug(ctx, "to-be parsed trimmed JWT token", zap.String("token", token))
		claims, err := jwt.ParseAccessToken(token, jwt.WithJWT(options.JWT))
		if err != nil {
			log.Warn(ctx, "error parsing JWT token", zap.Any("claims", claims), zap.Error(err))
			return c.Status(http.StatusUnauthorized).SendString(err.Error())
		}

		// For webhook tokens, we don't need to validate the user exists
		// Just validate the token and extract ServiceID from claims
		if claims.Type == jwt.TokenTypeWebhook {
			// Attach the parsed JWT claims to the context for routes to read as needed
			c.Locals(options.ContextKey, claims)

			if options.ServiceIDContextKey != "" && claims.ServiceID != nil {
				c.Locals(options.ServiceIDContextKey, *claims.ServiceID)
			}

			if options.UserIDContextKey != "" {
				// For webhook tokens, we might not have a user ID, so we'll skip this
				// or set it to 0 if needed
				c.Locals(options.UserIDContextKey, uint(0))
			}

			ctx = log.With(ctx, zap.String("userEmail", claims.Email))
			c.SetUserContext(ctx)

			log.Info(ctx, "found valid webhook token", zap.Duration("expiresIn", time.Until(claims.ExpiresAt.Time)))

			return c.Next()
		}

		modelType := models.InternalUserType

		if claims.IsOnPrem && serviceIDContextKey == "" {
			modelType = models.OnPremUserType
		}

		// Verify the associated user exists
		user, err := useraccessor.GetByEmail(ctx, claims.Email, modelType)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				// user no longer exists (may have been deleted)
				log.Warn(
					ctx,
					"user does not exist",
					zap.String("email", claims.Email),
					zap.Error(err),
				)

				return c.Status(http.StatusUnauthorized).SendString(
					fmt.Sprintf("user %s does not exist", claims.Email))

			}

			log.Warn(
				ctx,
				"user not found",
				zap.String("email", claims.Email),
				zap.Error(err),
			)

			// some other DB error
			return c.SendStatus(http.StatusInternalServerError)
		}

		// Ensure the token has not been invalidated (gets invalidated on /logout)
		hashedToken := HashedToken(token)
		if len(user.GetHashedSessionTokens()) == 0 {
			// Temporarily trust-on-first-use: update the user with the hashed session token
			// (to backfill)
			// TODO: remove this case after all users have hashed session tokens in the DB
			// https://linear.app/drumkit/issue/ENG-1659/require-non-empty-hashed-session-token
			tokens := append(user.GetHashedSessionTokens(), hashedToken)
			user.SetHashedSessionTokens(tokens)

			if err := useraccessor.Update(ctx, user); err != nil {
				log.Error(ctx, "error updating user's hashed session tokens", zap.Error(err))
			}

		} else if !slices.Contains(user.GetHashedSessionTokens(), hashedToken) {
			// otherwise if we haven't seen this token, tell user to login
			return c.Status(http.StatusUnauthorized).SendString("token invalidated")
		}

		// Attach the parsed JWT claims to the context for routes to read as needed
		c.Locals(options.ContextKey, claims)

		if options.ServiceIDContextKey != "" && !options.IsOnPrem {
			if s, ok := user.(models.ServiceIDAccessor); ok {
				// TODO: For now, assign the the ServiceID to the context here
				// Later, we can assign the ServiceID directly into the JWT Claims
				c.Locals(options.ServiceIDContextKey, s.GetServiceID())
			}
		}

		if options.UserIDContextKey != "" {
			c.Locals(options.UserIDContextKey, user.GetID())
		}

		ctx = log.With(ctx, zap.String("userEmail", user.GetEmailAddress()), zap.Uint("userID", user.GetID()))
		c.SetUserContext(ctx)
		utilsentry.SetUser(ctx, user)

		log.Info(ctx, "found valid access token", zap.Duration("expiresIn", time.Until(claims.ExpiresAt.Time)))

		return c.Next()
	}
}

// HashedToken returns the base64-encoded sha-256 hash of the given token
func HashedToken(token string) string {
	return base64.StdEncoding.EncodeToString(sha256.New().Sum([]byte(token)))
}

// ClaimsFromContext will read the JWT claims from the Fiber context.
//
// Each protected route handler should call this to validate the claim against the request details.
func ClaimsFromContext(c *fiber.Ctx) jwt.AccessTokenClaims {
	return c.Locals(contextKey).(jwt.AccessTokenClaims)
}

// ServiceIDFromContext will read the Service ID from the Fiber context.
//
// Only use this for lambdas hosted by Drumkit e.g., `fn/api` and not `fn/onprem`.
func ServiceIDFromContext(c *fiber.Ctx) uint {
	return c.Locals(serviceIDContextKey).(uint)
}

// UserIDFromContext will read the User ID from the Fiber context.
//
// Only use this for lambdas hosted by Drumkit e.g., `fn/api` and not `fn/onprem`.
func UserIDFromContext(c *fiber.Ctx) uint {
	return c.Locals(userIDContextKey).(uint)
}
