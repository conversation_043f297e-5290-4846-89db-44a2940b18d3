package middleware

import (
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
)

func Zap(opts ...Option) fiber.Handler {
	options := &Options{
		AppEnv: "",
	}

	for _, opt := range opts {
		opt(options)
	}

	return func(c *fiber.Ctx) error {
		start := time.Now()

		ctx := log.NewFromEnv(c.UserContext(), zap.String("method", c.Method()), zap.String("path", c.Path()))
		c.SetUserContext(ctx)

		if err := c.Next(); err != nil {
			log.Error(ctx, "fiber error", zap.Error(err))
			return err
		}

		resp := c.Response()
		if resp == nil {
			return nil
		}

		code := resp.StatusCode()
		ctx = log.With(ctx, zap.Int("status", code), zap.Duration("latency", time.Since(start)))

		switch {
		case code < 400:
			log.Info(ctx, "ok", zap.Int("responseSize", len(resp.Body())))
		case code < 500:
			log.WarnNoSentry(ctx, "client error", zap.ByteString("responseBody", resp.Body()))
		default:
			log.ErrorNoSentry(ctx, "server error", zap.ByteString("responseBody", resp.Body()))
		}

		// In local dev, flush after each request for near synchronous streaming to Axiom; without it,
		// the logs will not be sent to Axiom as the buffer will not be flushed. In staging/prod Lambda,
		// the call to log.Flush in handlerWithLogging() takes care of this already
		if options.AppEnv == "dev" {
			go log.Flush(ctx)
		}

		return nil
	}
}
