package load

import (
	"os"
	"testing"

	"github.com/go-redis/redismock/v9"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	rediscommon "github.com/drumkitai/drumkit/common/redis"
)

func TestPrepareLoadForUpsert(t *testing.T) {
	if os.Getenv("TEST_LIVE_DB") != "true" {
		t.Skip("skipping test: run with TEST_LIVE_DB=true to enable")
		return
	}

	mockRDB, _ := redismock.NewClientMock()
	rediscommon.RDB = mockRDB
	t.Cleanup(func() {
		_ = mockRDB.Close()
		rediscommon.RDB = nil
	})

	ctx := rds.SetupTestDB(t)
	service := rds.CreateTestService(ctx, t)
	tms := rds.CreateTestIntegrationByType(ctx, t, service.ID, models.TMS)

	baseLoad := models.Load{
		ServiceID: service.ID,
		TMSID:     tms.ID,
		LoadCoreInfo: models.LoadCoreInfo{
			Pickup: models.Pickup{
				CompanyCoreInfo: models.CompanyCoreInfo{
					Zipcode: "02118",
				},
			},
			Consignee: models.Consignee{
				CompanyCoreInfo: models.CompanyCoreInfo{
					Zipcode: "02139",
				},
			},
		},
	}

	t.Run("success - new load sets zip prefixes", func(t *testing.T) {
		newLoad := baseLoad
		newLoad.FreightTrackingID = "TEST001"

		isNewLoad, err := prepareLoadForUpsert(ctx, &newLoad, &service)
		require.NoError(t, err)
		assert.True(t, isNewLoad)
		assert.Equal(t, "021", newLoad.Pickup.ZipPrefix)
		assert.Equal(t, "021", newLoad.Consignee.ZipPrefix)
	})

	t.Run("success - existing load returns false", func(t *testing.T) {
		// Create an existing load
		existingLoad := baseLoad
		existingLoad.FreightTrackingID = "TEST002"
		require.NoError(t, rds.WithContext(ctx).Create(&existingLoad).Error)

		// Try to prepare the same load
		newLoad := baseLoad
		newLoad.FreightTrackingID = "TEST002"

		isNewLoad, err := prepareLoadForUpsert(ctx, &newLoad, &service)
		require.NoError(t, err)
		assert.False(t, isNewLoad)
	})
}

func TestUpsertLoad(t *testing.T) {
	if os.Getenv("TEST_LIVE_DB") != "true" {
		t.Skip("skipping test: run with TEST_LIVE_DB=true to enable")
		return
	}

	mockRDB, _ := redismock.NewClientMock()
	rediscommon.RDB = mockRDB
	t.Cleanup(func() {
		_ = mockRDB.Close()
		rediscommon.RDB = nil
	})

	ctx := rds.SetupTestDB(t)
	service := rds.CreateTestService(ctx, t)
	tms := rds.CreateTestIntegrationByType(ctx, t, service.ID, models.TMS)

	baseLoad := models.Load{
		ServiceID: service.ID,
		TMSID:     tms.ID,
		LoadCoreInfo: models.LoadCoreInfo{
			Pickup: models.Pickup{
				CompanyCoreInfo: models.CompanyCoreInfo{
					AddressLine1: "123 Main St",
					City:         "Boston",
					State:        "MA",
					Zipcode:      "02118",
				},
			},
			Consignee: models.Consignee{
				CompanyCoreInfo: models.CompanyCoreInfo{
					AddressLine1: "456 Elm St",
					City:         "Cambridge",
					State:        "MA",
					Zipcode:      "02139",
				},
			},
		},
	}

	t.Run("success - upsert new load sets zip prefixes", func(t *testing.T) {
		newLoad := baseLoad
		newLoad.FreightTrackingID = "TEST_UPSERT001"

		err := UpsertLoad(ctx, &newLoad, &service)
		require.NoError(t, err)
		assert.NotZero(t, newLoad.ID)
		assert.Equal(t, "021", newLoad.Pickup.ZipPrefix)
		assert.Equal(t, "021", newLoad.Consignee.ZipPrefix)

		// Verify load was persisted
		var dbLoad models.Load
		err = rds.WithContext(ctx).Where("freight_tracking_id = ?", "TEST_UPSERT001").First(&dbLoad).Error
		require.NoError(t, err)
		assert.Equal(t, newLoad.ID, dbLoad.ID)
	})

	t.Run("success - upsert new load with commodities", func(t *testing.T) {
		newLoad := baseLoad
		newLoad.FreightTrackingID = "TEST_UPSERT002"
		newLoad.Commodities = []models.Commodity{
			{
				Description: "Test Commodity 1",
				WeightTotal: 1000,
			},
			{
				Description: "Test Commodity 2",
				WeightTotal: 2000,
			},
		}

		err := UpsertLoad(ctx, &newLoad, &service)
		require.NoError(t, err)
		assert.NotZero(t, newLoad.ID)
		assert.Len(t, newLoad.Commodities, 2)

		// Verify commodities were persisted
		var dbCommodities []models.Commodity
		err = rds.WithContext(ctx).Where("load_id = ?", newLoad.ID).Find(&dbCommodities).Error
		require.NoError(t, err)
		assert.Len(t, dbCommodities, 2)
		assert.Equal(t, "Test Commodity 1", dbCommodities[0].Description)
		assert.Equal(t, "Test Commodity 2", dbCommodities[1].Description)
	})

	t.Run("success - upsert existing load updates it", func(t *testing.T) {
		// Create an existing load
		existingLoad := baseLoad
		existingLoad.FreightTrackingID = "TEST_UPSERT003"
		existingLoad.Pickup.City = "Original City"
		require.NoError(t, rds.WithContext(ctx).Create(&existingLoad).Error)

		// Update the load
		updatedLoad := baseLoad
		updatedLoad.FreightTrackingID = "TEST_UPSERT003"
		updatedLoad.Pickup.City = "Updated City"

		err := UpsertLoad(ctx, &updatedLoad, &service)
		require.NoError(t, err)
		assert.Equal(t, existingLoad.ID, updatedLoad.ID) // Same ID means it was updated, not created

		// Verify the update
		var dbLoad models.Load
		err = rds.WithContext(ctx).Where("freight_tracking_id = ?", "TEST_UPSERT003").First(&dbLoad).Error
		require.NoError(t, err)
		assert.Equal(t, "Updated City", dbLoad.Pickup.City)
	})
}
