package appt

import (
	"context"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

func GetByServiceIDAccountAndFreightTrackingIDForEmail(
	ctx context.Context,
	serviceID uint,
	account,
	freightTrackingID string,
) (appt models.Appointment, err error) {

	return appt, rds.WithContext(ctx).
		Where("service_id = ?", serviceID).
		Where("account = ?", account).
		Where("freight_tracking_id = ?", freightTrackingID).
		Where("email_body IS NOT NULL").
		First(&appt).Error
}
