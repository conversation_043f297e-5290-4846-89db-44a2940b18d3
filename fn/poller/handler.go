package main

import (
	"context"
	"errors"
	"fmt"
	"os"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	"github.com/drumkitai/drumkit/common/sentry"
)

const (
	EasternTimezone = "America/New_York"
	MaxWorkers      = 100
)

func handler(ctx context.Context) (err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "handler", nil)
	defer func() {
		if err != nil {
			log.Error(ctx, "all polling jobs failed", zap.Error(err))
		}
		metaSpan.End(err)
	}()

	// All TMS integrations that might use poller for onboarding updated loads
	tmsIntegrations, err := integrationDB.GetAllValidTMS(ctx)
	if err != nil {
		return fmt.Errorf("error getting TMS integrations: %w", err)
	}

	numJobs := len(tmsIntegrations)
	workers := helpers.Min(numJobs, MaxWorkers)

	jobsChan := make(chan models.Integration, numJobs)
	jobCompletedChan := make(chan bool, numJobs)

	// Regardless of how many GlobalTranz integrations we have, only one poller job is needed
	globalTranzPollerCompletedChan := make(chan bool, 1)

	workerFunc := func(_ int) {
		sentry.WithHub(ctx, func(ctx context.Context) {

			defer func() {
				if r := recover(); r != nil {
					err = fmt.Errorf("%v", r)
					log.Error(ctx, "poller worker panicked", zap.Error(err))
				}
				jobCompletedChan <- true
			}()

			for job := range jobsChan {
				// Error logging and Sentry reporting is handled by Span.End in the handler functions because
				// stack trace is more detailed
				switch job.Name {
				case models.McleodEnterprise, models.Turvo, models.FreightFlow,
					models.ThreeG, models.GlobalTranzTMS, models.Aljex,
					models.Revenova, models.Stark, models.QuantumEdge:
					_ = pollByTime(ctx, job, false, models.NullTime{}) //nolint:errcheck // handled by Span.End
				case models.Ascend, models.Relay: // FYI this strategy also works for Aljex
					_ = pollByIncrementingExternalTMSID(ctx, job) //nolint:errcheck // handled by Span.End
				case models.Tai, models.Webhook, models.PLS:
					// No polling needed
				case models.TruckMate:
					// TODO: Add polling for TruckMate
				default:
					log.Error(ctx, "unsupported TMS", zap.String("tmsName", string(job.Name)))
				}
			}
		})
	}

	// Start worker goroutines
	for w := 1; w <= workers; w++ {
		go workerFunc(w)
	}

	// Run GlobalTranz poller in a separate goroutine
	go func() {
		defer func() {
			if r := recover(); r != nil {
				err := fmt.Errorf("%v", r)
				log.Error(ctx, "poller worker panicked", zap.Error(err))
			}
			globalTranzPollerCompletedChan <- true
		}()

		handleGlobalTranzPoller(ctx)
	}()

	// Run OpenDock warehouse poller in a separate goroutine (runs once a day)
	go func() {
		defer func() {
			if r := recover(); r != nil {
				err := fmt.Errorf("%v", r)
				log.Error(ctx, "Opendock poller worker panicked", zap.Error(err))
			}
		}()

		handleOpendockPoller(ctx)
	}()

	// Run Retalix warehouse poller in a separate goroutine (runs once a week)
	go func() {
		defer func() {
			if r := recover(); r != nil {
				err := fmt.Errorf("%v", r)
				log.Error(ctx, "Retalix poller worker panicked", zap.Error(err))
			}
		}()

		handleRetalixPoller(ctx)
	}()

	// Send jobs to the jobs channel
	for _, job := range tmsIntegrations {
		jobsChan <- job
	}
	close(jobsChan)

	// Wait for all jobs to be completed
	for k := 0; k < numJobs; k++ {
		<-jobCompletedChan
	}

	// Wait for GlobalTranz poller to complete
	<-globalTranzPollerCompletedChan

	log.Info(ctx, "completed all jobs")
	return nil
}

func handleGlobalTranzPoller(ctx context.Context) {
	sentry.WithHub(ctx, func(ctx context.Context) {

		// Check if any valid GlobalTranz integration for poller to cache QQ Lane History responses for
		globaltranzIntegration, err := integrationDB.GetFirstValidGlobalTranz(ctx)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {

				env := os.Getenv("APP_ENV")
				if env == "staging" || env == "dev" {
					log.WarnNoSentry(ctx, "no valid GlobalTranz integration found for poller")
					return
				}

				// TODO: Revert to log.Warn once GlobalTranz is re-enabled in prod
				log.WarnNoSentry(ctx, "no valid GlobalTranz integration found for poller")
				return
			}

			log.Error(ctx, "error checking for valid GlobalTranz integration", zap.Error(err))
			return
		}

		_ = pollGlobalTranzLaneHistoryCombinations(ctx, globaltranzIntegration) //nolint:errcheck
	})
}

func handleOpendockPoller(ctx context.Context) {
	sentry.WithHub(ctx, func(ctx context.Context) {

		if !shouldRunOpendockPoller(ctx) {
			log.Info(ctx, "Opendock warehouse poller not due to run yet, skipping")
			return
		}

		log.Info(ctx, "starting Opendock warehouse poller")

		if err := pollOpendockWarehouses(ctx); err != nil {
			log.Error(ctx, "Opendock warehouse poller failed", zap.Error(err))
			return
		}

		// Update the last run time
		if err := updateOpendockPollerLastRun(ctx); err != nil {
			log.Error(ctx, "failed to mark Opendock warehouse poller run time", zap.Error(err))
		}

		log.Info(ctx, "Opendock warehouse poller completed successfully")
	})
}

func handleRetalixPoller(ctx context.Context) {
	sentry.WithHub(ctx, func(ctx context.Context) {

		if !shouldRunRetalixPoller(ctx) {
			log.Info(ctx, "Retalix warehouse poller not due to run yet, skipping")
			return
		}

		log.Info(ctx, "starting Retalix warehouse poller")

		if err := pollRetalixWarehouses(ctx); err != nil {
			log.Error(ctx, "Retalix warehouse poller failed", zap.Error(err))
			return
		}

		// Update the last run time
		if err := updateRetalixPollerLastRun(ctx); err != nil {
			log.Error(ctx, "failed to mark Retalix warehouse poller run time", zap.Error(err))
		}

		log.Info(ctx, "Retalix warehouse poller completed successfully")
	})
}
