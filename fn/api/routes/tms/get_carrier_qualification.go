package tmsroutes

import (
	"errors"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/integrations/tms/mcleodenterprise"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
)

type (
	GetCarrierQualificationQuery struct {
		TMSID                uint   `json:"tmsID" validate:"required"`
		FreightTrackingID    string `json:"freightTrackingID" validate:"required"`
		CarrierExternalTMSID string `json:"carrierExternalTMSID" validate:"required"`
	}

	GetCarrierQualificationResponse struct {
		CarrierExternalTMSID string `json:"carrierExternalTMSID"`
		IsQualified          bool   `json:"isQualified"`
	}
)

// Checks if a carrier is qualified to be assigned to a load.
// Different from /carrierVerification endpoints which verify a carrier's credentials via systems like Highway.
// NOTE: Implemented just for Mcleod Enterprise right now, but can be added to TMSInterface in the future
func GetCarrierQualification(c *fiber.Ctx) error {
	var query GetCarrierQualificationQuery
	if err := api.Parse(c, nil, &query, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Any("requestQuery", query))
	userServiceID := middleware.ServiceIDFromContext(c)
	var err error

	tmsIntegration, err := integrationDB.Get(ctx, query.TMSID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Warn(
				ctx,
				"trying to get carrier qualification for a service with no active TMS",
				zap.Uint("serviceID", userServiceID),
			)

			return c.SendStatus(http.StatusNotFound)
		}

		log.Error(ctx, "error fetching integrations from DB", zap.Error(err))
		return err
	}

	if tmsIntegration.ServiceID != userServiceID {
		log.Infof(
			ctx,
			"user serviceID %d does not match TMS service ID %d",
			userServiceID,
			tmsIntegration.ServiceID,
		)

		return c.SendStatus(http.StatusUnauthorized)
	}

	if tmsIntegration.Name != models.McleodEnterprise {
		log.Warn(ctx, "trying to get carrier qualification for unsupported TMS",
			zap.Uint("serviceID", userServiceID), zap.String("tmsName", string(tmsIntegration.Name)))

		return c.SendStatus(http.StatusServiceUnavailable)
	}

	mcleodClient, err := mcleodenterprise.New(tmsIntegration)
	if err != nil {
		log.Error(ctx, "error creating McleodEnterprise client", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	var curLoad models.Load
	// Small optimization: Get the load from the DB; otherwise fallback to fetching from TMS
	curLoad, err = loadDB.GetLoadByFreightIDAndTMSID(ctx, tmsIntegration.ID, query.FreightTrackingID)
	if err != nil {
		log.WarnNoSentry(ctx, "error getting load from DB", zap.Error(err))

		curLoad, _, err = mcleodClient.GetLoad(ctx, query.FreightTrackingID)
		if err != nil {
			log.Error(ctx, "error getting load from TMS", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}
	}

	isQualified, err := mcleodClient.GetCarrierQualification(ctx, &curLoad, query.CarrierExternalTMSID)
	if err != nil {
		log.Error(ctx, "error getting carrier qualification from TMS", zap.Error(err))
		return c.SendStatus(http.StatusServiceUnavailable)
	}

	return c.Status(http.StatusOK).JSON(GetCarrierQualificationResponse{
		IsQualified:          isQualified,
		CarrierExternalTMSID: query.CarrierExternalTMSID,
	})
}
