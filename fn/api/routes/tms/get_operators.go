package tmsroutes

import (
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/integrations/tms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	tmsUserDB "github.com/drumkitai/drumkit/common/rds/tmsuser"
)

type (
	// If multi-TMS service, specify TMS ID
	GetOperatorsQuery = rds.GenericGetQuery

	// To maintain backwards-compatibility and because it satisfies our current needs, we return just an
	// slice of usernames instead of structs..
	// TMS function is responsible for matching username with ExternalTMSID when needed via DB lookup.
	GetOperatorsResponse struct {
		Operators []string `json:"operators"`
	}
)

func GetOperators(c *fiber.Ctx) error {
	var query GetOperatorsQuery

	if err := api.Parse(c, nil, &query, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	// TODO: Support search in FE; for now we return all operators as it's unlikely to be many objects
	// if query.Limit <= 0 {
	// 	query.Limit = 3000
	// }

	ctx := log.With(c.UserContext(), zap.Any("requestQuery", query))

	userServiceID := middleware.ServiceIDFromContext(c)
	var err error
	var operators []models.TMSUser
	var tmsIntegration models.Integration

	tmsIntegration, err = integrationDB.Get(ctx, query.TMSID)
	if err != nil {
		log.Error(ctx, "could not get tms integration", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if tmsIntegration.ServiceID != userServiceID {
		log.Infof(
			ctx,
			"user serviceID %d does not match TMS service ID %d",
			userServiceID,
			tmsIntegration.ServiceID,
		)

		return c.SendStatus(http.StatusUnauthorized)
	}

	if !query.ForceRefresh {
		operators, err = tmsUserDB.GetUsersByTMSID(ctx, query)

		switch {
		case len(operators) == 0:
			log.Info(ctx, "no users found in DB, falling back to TMS")

		case err != nil:
			// Fail-open and try TMS; Gorm will send to Sentry
			log.WarnNoSentry(ctx, "error searching TMS users in DB, falling back to TMS", zap.Error(err))

		case len(operators) > 0:
			operatorsStr := []string{}
			for _, op := range operators {
				operatorsStr = append(operatorsStr, op.Username)
			}

			return c.Status(http.StatusOK).JSON(
				GetOperatorsResponse{
					Operators: operatorsStr,
				},
			)
		}
	}

	client, err := tms.New(ctx, tmsIntegration)
	if err != nil {
		return c.SendStatus(http.StatusInternalServerError)
	}

	operators, err = client.GetUsers(ctx)
	if err != nil {
		log.Error(ctx, "GetUsers from TMS failed", zap.Error(err))
		return c.SendStatus(http.StatusServiceUnavailable)
	}

	if err = tmsUserDB.RefreshUsers(ctx, operators); err != nil {
		log.WarnNoSentry(ctx, "error refreshing TMS users in db", zap.Error(err))
	}

	operatorsStr := []string{}
	for _, op := range operators {
		operatorsStr = append(operatorsStr, op.Username)
	}

	endIndex := helpers.Min(len(operators), query.Limit)

	return c.Status(http.StatusOK).JSON(
		GetOperatorsResponse{
			Operators: operatorsStr[0:endIndex],
		},
	)
}
