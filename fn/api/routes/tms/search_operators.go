package tmsroutes

import (
	"errors"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	tmsOperatorDB "github.com/drumkitai/drumkit/common/rds/tmsuser"
)

type (
	SearchOperatorsQuery = rds.GenericSearchQuery

	SearchOperatorsResponse = GetOperatorsResponse
)

// Note that unlike GetOperators, this function does not support force refresh from the TMS.
func SearchOperators(c *fiber.Ctx) error {
	var query SearchOperatorsQuery
	if err := api.Parse(c, nil, &query, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Any("requestQuery", query))
	userServiceID := middleware.ServiceIDFromContext(c)
	var operatorList []models.TMSUser
	var err error

	tmsIntegration, err := integrationDB.Get(ctx, query.TMSID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Error(ctx, "integration not configured properly",
				zap.Error(err), zap.Uint("service_id", userServiceID))
			return c.SendStatus(http.StatusNotFound)
		}
		log.Error(ctx, "error fetching integration from DB",
			zap.Error(err), zap.Uint("service_id", userServiceID))
		return err
	}
	if tmsIntegration.ServiceID != userServiceID {
		log.Infof(
			ctx,
			"user serviceID %d does not match TMS service ID %d",
			userServiceID,
			tmsIntegration.ServiceID,
		)

		return c.SendStatus(http.StatusUnauthorized)
	}

	query.TMSID = tmsIntegration.ID

	operatorList, err = tmsOperatorDB.SearchByTMSID(ctx, query)
	if err != nil {
		log.Error(ctx, "error searching TMS operators in DB", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	operatorsStr := []string{}
	for _, op := range operatorList {
		operatorsStr = append(operatorsStr, op.Username)
	}

	return c.Status(http.StatusOK).JSON(
		GetOperatorsResponse{
			Operators: operatorsStr,
		},
	)
}
