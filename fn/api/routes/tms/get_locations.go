package tmsroutes

import (
	"context"
	"errors"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/integrations/tms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	tmsLocationDB "github.com/drumkitai/drumkit/common/rds/tms_location"
	"github.com/drumkitai/drumkit/common/sentry"
)

type (
	// NOTE: If limit is not set, it defaults to 100
	GetLocationsQuery = rds.GenericGetQuery

	GetLocationsResponse struct {
		LocationList []models.TMSLocation `json:"locationList"`
		TMSTenant    string               `json:"tmsTenant"`
		Message      string               `json:"message"`
	}
)

// GetLocations retrieves TMS location data.
// It first attempts to fetch locations from the database unless a refresh is forced.
// If no data exists or a refresh is requested, it fetches from the TMS API directly.
// The function supports both synchronous and asynchronous operation modes.

// Returns a list of locations to initially populate the location dropdown.
func GetLocations(c *fiber.Ctx) error {
	var query GetLocationsQuery
	if err := api.Parse(c, nil, &query, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	if query.Limit <= 0 {
		query.Limit = 100
	}

	ctx := log.With(c.UserContext(), zap.Any("requestQuery", query))
	userServiceID := middleware.ServiceIDFromContext(c)
	var locationList []models.TMSLocation
	var err error

	tmsIntegration, err := integrationDB.Get(ctx, query.TMSID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Warn(ctx, "trying to fetch customers for a service with no active TMS",
				zap.Uint("serviceID", userServiceID))

			return c.SendStatus(http.StatusNotFound)
		}

		log.Error(ctx, "error fetching integrations from DB", zap.Error(err))
		return err
	}

	if tmsIntegration.ServiceID != userServiceID {
		log.Infof(
			ctx,
			"user serviceID %d does not match TMS service ID %d",
			userServiceID,
			tmsIntegration.ServiceID,
		)

		return c.SendStatus(http.StatusUnauthorized)
	}

	// If we're not forcing a refresh, then get from DB.
	// If async request, get query.Limit records from DB to return to FE as a placeholder
	// while we asynchronously add the rest to DB
	if !query.ForceRefresh || query.Async {
		locationList, err = tmsLocationDB.GetTMSLocationsByTMSID(ctx, query)

		switch {
		case len(locationList) == 0:
			log.Info(ctx, "no locations found in DB, falling back to TMS")

		case err != nil:
			// Fail-open and try TMS; Gorm will send to Sentry
			log.WarnNoSentry(ctx, "error searching TMS locations in DB, falling back to TMS", zap.Error(err))

		case len(locationList) > 0 && !query.Async:
			return c.Status(http.StatusOK).JSON(
				GetLocationsResponse{
					LocationList: locationList,
					TMSTenant:    tmsIntegration.Tenant,
				},
			)
		}
	}

	client, err := tms.New(ctx, tmsIntegration)
	if err != nil {
		log.Error(ctx, "error creating TMS client", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	// Set optional parameter to change the host name for TMS api call
	var changeHostName bool
	if query.ChangeHostName {
		changeHostName = true
	}

	if query.Async {
		go sentry.WithHub(ctx, func(ctx context.Context) {
			asyncCtx := log.InheritContext(ctx, context.Background())
			asyncCtx, cancel := context.WithTimeout(asyncCtx, 15*time.Minute)
			defer cancel()

			// Locations are upserted to db within the GetLocations function
			// to handle partial progress during large data fetches (10k-100k+ records).
			// This deviates from our usual single-responsibility pattern but is necessary for data reliability

			locationList, err = client.GetLocations(asyncCtx, models.WithChangeHostName(changeHostName))
			// Note: an empty slice of TMSLocations is not an error, we handle it further down.
			if err != nil && err.Error() != tmsLocationDB.EmptyTMSLocationsSlice {
				log.Error(asyncCtx, "async getLocations from TMS failed", zap.Error(err))
				return
			}

			err = integrationDB.SetColumn(
				asyncCtx,
				tmsIntegration.ID,
				integrationDB.LastLocationUpdatedAt,
				time.Now(),
			)
			if err != nil {
				log.Warn(
					asyncCtx,
					"failed to set last location updated at",
					zap.Error(err),
					zap.Uint("tmsID", tmsIntegration.ID),
				)
			}

			if len(locationList) == 0 {
				log.Info(asyncCtx, "no locations found in TMS")
				return
			}

			log.Info(asyncCtx, "successfully got locations from TMS", zap.Int("count", len(locationList)))
		})

		// If async request, return query.Limit records from DB to return to FE as a placeholder
		// while we asynchronously add the rest to DB
		endIndex := helpers.Min(len(locationList), query.Limit)

		return c.Status(http.StatusAccepted).JSON(
			GetLocationsResponse{
				LocationList: locationList[0:endIndex],
				TMSTenant:    tmsIntegration.Tenant,
				Message:      "Asynchronously fetching more locations from TMS",
			},
		)
	}

	// Otherwise, fetch synchronously from TMS

	// Locations are upserted to db within the GetLocations function
	// to handle partial progress during large data fetches (10k-100k+ records).
	// This deviates from our usual single-responsibility pattern but is necessary for data reliability
	locationList, err = client.GetLocations(ctx, models.WithChangeHostName(changeHostName))
	// Note: an empty slice of TMSLocations is not an error, we handle it further down.
	if err != nil && err.Error() != tmsLocationDB.EmptyTMSLocationsSlice {
		log.Error(ctx, "getLocations from TMS failed", zap.Error(err))
		return c.SendStatus(http.StatusServiceUnavailable)
	}

	err = integrationDB.SetColumn(ctx, tmsIntegration.ID, integrationDB.LastLocationUpdatedAt, time.Now())
	if err != nil {
		log.Warn(
			ctx,
			"failed to set last location updated at",
			zap.Error(err),
			zap.Uint("tmsID", tmsIntegration.ID),
		)
	}

	if len(locationList) == 0 {
		log.Info(ctx, "no locations found in TMS")
		return c.Status(http.StatusNotFound).JSON(
			GetLocationsResponse{
				Message: "No new or updated locations found.",
			},
		)
	}

	endIndex := helpers.Min(len(locationList), query.Limit)

	return c.Status(http.StatusOK).JSON(
		GetLocationsResponse{
			LocationList: locationList[0:endIndex],
			TMSTenant:    tmsIntegration.Tenant,
		},
	)
}
