package tmsroutes

import (
	"net/http"
	"strconv"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	tmsLocationDB "github.com/drumkitai/drumkit/common/rds/tms_location"
)

// DeleteLocation soft deletes a location from the DB, NOT from TMS
func DeleteLocation(c *fiber.Ctx) error {
	locationID, err := strconv.ParseUint(c.Params("ID"), 10, 64)
	if err != nil {
		return c.Status(http.StatusBadRequest).SendString("invalid location ID")
	}

	ctx := c.UserContext()
	userServiceID := middleware.ServiceIDFromContext(c)

	loc, err := tmsLocationDB.GetTMSLocationByID(ctx, uint(locationID))
	if err != nil {
		return c.Status(http.StatusNotFound).SendString("location not found")
	}

	// Fetch TMS integrations for this service
	tmsIntegrations, err := integrationDB.GetTMSListByServiceID(ctx, userServiceID)
	if err != nil {
		return c.Status(http.StatusInternalServerError).SendString("failed to check integrations")
	}
	allowed := false
	for _, tms := range tmsIntegrations {
		if tms.ID == loc.TMSIntegrationID {
			allowed = true
			break
		}
	}
	if !allowed {
		return c.Status(http.StatusForbidden).SendString("not authorized to delete this location")
	}

	if err := tmsLocationDB.DeleteLocation(ctx, uint(locationID)); err != nil {
		log.Error(ctx, "failed to soft delete TMSLocation", zap.Error(err))
		return c.Status(http.StatusInternalServerError).SendString("failed to delete location")
	}

	log.Info(
		ctx,
		"TMSLocation soft deleted",
		zap.Uint("locationID", uint(locationID)),
		zap.Uint("integrationID", loc.TMSIntegrationID),
	)

	return c.Status(http.StatusOK).SendString("Location deleted")
}
