package integrations

import (
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers/perms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/rds/integration"
)

type AdminIntegrationResponse struct {
	ID       uint   `json:"id"`
	Name     string `json:"name"`
	Type     string `json:"type"`
	Username string `json:"username"`
	Service  string `json:"service"`
	Note     string `json:"note"`
}

// AdminGetIntegrations returns all integrations with passwords for admin management
func AdminGetIntegrations(c *fiber.Ctx) error {
	ctx := c.UserContext()
	email := middleware.ClaimsFromContext(c).Email

	// Check if user is admin
	if !perms.IsAdmin(email) {
		log.WarnNoSentry(
			ctx,
			"unauthorized: non-admin user trying to access admin integrations",
			zap.String("email", email),
		)
		return c.SendStatus(http.StatusUnauthorized)
	}

	// Get all integrations with passwords
	integrations, err := integration.GetAllWithPasswords(ctx)
	if err != nil {
		log.Error(ctx, "failed to get integrations with passwords", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	// Convert to response format
	response := make([]AdminIntegrationResponse, 0, len(integrations))
	for _, integration := range integrations {
		response = append(response, AdminIntegrationResponse{
			ID:       integration.ID,
			Name:     string(integration.Name),
			Type:     string(integration.Type),
			Username: integration.Username,
			Service:  integration.Service.Name,
			Note:     integration.Note,
		})
	}

	return c.JSON(response)
}
