package integrations

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"strconv"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/integrations/scheduling"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	usergroupDB "github.com/drumkitai/drumkit/common/rds/usergroups"
	warehouseDB "github.com/drumkitai/drumkit/common/rds/warehouses"
)

type OnboardSchedulerQuery struct {
	OnboardWarehouses        bool `query:"onboardWarehouses"`
	AddSchedulingIntegration bool `query:"add_scheduling_integration"`
}

func OnboardScheduler(c *fiber.Ctx) error {
	var query OnboardSchedulerQuery
	var body models.OnboardSchedulerRequest
	if err := api.Parse(c, nil, &query, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext())
	userServiceID := middleware.ServiceIDFromContext(c)

	// If add_scheduling_integration is true, use the simple add integration flow
	if query.AddSchedulingIntegration {
		return handleAddIntegrationFlow(ctx, c, userServiceID, body)
	}

	// Original onboard flow
	encryptedPassword, err := crypto.EncryptAESGCM(ctx, body.Password, nil)
	if err != nil {
		log.Error(ctx, "Password encryption failed.", zap.Error(err))
		return c.SendStatus(http.StatusUnprocessableEntity)
	}

	newIntegration := models.Integration{
		Name:              models.IntegrationName(body.Name),
		Type:              models.Scheduling,
		APIKey:            body.APIKey,
		EncryptedPassword: []byte(encryptedPassword),
		AppID:             body.AppID,
		ServiceID:         userServiceID,
		Username:          body.Username,
		Tenant:            body.Tenant,
		Note:              body.Note,
	}

	client, err := scheduling.New(ctx, newIntegration)
	if err != nil {
		log.Error(ctx, "error creating new integration", zap.Error(err))
		return c.SendStatus(http.StatusUnprocessableEntity)
	}

	onBoardResp, err := client.OnboardScheduler(ctx)
	if err != nil && !errors.As(err, &errtypes.NotImplementedError{}) {
		log.Error(ctx, "error onboard integration", zap.Error(err))
		return c.SendStatus(http.StatusUnprocessableEntity)
	}

	encryptedPassword, err = crypto.EncryptAESGCM(ctx, body.Password, nil)
	if err != nil {
		log.Error(ctx, "Password encryption failed during onboard", zap.Error(err))
		return c.SendStatus(http.StatusUnprocessableEntity)
	}

	newIntegration.Username = body.Username
	newIntegration.EncryptedPassword = []byte(encryptedPassword)
	newIntegration.AccessToken = onBoardResp.AccessToken
	newIntegration.AccessTokenExpirationDate = models.NullTime{
		Time:  onBoardResp.AccessTokenExpirationDate,
		Valid: true,
	}
	newIntegration.ServiceID = userServiceID
	newIntegration.AppID = body.AppID
	newIntegration.Tenant = body.Tenant
	newIntegration.Note = body.Note

	if err = integrationDB.Create(ctx, &newIntegration); err != nil {
		return c.SendStatus(http.StatusInternalServerError)
	}

	if query.OnboardWarehouses {
		var allWarehouses []models.Warehouse

		allWarehouses, err := client.GetAllWarehouses(ctx)
		if err != nil {
			return fmt.Errorf("unable to fetch all warehouses from scheduler: %w", err)
		}

		if len(allWarehouses) > 0 {
			err = warehouseDB.OnboardWarehouses(ctx, allWarehouses)
			if err != nil {
				return fmt.Errorf("unable to onboard warehouses: %w", err)
			}
		}
	}

	return c.SendStatus(http.StatusCreated)
}

// handleAddIntegrationFlow handles the simple add integration flow (like the add_integration.go API)

func handleAddIntegrationFlow(
	ctx context.Context,
	c *fiber.Ctx,
	userServiceID uint,
	body models.OnboardSchedulerRequest,
) error {
	ctx, span := otel.StartSpan(ctx, "handleAddIntegrationFlow", nil)
	defer span.End(nil)

	// Encrypt password
	encryptedPassword, err := crypto.EncryptAESGCM(ctx, body.Password, nil)
	if err != nil {
		log.Error(ctx, "Password encryption failed", zap.Error(err))
		return c.SendStatus(http.StatusUnprocessableEntity)
	}

	if !body.IsServiceWide {
		body.IsServiceWide = false
	}

	// Create new integration
	newIntegration := models.Integration{
		Name:              models.IntegrationName(body.Name),
		Type:              models.Scheduling,
		Username:          body.Username,
		EncryptedPassword: []byte(encryptedPassword),
		ServiceID:         userServiceID,
		IsServiceWide:     body.IsServiceWide,
		Disabled:          false,
		Note:              body.Note,
		Tenant:            body.Tenant,
	}

	// Create integration in database
	if err = integrationDB.Create(ctx, &newIntegration); err != nil {
		log.Error(ctx, "Failed to create integration", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	// If user_group is provided and not service wide, associate with user group
	if !body.IsServiceWide && body.UserGroup != "" {
		userGroupID, err := strconv.ParseUint(body.UserGroup, 10, 32)
		if err != nil {
			log.Error(ctx, "Invalid user group ID", zap.Error(err))
			return c.Status(http.StatusBadRequest).SendString("Invalid user group ID")
		}

		// Verify user group exists and belongs to the same service
		userGroup, err := usergroupDB.GetByID(ctx, uint(userGroupID))
		if err != nil {
			log.Error(ctx, "User group not found", zap.Error(err))
			return c.Status(http.StatusNotFound).SendString("User group not found")
		}

		if userGroup.ServiceID != userServiceID {
			log.Error(
				ctx,
				"User group does not belong to user's service",
				zap.Uint("userGroupServiceID", userGroup.ServiceID),
				zap.Uint("userServiceID", userServiceID),
			)
			return c.Status(http.StatusForbidden).SendString("User group does not belong to your service")
		}

		// Associate integration with user group
		err = integrationDB.AssociateWithUserGroup(ctx, newIntegration.ID, uint(userGroupID))
		if err != nil {
			log.Error(ctx, "Failed to associate integration with user group", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}

		log.Info(
			ctx,
			"Integration associated with user group",
			zap.Uint("integrationID", newIntegration.ID),
			zap.Uint("userGroupID", uint(userGroupID)),
		)
	}

	log.Info(ctx, "Integration created successfully",
		zap.Uint("integrationID", newIntegration.ID),
		zap.String("name", body.Name),
		zap.Bool("isServiceWide", body.IsServiceWide))

	return c.Status(http.StatusCreated).JSON(map[string]any{
		"id":        newIntegration.ID,
		"message":   "Integration created successfully",
		"createdAt": newIntegration.CreatedAt.Format("2006-01-02T15:04:05Z"),
	})
}
