package integrations

import (
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/integrations/tms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
)

func OnboardTMS(c *fiber.Ctx) error {
	var body models.OnboardTMSRequest
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext())
	userServiceID := middleware.ServiceIDFromContext(c)

	encryptedPassword, err := crypto.EncryptAESGCM(ctx, body.Password, nil)
	if err != nil {
		log.Error(ctx, "Password encryption failed.", zap.Error(err))
		return c.SendStatus(http.StatusUnprocessableEntity)
	}

	service, err := rds.GetServiceByID(ctx, userServiceID)
	if err != nil {
		log.Error(ctx, "Fetching user service failed", zap.Error(err))
		return c.SendStatus(http.StatusUnprocessableEntity)
	}

	newTMS := models.Integration{
		Name:              models.IntegrationName(body.Name),
		Type:              models.TMS,
		APIKey:            body.APIKey,
		EncryptedPassword: []byte(encryptedPassword),
		AppID:             body.AppID,
		Username:          body.Username,
		Tenant:            body.Tenant,
		TwoFactorSecret:   body.TwoFactorSecret,
		ServiceID:         service.ID,
	}

	client, err := tms.New(ctx, newTMS)
	if err != nil {
		log.Error(ctx, "Creating TMS client failed", zap.Error(err))
		return c.SendStatus(http.StatusUnprocessableEntity)
	}

	initialOnboardResponse, err := client.InitialOnboard(ctx, service, body)
	if err != nil {
		log.Error(ctx, "OnboardTMS failed", zap.Error(err))
		return c.SendStatus(http.StatusUnprocessableEntity)
	}

	newTMS.Username = initialOnboardResponse.Username
	newTMS.EncryptedPassword = []byte(initialOnboardResponse.EncryptedPassword)
	newTMS.AccessToken = initialOnboardResponse.AccessToken
	newTMS.RefreshToken = initialOnboardResponse.RefreshToken
	newTMS.ServiceID = userServiceID
	newTMS.AppID = initialOnboardResponse.AppID
	newTMS.Tenant = initialOnboardResponse.Tenant
	newTMS.TwoFactorSecret = initialOnboardResponse.TwoFactorSecret

	if err = integrationDB.Create(ctx, &newTMS); err != nil {
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.SendStatus(http.StatusCreated)
}
