package integrations

import (
	"errors"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers/perms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/rds/integration"
)

type UpdatePasswordRequest struct {
	IntegrationID   uint   `json:"integrationId" validate:"required"`
	NewPassword     string `json:"newPassword" validate:"required"`
	ConfirmPassword string `json:"confirmPassword" validate:"required"`
}

type UpdatePasswordResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// AdminUpdateIntegrationPassword updates the password for an integration
func AdminUpdateIntegrationPassword(c *fiber.Ctx) error {
	ctx := c.UserContext()
	email := middleware.ClaimsFromContext(c).Email

	// Check if user is admin
	if !perms.IsAdmin(email) {
		log.WarnNoSentry(
			ctx,
			"unauthorized: non-admin user trying to update integration password",
			zap.String("email", email),
		)
		return c.SendStatus(http.StatusUnauthorized)
	}

	var req UpdatePasswordRequest
	if err := c.BodyParser(&req); err != nil {
		log.Error(ctx, "failed to parse request body", zap.Error(err))
		return c.Status(http.StatusBadRequest).JSON(UpdatePasswordResponse{
			Success: false,
			Message: "Invalid request format",
		})
	}

	// Validate passwords match
	if req.NewPassword != req.ConfirmPassword {
		return c.Status(http.StatusBadRequest).JSON(UpdatePasswordResponse{
			Success: false,
			Message: "Passwords do not match",
		})
	}

	// Validate password is not empty
	if req.NewPassword == "" {
		return c.Status(http.StatusBadRequest).JSON(UpdatePasswordResponse{
			Success: false,
			Message: "Password cannot be empty",
		})
	}

	// Update the integration password
	err := integration.UpdatePassword(ctx, req.IntegrationID, req.NewPassword)
	if err != nil {
		if errors.Is(err, integration.ErrIntegrationNotFound) {
			return c.Status(http.StatusNotFound).JSON(UpdatePasswordResponse{
				Success: false,
				Message: "Integration not found",
			})
		}
		log.Error(
			ctx,
			"failed to update integration password",
			zap.Error(err),
			zap.Uint("integrationID", req.IntegrationID),
		)
		return c.Status(http.StatusInternalServerError).JSON(UpdatePasswordResponse{
			Success: false,
			Message: "Failed to update password",
		})
	}

	log.Info(
		ctx,
		"integration password updated successfully",
		zap.Uint("integrationID", req.IntegrationID),
		zap.String("adminEmail", email),
	)

	return c.JSON(UpdatePasswordResponse{
		Success: true,
		Message: "Password updated successfully",
	})
}
