package integrations

import (
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
)

type SuccessResponse struct {
	ID        uint      `json:"id"`
	CreatedAt time.Time `json:"createdAt"`
}

func LoadWebhookHandler(c *fiber.Ctx) error {
	var body models.WebhookLoadBody
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext())
	userServiceID := middleware.ServiceIDFromContext(c)

	tmsVar, err := integrationDB.GetByName(ctx, userServiceID, models.Webhook)
	if err != nil {
		log.Error(
			ctx,
			"integration not configured properly",
			zap.Error(err),
			zap.Uint("service_id", userServiceID),
		)

		return c.SendStatus(http.StatusInternalServerError)
	}

	newLoad := models.Load{
		FreightTrackingID: body.FreightLoadID,
		ExternalTMSID:     body.ExternalTMSLoadID,
		ServiceID:         userServiceID,
		LoadCoreInfo:      body.LoadCoreInfo,
	}

	err = loadDB.UpsertLoad(ctx, &newLoad, &tmsVar.Service)
	if err != nil {
		log.Error(
			ctx,
			"error upserting load",
			zap.Error(err),
			zap.Uint("service_id", userServiceID),
		)

		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.Status(http.StatusOK).JSON(SuccessResponse{
		ID:        newLoad.ID,
		CreatedAt: newLoad.CreatedAt,
	})
}
