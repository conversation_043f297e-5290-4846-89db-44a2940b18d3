package metrics

import (
	"encoding/csv"
	"fmt"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	quoteRequestDB "github.com/drumkitai/drumkit/common/rds/quote_request"
)

type (
	ExportQuoteRequestsCSVPath struct {
		ServiceID uint `path:"serviceID" validate:"required"`
	}
)

func ExportQuoteRequestsToCSV(c *fiber.Ctx) error {
	var path ExportQuoteRequestsCSVPath
	if err := api.Parse(c, &path, nil, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()
	userServiceID := middleware.ServiceIDFromContext(c)

	if userServiceID != path.ServiceID {
		log.WarnNoSentry(ctx, "unauthorized: serviceID from token does not match path")
		return c.SendStatus(http.StatusUnauthorized)
	}

	c.Set("Content-Type", "text/csv")
	c.Set("Content-Disposition", `attachment; filename="quote_requests.csv"`)

	writer := csv.NewWriter(c.Response().BodyWriter())

	headers := []string{
		"Quote ID", "Created Date", "Last Updated", "Email ID", "Transport Type", "Distance", "Pickup Location",
		"Delivery Location", "Status", "Email Details", "Cost Details", "Pickup Date", "Pickup State",
		"Delivery Date", "Delivery State", "Email Recipients", "Email Subject", "Email Sent At",
		"Currency Type", "Final Quote Price", "Final Margin %", "Target Buy Rate",
	}
	if err := writer.Write(headers); err != nil {
		return err
	}

	rows, err := quoteRequestDB.GetValidRequestsWithQuotes(ctx, path.ServiceID, quoteRequestDB.WithNoDateLimit())
	if err != nil {
		return err
	}

	for _, row := range rows {
		transportType := ""
		distance := ""
		if row.AppliedRequest.TransportType != "" {
			transportType = string(row.AppliedRequest.TransportType)
			distance = fmt.Sprint(row.AppliedRequest.Distance)
		} else if row.SuggestedRequest.TransportType != "" {
			transportType = string(row.SuggestedRequest.TransportType)
			distance = fmt.Sprint(row.SuggestedRequest.Distance)
		}

		emailDetails := fmt.Sprintf("From: %s\nTo: %s\nSubject: %s",
			row.Sender, row.Recipients, row.Subject)

		costDetails := fmt.Sprintf("Total: $%s\nQuote: $%s\nMargin: %s",
			formatMoneyOrEmpty(row.TotalCost),
			formatMoneyOrEmpty(row.FinalQuotePrice),
			formatMoneyOrEmpty(row.FinalMargin))

		pickupDate := ""
		if row.AppliedRequest.PickupDate.Valid {
			pickupDate = row.AppliedRequest.PickupDate.Time.Format(time.RFC3339)
		} else if row.SuggestedRequest.PickupDate.Valid {
			pickupDate = row.SuggestedRequest.PickupDate.Time.Format(time.RFC3339)
		}

		deliveryDate := ""
		if row.AppliedRequest.DeliveryDate.Valid {
			deliveryDate = row.AppliedRequest.DeliveryDate.Time.Format(time.RFC3339)
		} else if row.SuggestedRequest.DeliveryDate.Valid {
			deliveryDate = row.SuggestedRequest.DeliveryDate.Time.Format(time.RFC3339)
		}

		pickupState := ""
		if row.AppliedRequest.PickupLocation.State != "" {
			pickupState = row.AppliedRequest.PickupLocation.State
		} else if row.SuggestedRequest.PickupLocation.State != "" {
			pickupState = row.SuggestedRequest.PickupLocation.State
		}

		deliveryState := ""
		if row.AppliedRequest.DeliveryLocation.State != "" {
			deliveryState = row.AppliedRequest.DeliveryLocation.State
		} else if row.SuggestedRequest.DeliveryLocation.State != "" {
			deliveryState = row.SuggestedRequest.DeliveryLocation.State
		}

		csvRow := []string{
			fmt.Sprint(row.ID),
			row.CreatedAt.Format(time.RFC3339),
			row.UpdatedAt.Format(time.RFC3339),
			fmt.Sprint(row.EmailID),
			transportType,
			distance,
			getLocation(&row.AppliedRequest, &row.SuggestedRequest, true),
			getLocation(&row.AppliedRequest, &row.SuggestedRequest, false),
			row.QuoteRequestStatus,
			emailDetails,
			costDetails,
			pickupDate,
			pickupState,
			deliveryDate,
			deliveryState,
			row.Recipients,
			row.Subject,
			row.SentAt.Format(time.RFC3339),
			row.Currency,
			formatMoneyOrEmpty(row.FinalQuotePrice),
			formatMoneyOrEmpty(row.FinalMargin),
			formatMoneyOrEmpty(row.TargetBuyRate),
		}

		if err := writer.Write(csvRow); err != nil {
			return err
		}
	}

	writer.Flush()
	return writer.Error()
}

func formatMoneyOrEmpty(n any) string {
	if n == nil {
		return ""
	}

	switch v := n.(type) {
	case float64:
		if v == 0 {
			return ""
		}
		return fmt.Sprintf("%.2f", v)

	case int:
		if v == 0 {
			return ""
		}
		return fmt.Sprintf("%.2f", float64(v))

	default:
		return ""
	}
}

func getLocation(applied, suggested *models.QuoteLoadInfo, isPickup bool) string {
	if applied == nil && suggested == nil {
		return ""
	}

	var address models.Address
	if isPickup {
		if applied != nil {
			address = applied.PickupLocation
		} else {
			address = suggested.PickupLocation
		}
	} else {
		if applied != nil {
			address = applied.DeliveryLocation
		} else {
			address = suggested.DeliveryLocation
		}
	}

	return formatAddress(address.City, address.State)
}

func formatAddress(city, state string) string {
	if city == "" || state == "" {
		return ""
	}

	return fmt.Sprintf("%s, %s", city, state)
}
