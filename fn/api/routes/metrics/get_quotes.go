package metrics

import (
	"errors"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	quote "github.com/drumkitai/drumkit/common/rds/quick_quote"
)

type (
	GetQuotesPath struct {
		ServiceID uint `path:"serviceID" validate:"required"`
	}

	GetQuotesResponse struct {
		Quotes []models.QuickQuote `json:"quotes" `
	}
)

func GetQuotes(c *fiber.Ctx) error {
	var path GetQuotesPath
	if err := api.Parse(c, &path, nil, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()
	userServiceID := middleware.ServiceIDFromContext(c)

	if userServiceID != path.ServiceID {
		log.WarnNoSentry(ctx, "unauthorized: serviceID from token does not match path")
		return c.SendStatus(http.StatusUnauthorized)
	}

	quotes, err := quote.GetAll(ctx, path.ServiceID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.SendStatus(http.StatusNotFound)
		}

		log.Warn(ctx, "error fetching valid quotes for metrics", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	response := GetQuotesResponse{
		Quotes: quotes,
	}

	return c.Status(http.StatusOK).JSON(response)
}
