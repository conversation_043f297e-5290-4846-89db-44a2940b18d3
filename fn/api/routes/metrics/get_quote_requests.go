package metrics

import (
	"errors"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	quoteRequestDB "github.com/drumkitai/drumkit/common/rds/quote_request"
)

type (
	GetQuoteRequestsPath struct {
		ServiceID uint `path:"serviceID" validate:"required"`
	}

	GetQuoteRequestsQuery struct {
		StartTime time.Time `query:"startTime"`
		EndTime   time.Time `query:"endTime"`
	}

	QuoteRequestResponse struct {
		ID                   uint                       `json:"id"`
		CreatedAt            time.Time                  `json:"createdAt"`
		UpdatedAt            time.Time                  `json:"updatedAt"`
		UserID               uint                       `json:"userId"`
		EmailID              uint                       `json:"emailId"`
		ServiceID            uint                       `json:"serviceId"`
		ThreadID             string                     `json:"threadId"`
		SelectedQuickQuoteID *uint                      `json:"selectedQuickQuoteId,omitempty"`
		Attachment           models.Attachment          `json:"attachment"`
		ThirdPartyQuoteURLs  models.ThirdPartyQuoteURLs `json:"thirdPartyQuoteUrls"`
		SuggestedRequest     models.QuoteLoadInfo       `json:"suggestedRequest"`
		AppliedRequest       models.QuoteLoadInfo       `json:"appliedRequest"`
		RFCMessageID         string                     `json:"rfcMessageId"`
		Subject              string                     `json:"subject"`
		SentAt               time.Time                  `json:"sentAt"`
		Sender               string                     `json:"sender"`
		Recipients           string                     `json:"recipients"`
		ClassificationMethod string                     `json:"classificationMethod"`
		UserEmail            string                     `json:"userEmail"`
		ServiceName          string                     `json:"serviceName"`
		Currency             string                     `json:"currency"`
		TotalCost            float64                    `json:"totalCost"`
		FinalQuotePrice      int                        `json:"finalQuotePrice"`
		FinalMargin          int                        `json:"finalMargin"`
		FinalCarrierCost     int                        `json:"finalCarrierCost"`
		TargetBuyRate        float64                    `json:"targetBuyRate"`
		MinMarkup            float64                    `json:"minMarkup"`
		MaxMarkup            float64                    `json:"maxMarkup"`
		QuoteID              uint                       `json:"quoteId"`
		QuoteRequestStatus   string                     `json:"quoteRequestStatus"`
		QuotePipeline        string                     `json:"quotePipeline"`
	}

	GetQuoteRequestsResponse struct {
		QuoteRequests []QuoteRequestResponse `json:"quoteRequests" `
	}
)

func GetQuoteRequests(c *fiber.Ctx) error {
	var path GetQuoteRequestsPath
	var query GetQuoteRequestsQuery
	if err := api.Parse(c, &path, &query, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()
	userServiceID := middleware.ServiceIDFromContext(c)

	if userServiceID != path.ServiceID {
		log.WarnNoSentry(ctx, "unauthorized: serviceID from token does not match path")
		return c.SendStatus(http.StatusUnauthorized)
	}

	log.Info(
		ctx,
		"processing quote requests query",
		zap.Time("startTime", query.StartTime),
		zap.Time("endTime", query.EndTime),
		zap.Uint("serviceID", path.ServiceID),
	)

	// Handle date range filtering for quote requests:
	// - If both startTime and endTime provided: filters within exact range
	// - If only startTime: filters from that time forward
	// - If only endTime: filters from beginning until that time
	// - If neither provided: uses default range (usually last 7 days)
	var opts []quoteRequestDB.Option

	hasStart := !query.StartTime.IsZero()
	hasEnd := !query.EndTime.IsZero()

	switch {
	case hasStart && hasEnd:
		// Validate that start time is before end time when both are provided
		if query.StartTime.After(query.EndTime) {
			return c.Status(http.StatusBadRequest).SendString("start time must be before end time")
		}

		// Apply date range filter when both times are provided
		opts = append(opts, quoteRequestDB.WithDateRange(query.StartTime, query.EndTime))
		log.Debug(
			ctx,
			"using date range filter",
			zap.Time("startTime", query.StartTime),
			zap.Time("endTime", query.EndTime),
		)

	case hasStart:
		// Apply start time filter only - will return quotes from this time forward
		opts = append(opts, quoteRequestDB.WithStartDate(query.StartTime))
		log.Debug(
			ctx,
			"using start date filter",
			zap.Time("startTime", query.StartTime),
		)

	case hasEnd:
		// Apply end time filter only - will return quotes from beginning until this time
		opts = append(opts, quoteRequestDB.WithEndDate(query.EndTime))
		log.Debug(
			ctx,
			"using end date filter",
			zap.Time("endTime", query.EndTime),
		)

	default:
		log.Debug(ctx, "no date filters provided, using default range")
	}

	// Fetch quote requests with any provided date filters
	quoteRequests, err := quoteRequestDB.GetValidRequestsWithQuotes(ctx, path.ServiceID, opts...)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.SendStatus(http.StatusNotFound)
		}

		log.Warn(ctx, "error fetching valid quote requests for metrics", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	// TODO: We can scan the wanted shape into `quoteRequests` from the SQL query but the data shape is a WIP and
	// subject to change
	responseQuotes := make([]QuoteRequestResponse, len(quoteRequests))
	for i, qr := range quoteRequests {
		responseQuotes[i] = QuoteRequestResponse{
			ID:                   qr.ID,
			CreatedAt:            qr.CreatedAt,
			UpdatedAt:            qr.UpdatedAt,
			UserID:               qr.UserID,
			EmailID:              qr.EmailID,
			ServiceID:            qr.ServiceID,
			ThreadID:             qr.ThreadID,
			SelectedQuickQuoteID: qr.SelectedQuickQuoteID,
			Attachment:           qr.Attachment,
			ThirdPartyQuoteURLs:  qr.ThirdPartyQuoteURLs,
			SuggestedRequest:     qr.SuggestedRequest,
			AppliedRequest:       qr.AppliedRequest,
			RFCMessageID:         qr.RFCMessageID,
			Subject:              qr.Subject,
			SentAt:               qr.SentAt,
			Sender:               qr.Sender,
			Recipients:           qr.Recipients,
			ClassificationMethod: qr.ClassificationMethod,
			UserEmail:            qr.UserEmail,
			ServiceName:          qr.ServiceName,
			Currency:             qr.Currency,
			TotalCost:            qr.TotalCost,
			FinalQuotePrice:      qr.FinalQuotePrice,
			FinalMargin:          qr.FinalMargin,
			FinalCarrierCost:     qr.FinalCarrierCost,
			TargetBuyRate:        qr.TargetBuyRate,
			MinMarkup:            qr.MinMarkup,
			MaxMarkup:            qr.MaxMarkup,
			QuoteID:              qr.QuoteID,
			QuoteRequestStatus:   qr.QuoteRequestStatus,
			QuotePipeline:        qr.QuotePipeline,
		}
	}

	response := GetQuoteRequestsResponse{
		QuoteRequests: responseQuotes,
	}

	return c.Status(http.StatusOK).JSON(response)
}
