package truck

import (
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	truckDB "github.com/drumkitai/drumkit/common/rds/truck"
)

type (
	AddTruckToTruckListQuery struct {
		ThreadID string `params:"threadID" validate:"required"`
		EmailID  string `params:"emailID" validate:"required"`
	}

	AddTruckToTruckListBody struct {
		PickupDate string `json:"pickupDate"`
	}

	AddTruckToTruckListResponse struct {
		CreatedTruck CoreTruckObject `json:"createdTruck"`
	}
)

func AddTruckToTruckList(c *fiber.Ctx) error {
	var query AddTruckToTruckListQuery
	var body AddTruckToTruckListBody
	err := api.Parse(c, nil, &query, &body)
	if err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()
	userServiceID := middleware.ServiceIDFromContext(c)

	service, err := rds.GetServiceByID(ctx, userServiceID)
	if err != nil {
		log.Error(ctx, "could not get service", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	trucklist, err := truckDB.GetTruckListByEmail(ctx, query.EmailID)
	if err != nil {
		log.WarnNoSentry(ctx, "error fetching truck list by email", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if trucklist.ServiceID != service.ID {
		log.Warn(ctx, "unauthorized: truck list from email does not belong to service")
		return c.SendStatus(http.StatusUnauthorized)
	}

	newTruck := getEmptyTruckForTruckList(*trucklist, body.PickupDate)
	err = truckDB.CreateTruck(ctx, &newTruck)
	if err != nil {
		log.Error(ctx, "error creating truck", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	trucklist.Trucks = append(trucklist.Trucks, newTruck)

	err = truckDB.UpdateTruckList(ctx, trucklist)
	if err != nil {
		log.Warn(ctx, "failed to update truck list while associating with created truck", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.Status(http.StatusCreated).JSON(AddTruckToTruckListResponse{
		// Since this was created as an empty truck, we can ignore empty non-default values.
		CreatedTruck: CoreTruckObject{
			ID:         newTruck.ID,
			Type:       newTruck.Type.Suggestion,
			PickupDate: newTruck.PickupDate.Suggestion,
		},
	})
}

func getEmptyTruckForTruckList(trucklist models.TruckList, pickupDate string) models.Truck {
	date, err := time.Parse("2006-01-02T15:04:05Z", pickupDate)
	if err != nil {
		return models.Truck{}
	}

	return models.Truck{
		UserID:    trucklist.UserID,
		ServiceID: trucklist.ServiceID,
		EmailID:   trucklist.EmailID,
		ThreadID:  trucklist.ThreadID,
		PickupDate: models.SuggestionAppliedPair[models.NullTime]{
			Suggestion: models.NullTime{
				Time:  date,
				Valid: true,
			},
		},
		Type: models.SuggestionAppliedPair[models.TruckType]{
			Suggestion: models.VanTruckType,
		},
	}
}
