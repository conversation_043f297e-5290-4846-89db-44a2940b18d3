package email

import (
	"errors"
	"testing"

	apitesting "github.com/drumkitai/drumkit/fn/api/testing"
)

func TestIngestOnPremEmail(t *testing.T) {
	validator := apitesting.NewValidationTest(t)

	t.Run("valid request body", func(t *testing.T) {
		body := IngestOnPremEmailRequestBody{
			EmailAddress: "<EMAIL>",
		}

		if err := validator.TestBody(body); err != nil {
			t.<PERSON>("Expected no error, got: %v", err)
		}
	})

	t.Run("missing required EmailAddress", func(t *testing.T) {
		body := IngestOnPremEmailRequestBody{}

		err := validator.TestBody(body)
		if err == nil {
			t.Error("Expected error for missing EmailAddress, got none")
			return
		}

		var validationErr *apitesting.ValidationError
		if !errors.As(err, &validationErr) {
			t.<PERSON>("Expected ValidationError, got %T: %v", err, err)
			return
		}

		// Verify we got exactly one body error
		if validationErr.NumBodyErrors() != 1 {
			t.<PERSON><PERSON><PERSON>("Expected exactly 1 body validation error, got %d body errors: %v",
				validationErr.NumBodyErrors(), validationErr)
			return
		}

		// Verify it's the specific body error we expect
		if !validationErr.Contains("emailAddress is a required field") {
			t.Errorf("Expected body error about EmailAddress field, got: %v", validationErr)
		}
	})
}
