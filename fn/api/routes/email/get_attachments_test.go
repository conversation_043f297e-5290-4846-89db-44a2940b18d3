package email

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/drumkitai/drumkit/common/models"
)

func TestDeduplicateInlineAttachments(t *testing.T) {
	tests := []struct {
		name        string
		attachments []models.Attachment
		expected    []models.Attachment
	}{
		{
			name:        "empty slice",
			attachments: []models.Attachment{},
			expected:    []models.Attachment{},
		},
		{
			name: "single attachment",
			attachments: []models.Attachment{
				{OriginalFileName: "test.jpg", IsInline: true},
			},
			expected: []models.Attachment{
				{OriginalFileName: "test.jpg", IsInline: true},
			},
		},
		{
			name: "no duplicates",
			attachments: []models.Attachment{
				{OriginalFileName: "test1.jpg", IsInline: true},
				{OriginalFileName: "test2.jpg", IsInline: true},
				{OriginalFileName: "test3.pdf", IsInline: false},
			},
			expected: []models.Attachment{
				{OriginalFileName: "test1.jpg", IsInline: true},
				{OriginalFileName: "test2.jpg", IsInline: true},
				{OriginalFileName: "test3.pdf", IsInline: false},
			},
		},
		{
			name: "duplicate inline attachments with same filename",
			attachments: []models.Attachment{
				{OriginalFileName: "logo.png", IsInline: true, ExternalID: "1"},
				{OriginalFileName: "document.pdf", IsInline: false, ExternalID: "2"},
				{OriginalFileName: "logo.png", IsInline: true, ExternalID: "3"},
				{OriginalFileName: "other.jpg", IsInline: true, ExternalID: "4"},
				{OriginalFileName: "logo.jpg", IsInline: true, ExternalID: "5"},
			},
			expected: []models.Attachment{
				{OriginalFileName: "logo.png", IsInline: true, ExternalID: "1"},
				{OriginalFileName: "document.pdf", IsInline: false, ExternalID: "2"},
				{OriginalFileName: "other.jpg", IsInline: true, ExternalID: "4"},
				{OriginalFileName: "logo.jpg", IsInline: true, ExternalID: "5"},
			},
		},
		{
			name: "duplicate non-inline attachments should be preserved",
			attachments: []models.Attachment{
				{OriginalFileName: "report.pdf", IsInline: false, ExternalID: "1"},
				{OriginalFileName: "report.pdf", IsInline: false, ExternalID: "2"},
				{OriginalFileName: "logo.png", IsInline: true, ExternalID: "3"},
			},
			expected: []models.Attachment{
				{OriginalFileName: "report.pdf", IsInline: false, ExternalID: "1"},
				{OriginalFileName: "report.pdf", IsInline: false, ExternalID: "2"},
				{OriginalFileName: "logo.png", IsInline: true, ExternalID: "3"},
			},
		},
		{
			name: "fallback to transformed filename when original is empty",
			attachments: []models.Attachment{
				{OriginalFileName: "", TransformedFileName: "transformed_logo.png", IsInline: true, ExternalID: "1"},
				{OriginalFileName: "", TransformedFileName: "transformed_logo.png", IsInline: true, ExternalID: "2"},
				{OriginalFileName: "document.pdf", IsInline: false, ExternalID: "3"},
			},
			expected: []models.Attachment{
				{OriginalFileName: "", TransformedFileName: "transformed_logo.png", IsInline: true, ExternalID: "1"},
				{OriginalFileName: "document.pdf", IsInline: false, ExternalID: "3"},
			},
		},
		{
			name: "mixed inline and non-inline with same filenames",
			attachments: []models.Attachment{
				{OriginalFileName: "shared.png", IsInline: true, ExternalID: "1"},
				{OriginalFileName: "shared.png", IsInline: false, ExternalID: "2"},
				{OriginalFileName: "shared.png", IsInline: true, ExternalID: "3"},
				{OriginalFileName: "shared.png", IsInline: false, ExternalID: "4"},
			},
			expected: []models.Attachment{
				{OriginalFileName: "shared.png", IsInline: true, ExternalID: "1"},
				{OriginalFileName: "shared.png", IsInline: false, ExternalID: "2"},
				{OriginalFileName: "shared.png", IsInline: false, ExternalID: "4"},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := deduplicateInlineAttachments(tt.attachments)
			assert.Equal(t, tt.expected, result)
		})
	}
}
