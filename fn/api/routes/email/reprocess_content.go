package email

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/emails"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/fn/api/env"
)

type (
	ReprocessContentPath struct {
		EmailID uint `params:"id" validate:"required"`
	}

	ReprocessContentBody struct {
		LabelsToReprocess []emails.EmailLabel `json:"labelsToReprocess"`
	}

	ReprocessContentProcessorBody struct {
		LabelsToReprocess []string `json:"labelsToReprocess"`
	}
)

const reprocessContentPath = "%s/reprocess-content/%d"

func ReprocessContent(c *fiber.Ctx) error {
	var path ReprocessContentPath
	var body ReprocessContentBody
	if err := api.Parse(c, &path, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Any("emailID", path.EmailID))

	var labelsToReprocessStr []string
	if len(body.LabelsToReprocess) == 0 {
		log.Info(
			ctx,
			"no specific labels provided; proceeding to reprocess all original email labels",
			zap.Uint("emailID", path.EmailID),
		)
	} else {
		for _, label := range body.LabelsToReprocess {
			labelsToReprocessStr = append(labelsToReprocessStr, string(label))
		}

		log.Info(
			ctx,
			"reprocessing specific labels",
			zap.Strings("labels", labelsToReprocessStr),
			zap.Uint("emailID", path.EmailID),
		)
	}

	if env.Vars.DrumkitProcessorURL == "" {
		log.Error(ctx, "drumkit processor URL is not set")
		return c.SendStatus(http.StatusInternalServerError)
	}

	reprocessContentURL := fmt.Sprintf(reprocessContentPath, env.Vars.DrumkitProcessorURL, path.EmailID)

	processorBody := ReprocessContentProcessorBody{
		LabelsToReprocess: labelsToReprocessStr,
	}

	reqBody, err := json.Marshal(processorBody)
	if err != nil {
		return err
	}

	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		reprocessContentURL,
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		log.Error(
			ctx,
			"failed to construct http request to send to drumkit",
			zap.Uint("emailID", path.EmailID),
			zap.Error(err),
		)

		return err
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := otel.TracingHTTPClient().Do(req)
	if err != nil || resp.StatusCode >= 400 {
		log.Error(ctx, "failed to reprocess email", zap.Uint("emailID", path.EmailID))
		return c.SendStatus(http.StatusInternalServerError)
	}
	defer resp.Body.Close()

	return c.SendStatus(http.StatusOK)
}
