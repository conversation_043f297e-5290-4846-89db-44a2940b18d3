package email

import (
	"context"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/go-redis/redismock/v9"
	"github.com/gofiber/fiber/v2"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"

	rediscommon "github.com/drumkitai/drumkit/common/redis"
)

func TestGetIngestionStatusByThreadID_Endpoint(t *testing.T) {
	// Create a mock Redis client
	mockRDB, mock := redismock.NewClientMock()
	originalRDB := rediscommon.RDB
	rediscommon.RDB = mockRDB
	defer func() {
		rediscommon.RDB = originalRDB
		_ = mockRDB.Close()
	}()

	// Create a Fiber app for testing
	app := fiber.New()
	app.Get("/email/ingest/status/:id", GetIngestionStatusByThreadID)

	tests := []struct {
		name           string
		threadID       string
		setupMock      func(mock redismock.ClientMock)
		expectedStatus int
		expectedBody   string
	}{
		{
			name:     "key doesn't exist - returns notInFlight",
			threadID: "thread-123",
			setupMock: func(mock redismock.ClientMock) {
				mock.ExpectGet("ingestion-status:thread-123").RedisNil()
			},
			expectedStatus: http.StatusOK,
			expectedBody:   `{"status":"notInFlight"}`,
		},
		{
			name:     "key exists with inFlight value",
			threadID: "thread-456",
			setupMock: func(mock redismock.ClientMock) {
				mock.ExpectGet("ingestion-status:thread-456").SetVal(`"inFlight"`)
			},
			expectedStatus: http.StatusOK,
			expectedBody:   `{"status":"inFlight"}`,
		},
		{
			name:     "key exists with other value - returns notInFlight",
			threadID: "thread-789",
			setupMock: func(mock redismock.ClientMock) {
				mock.ExpectGet("ingestion-status:thread-789").SetVal(`"completed"`)
			},
			expectedStatus: http.StatusOK,
			expectedBody:   `{"status":"notInFlight"}`,
		},
		{
			name:     "Redis error - returns internal server error",
			threadID: "thread-error",
			setupMock: func(mock redismock.ClientMock) {
				mock.ExpectGet("ingestion-status:thread-error").SetErr(redis.ErrClosed)
			},
			expectedStatus: http.StatusInternalServerError,
			expectedBody:   "redis: client is closed",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mock expectations
			tt.setupMock(mock)

			// Create test request
			req := httptest.NewRequest(http.MethodGet, "/email/ingest/status/"+tt.threadID, nil)
			req = req.WithContext(context.Background())

			// Execute request
			resp, err := app.Test(req)
			assert.NoError(t, err)
			defer resp.Body.Close()

			// Assert response
			assert.Equal(t, tt.expectedStatus, resp.StatusCode)

			// Read response body
			body, err := io.ReadAll(resp.Body)
			assert.NoError(t, err)
			actualBody := string(body)

			assert.Equal(t, tt.expectedBody, actualBody)

			// Verify all Redis expectations were met
			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}
