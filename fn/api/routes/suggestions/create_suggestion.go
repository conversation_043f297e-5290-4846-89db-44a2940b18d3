package suggestions

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	braintrustutil "github.com/drumkitai/drumkit/common/helpers/braintrust"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	quoteRequestDB "github.com/drumkitai/drumkit/common/rds/quote_request"
	tmscustomerDB "github.com/drumkitai/drumkit/common/rds/tms_customers"
	"github.com/drumkitai/drumkit/common/sentry"
)

// TODO: Pass in distance and FSC to GetQuickQuoteV2
type (
	CreateQuoteRequestSuggestionBody struct {
		// Metadata         Metadata               `json:"metadata"`
		SourceCategory models.SourceCategory  `json:"sourceCategory"`
		Source         models.IntegrationName `json:"source"`
		// If empty, endpoint autogenerates based on source, and suggested request data
		SourceExternalID     string          `json:"sourceExternalID"`
		SourceURL            string          `json:"sourceURL"`
		HTMLSnippet          string          `json:"htmlSnippet"`
		Customer             models.Customer `json:"customer"` // SuggestedRequest has only customer ID
		SelectedQuickQuoteID *uint           `json:"selectedQuickQuoteId,omitempty"`
		SuggestedRequest
	}

	Metadata struct {
		PortalCategory   models.SourceCategory  `json:"portalCategory"`
		Portal           models.IntegrationName `json:"portal"`
		PortalExternalID string                 `json:"portalExternalID"`
		PortalURL        string                 `json:"portalURL"`
	}

	CreateQuoteRequestSuggestionResponse = QuickQuoteSuggestionResponse
)

// CreateQuoteRequestSuggestion creates a new quote request suggestion from parsed data from a quoting/bidding portal.
// Note that in order to minimize latency, we do not call the LLM to generate the AI suggestion but instead use the
// data provided by the portal.
// FE fails open if this endpoint returns an error or times out as it's most critical for fuzzy searching customer name.
func CreateQuoteRequestSuggestion(c *fiber.Ctx) error {
	var body CreateQuoteRequestSuggestionBody
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()
	userServiceID := middleware.ServiceIDFromContext(c)
	userID := middleware.UserIDFromContext(c)

	var err error

	// If portal does not natively provide an external ID, we need to create one based on the request payload
	// for deduplication.
	if body.SourceExternalID == "" {
		body.SourceExternalID, err = generateSourceExternalID(body, userServiceID)
		if err != nil {
			log.Error(ctx, "error generating source external ID", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}
	}

	// Create quote request
	quoteRequest, err := createQuoteRequestFromSuggestion(ctx, userServiceID, userID, body)
	if err != nil {
		log.Error(ctx, "error creating quote request", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	// Proactively check for regex parsing issues/updated webpages
	validateQuoteRequest(ctx, body)

	// Check if quote request already exists to avoid duplicates which skews metrics
	// (this may happen if user refreshes the page or navigates back and forth between QRs)
	existingQR, err := quoteRequestDB.GetQuoteRequestBySourceExternalID(ctx, userServiceID, body.SourceExternalID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Error(ctx, "error checking existing quote request", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if existingQR.ID != 0 {
		quoteRequest.ID = existingQR.ID
		if err := quoteRequestDB.UpdateSuggestedRequest(ctx, &quoteRequest); err != nil {
			log.WarnNoSentry(ctx, "error updating suggested request", zap.Error(err))
		}

		log.Info(ctx, "returning existing quote request", zap.Uint("quoteRequestID", existingQR.ID))
		return c.Status(http.StatusOK).JSON(QuoteRequestModelToResponse(quoteRequest, 0))
	}

	if err := quoteRequestDB.CreateQuoteRequest(ctx, &quoteRequest); err != nil {
		log.Error(ctx, "error creating quote request", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	// Convert to response format with newly created DB ID
	response := QuoteRequestModelToResponse(quoteRequest, 0)

	// Submit to Braintrust asynchronously with a 1-minute timeout
	go sentry.WithHub(ctx, func(ctx context.Context) {
		asyncCtx := log.InheritContext(ctx, context.Background())
		asyncCtx, cancel := context.WithTimeout(asyncCtx, time.Minute)
		defer cancel()

		err := braintrustutil.UpdateQuoteRequestWithBraintrustLogs(asyncCtx, quoteRequest, body.HTMLSnippet)
		if err != nil {
			log.Error(
				asyncCtx,
				"failed to submit to Braintrust",
				zap.Error(err),
				zap.Uint("quoteRequestID", quoteRequest.ID),
			)

			return
		}
	})

	return c.Status(http.StatusOK).JSON(response)
}

func generateSourceExternalID(body CreateQuoteRequestSuggestionBody, serviceID uint) (string, error) {
	payload, err := json.Marshal(body.SuggestedRequest)
	if err != nil {
		return "", fmt.Errorf("failed to marshal suggested quote request: %w", err)
	}

	return fmt.Sprintf("service-%d-source-%s-payload-%s-%s",
		serviceID,
		body.Source,
		body.Customer.Name,
		string(payload),
	), nil
}

func createQuoteRequestFromSuggestion(
	ctx context.Context,
	userServiceID uint,
	userID uint,
	body CreateQuoteRequestSuggestionBody,
) (models.QuoteRequest, error) {

	// Get TMS integrations for the service
	tmsIntegrations, err := integrationDB.GetTMSListByServiceID(ctx, userServiceID)
	if err != nil {
		return models.QuoteRequest{}, fmt.Errorf("error fetching TMS integrations: %w", err)
	}

	if len(tmsIntegrations) == 0 {
		log.Info(ctx, "no TMS integrations found for service")
	}

	// Fuzzy search for customer in each TMS (usually just one)
	var customer models.TMSCustomer
	for _, tms := range tmsIntegrations {
		customer, err = tmscustomerDB.GetCustomerByName(ctx, tms.ID, body.Customer.Name)
		if err == nil {
			break
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			log.WarnNoSentry(ctx, "error fetching customer", zap.Error(err))
		}
	}

	// If no TMS integrations yet/customer not found, still capture the parsed name
	if customer.ID == 0 {
		customer.Name = body.Customer.Name
	}

	quoteRequest := models.QuoteRequest{
		ServiceID:            userServiceID,
		UserID:               userID,
		Status:               models.Pending,
		SourceCategory:       body.SourceCategory,
		Source:               body.Source,
		SourceExternalID:     body.SourceExternalID,
		SourceURL:            body.SourceURL,
		SelectedQuickQuoteID: body.SelectedQuickQuoteID,

		SuggestedRequest: models.QuoteLoadInfo{
			CustomerID:           customer.ID,
			Customer:             customer.CompanyCoreInfo,
			TransportType:        body.TransportType,
			Distance:             body.DistanceMiles,
			FuelSurchargePerMile: body.FuelSurchargePerMile,
			FuelSurchargeTotal:   body.FuelSurchargeTotal,
			PickupLocation: models.Address{
				City:  body.PickupCity,
				State: body.PickupState,
				Zip:   body.PickupZip,
			},
			DeliveryLocation: models.Address{
				City:  body.DeliveryCity,
				State: body.DeliveryState,
				Zip:   body.DeliveryZip,
			},
			Stops: body.Stops,
		},
	}

	if body.PickupDate != nil {
		quoteRequest.SuggestedRequest.PickupDate = models.NullTime{
			Time:  *body.PickupDate,
			Valid: body.PickupDate != nil,
		}
	}

	if body.DeliveryDate != nil {
		quoteRequest.SuggestedRequest.DeliveryDate = models.NullTime{
			Time:  *body.DeliveryDate,
			Valid: body.DeliveryDate != nil,
		}
	}

	return quoteRequest, nil
}

func validateQuoteRequest(ctx context.Context, body CreateQuoteRequestSuggestionBody) {
	parsed := body.SuggestedRequest
	if isInsufficientAddress(parsed.PickupCity, parsed.PickupState, parsed.PickupZip) ||
		isInsufficientAddress(parsed.DeliveryCity, parsed.DeliveryState, parsed.DeliveryZip) {

		log.Warn(
			ctx,
			"incomplete quote request parsed from portal, check regex parsing",
			zap.Any("parsed", parsed),
			zap.String("source", string(body.Source)),
			zap.String("source_url", body.SourceURL),
		)
	}
}

// Unlike LLM, we expect both city & zip to be present in portal regex parsing.
// If a new portal does not support all 3, update this function
func isInsufficientAddress(city, state, zip string) bool {
	return city == "" || state == "" || zip == ""
}
