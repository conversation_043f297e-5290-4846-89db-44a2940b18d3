package suggestions

import (
	"errors"
	"fmt"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	suggestionsDB "github.com/drumkitai/drumkit/common/rds/suggestedloadchanges"
)

type (
	SkipSuggestionPath struct {
		ID uint
	}

	SkipSuggestionBody struct {
		Status models.SuggestionStatus `json:"status" validate:"required,oneof=rejected"`
	}

	SkipSuggestionResponse struct {
		Message string `json:"message,omitempty"`
	}
)

// Generic handler for skipping suggestions of any kind
func SkipSuggestion(c *fiber.Ctx) error {
	var path SkipSuggestionPath
	var body SkipSuggestionBody
	if err := api.Parse(c, &path, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()

	suggestion, err := suggestionsDB.GetSuggestionByID(ctx, path.ID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(http.StatusNotFound).SendString(
				fmt.Sprintf("suggestion ID %d not found", path.ID))
		}

		log.WarnNoSentry(ctx, "get suggestions endpoint - suggestion query error", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	claims := middleware.ClaimsFromContext(c)
	if claims.Email != suggestion.Account {
		log.ErrorNoSentry(
			ctx,
			"unauthorized: email from token does not match DB",
			zap.String("dbSuggestionAccount", suggestion.Account),
			zap.String("claimsEmail", claims.Email),
		)

		return c.SendStatus(http.StatusUnauthorized)
	}

	suggestion.Status = body.Status
	if err = suggestionsDB.UpdateSuggestion(ctx, suggestion); err != nil {
		errMsg := fmt.Sprintf("Suggestion %d was not saved in the DB with status %s."+
			" Please check the DB and make the change manually if necessary.",
			suggestion.ID, body.Status)
		log.Error(ctx, errMsg, zap.Error(err))

		response := SkipSuggestionResponse{
			Message: "Uh oh. Something went wrong trying to skip the suggestion.",
		}

		return c.Status(http.StatusUnprocessableEntity).JSON(response)
	}

	response := SkipSuggestionResponse{
		Message: "Suggestion skipped",
	}

	return c.Status(http.StatusOK).JSON(response)
}
