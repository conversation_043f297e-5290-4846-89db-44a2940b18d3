package outbox

import (
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/rds/generatedemails"
)

type (
	DeleteEmailPath struct {
		ID uint `params:"id" validate:"required"`
	}

	DeleteEmailResponse struct {
		Message string `json:"message,omitempty"`
	}
)

func DeleteEmail(c *fiber.Ctx) error {
	var path DeleteEmailPath
	if err := api.Parse(c, &path, nil, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Any("pathParams", path))

	existingEmail, err := generatedemails.GetEmail(ctx, path.ID)
	if err != nil {
		log.Error(ctx, "generatedemails query error", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	err = generatedemails.DeletePendingEmail(ctx, existingEmail)
	if err != nil {
		log.Error(ctx, "error deleting generated email in DB", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	result := DeleteEmailResponse{
		Message: "Deleting email succeeded",
	}

	return c.Status(http.StatusOK).JSON(result)
}
