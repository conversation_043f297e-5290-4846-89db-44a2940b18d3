package outbox

import (
	"encoding/json"
	"net/http"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/sfn"
	"github.com/gofiber/fiber/v2"
	"github.com/lib/pq"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds/generatedemails"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	"github.com/drumkitai/drumkit/fn/api/env"
	apiutil "github.com/drumkitai/drumkit/fn/api/util"
)

type (
	UpdateEmailPath struct {
		ID uint `params:"id" validate:"required"`
	}

	UpdateEmailBody struct {
		Recipient    string          `params:"recipient"`
		Subject      string          `params:"subject"`
		Body         string          `params:"body"`
		ScheduleSend models.NullTime `params:"scheduleSend"`
	}

	UpdateEmailResponse struct {
		Message string `json:"message,omitempty"`
	}
)

func UpdateEmail(c *fiber.Ctx) error {
	var path UpdateEmailPath
	var body UpdateEmailBody
	if err := api.Parse(c, &path, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Any("pathParams", path))

	user, err := userDB.GetByEmail(ctx, middleware.ClaimsFromContext(c).Email)
	if err != nil {
		return apiutil.HandleDBError(c, err, false, "user %s not found", middleware.ClaimsFromContext(c).Email)
	}

	if user.EmailSignature == "" {
		log.Warn(ctx, "user without signature trying to update generated email")
	}

	existingEmail, err := generatedemails.GetEmail(ctx, path.ID)
	if err != nil {
		log.Error(ctx, "generatedemails query error", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	existingEmail.Recipients = pq.StringArray{body.Recipient}
	existingEmail.Subject = body.Subject
	existingEmail.Body = body.Body

	oldScheduledSend := existingEmail.ScheduleSend
	existingEmail.ScheduleSend = body.ScheduleSend

	err = generatedemails.Update(ctx, existingEmail)
	if err != nil {
		log.Error(ctx, "error updating generated email in DB", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if env.Vars.AppEnv != "dev" && oldScheduledSend.Time != body.ScheduleSend.Time {
		cfg, err := config.LoadDefaultConfig(ctx, config.WithRegion("us-east-1"))
		if err != nil {
			log.Error(ctx, "error creating aws config", zap.Error(err))
		}

		sfnClient := sfn.NewFromConfig(cfg)

		payload := map[string]any{
			"email": map[string]any{
				"generatedEmailID":   existingEmail.ID,
				"senderEmailAddress": user.EmailAddress,
				"emailProvider":      user.EmailProvider,
			},
			"triggerTime": existingEmail.ScheduleSend.Time,
		}

		payloadBytes, err := json.Marshal(payload)
		if err != nil {
			log.Error(ctx, "error marshaling step function payload", zap.Error(err))
		}

		input := &sfn.StartExecutionInput{
			StateMachineArn: aws.String(env.Vars.StateMachineARN),
			Input:           aws.String(string(payloadBytes)),
		}

		_, err = sfnClient.StartExecution(ctx, input)
		if err != nil {
			log.Error(ctx, "error executing step function", zap.Error(err))
		}
	}

	result := UpdateEmailResponse{
		Message: "Updating email succeeded",
	}

	return c.Status(http.StatusOK).JSON(result)
}
