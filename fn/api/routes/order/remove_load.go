package order

import (
	"net/http"
	"strconv"

	"github.com/gofiber/fiber/v2"

	orderdb "github.com/drumkitai/drumkit/common/rds/order"
)

// RemoveLoadFromOrder removes a load from an order
func RemoveLoadFromOrder(c *fiber.Ctx) error {
	orderID, err := strconv.ParseUint(c.Params("id"), 10, 32)
	if err != nil {
		return c.Status(http.StatusBadRequest).SendString("Invalid order ID format")
	}

	order, err := orderdb.GetOrderFromDB(c.UserContext(), uint(orderID))
	if err != nil {
		return c.Status(http.StatusNotFound).SendString("Order not found")
	}

	// Clear the LoadID to remove the association
	order.LoadID = 0

	// Save the updated order
	if err := orderdb.UpdateOrderInDB(c.UserContext(), order); err != nil {
		return c.Status(http.StatusInternalServerError).SendString("Failed to update order")
	}

	return c.SendStatus(http.StatusOK)
}
