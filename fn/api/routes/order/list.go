package order

import (
	"net/http"

	"github.com/gofiber/fiber/v2"

	orderdb "github.com/drumkitai/drumkit/common/rds/order"
)

// ListOrders lists all orders with optional filtering
func ListOrders(c *fiber.Ctx) error {
	status := c.Query("status")
	serviceID := c.Query("serviceID")
	fromDate := c.Query("fromDate")
	toDate := c.Query("toDate")

	orders, err := orderdb.ListOrdersFromDB(c.UserContext(), status, serviceID, fromDate, toDate)
	if err != nil {
		return c.Status(http.StatusInternalServerError).SendString("Failed to retrieve orders")
	}

	return c.JSON(orders)
}
