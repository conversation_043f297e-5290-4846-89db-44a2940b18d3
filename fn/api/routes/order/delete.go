package order

import (
	"net/http"
	"strconv"

	"github.com/gofiber/fiber/v2"

	orderdb "github.com/drumkitai/drumkit/common/rds/order"
)

// DeleteOrder deletes an order
func DeleteOrder(c *fiber.Ctx) error {
	id, err := strconv.ParseUint(c.<PERSON><PERSON>("id"), 10, 32)
	if err != nil {
		return c.Status(http.StatusBadRequest).SendString("Invalid order ID format")
	}

	if err := orderdb.DeleteOrderFromDB(c.UserContext(), uint(id)); err != nil {
		return c.Status(http.StatusInternalServerError).SendString("Failed to delete order")
	}

	return c.SendStatus(http.StatusNoContent)
}
