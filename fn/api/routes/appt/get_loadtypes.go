package appt

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"golang.org/x/exp/slices"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/integrations/scheduling"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	"github.com/drumkitai/drumkit/common/redis"
	"github.com/drumkitai/drumkit/fn/api/env"
)

type (
	GetLoadTypesQuery struct {
		WarehouseID   string `json:"warehouseID"`
		Source        string `json:"source"`
		IntegrationID uint   `json:"integrationID"`
	}

	GetLoadTypesResponse struct {
		LoadTypes []LoadTypesResponseObject `json:"loadTypes"`
	}

	LoadTypesResponseObject struct {
		ID                        string                `json:"id"`
		Name                      string                `json:"name"`
		Direction                 string                `json:"direction"`
		AllowCarrierDockSelection bool                  `json:"allowCarrierDockSelection"`
		Docks                     []LoadTypesDockObject `json:"docks"`
		Message                   string                `json:"message,omitempty"`
	}

	LoadTypesDockObject struct {
		ID   string `json:"id"`
		Name string `json:"name"`
	}
)

// TODO: Consolidate into GetWarehouse
func GetLoadTypes(c *fiber.Ctx) error {
	var query GetLoadTypesQuery
	if err := api.Parse(c, nil, &query, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	userID := middleware.UserIDFromContext(c)
	ctx := log.With(
		c.UserContext(),
		zap.Uint("userID", userID),
		zap.String("schedulingIntegration", query.Source),
		zap.Any("requestQuery", query),
	)

	user, err := rds.GetUserByID(ctx, userID)
	if err != nil {
		log.Error(ctx, "GetUserByID failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	// TODO: remove this when the frontend passes in a nonempty source
	if query.Source == "" {
		query.Source = "opendock"
	}

	switch query.Source {
	case string(models.Opendock):
		resp, err := handleOpendockLoadType(ctx, user, &query)
		if err != nil {
			if strings.Contains(
				err.Error(),
				dbFetchSchedulingIntegrationError,
			) && errors.Is(err, gorm.ErrRecordNotFound) {
				// Check if service has any scheduling integrations and user just needs to be assigned
				// to a group (GetSchedulerByServiceUserIDsAndName already checks if user has a group,
				// so we don't need to here)
				_, err := integrationDB.GetByServiceIDAndType(ctx, user.ServiceID, models.Scheduling)
				if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
					// Possible if Appointment feature flag enabled but no scheduling integrations
					// set up yet
					log.Warn(
						ctx,
						"service is trying appt scheduling but does not have any scheduling"+
							"integrations",
						zap.Error(err),
					)

					return c.Status(http.StatusNotFound).JSON(LoadTypesResponseObject{
						Message: "Please contact our team to finish setting up your" +
							"scheduling integration(s)",
					})
				}

				log.WarnNoSentry(
					ctx,
					"service has only group-based scheduling integrations but user is"+
						"not assigned to a group",
					zap.Error(err),
				)

				return c.Status(http.StatusNotFound).JSON(LoadTypesResponseObject{
					Message: fmt.Sprintf(
						"Please go to %s/welcome to add yourself to a group in order"+
							"to use their scheduling integration",
						api.GetPortalURLByEnv(env.Vars.AppEnv),
					),
				})
			}

			log.Error(ctx, "getLoadTypes failed", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}

		return c.Status(http.StatusOK).JSON(resp)

	case string(models.YardView):
		return handleYardViewLoadType(c, user, &query)

	default:
		return c.Status(http.StatusBadRequest).SendString("unsupported scheduling integration")
	}
}

func handleOpendockLoadType(
	ctx context.Context,
	user models.User,
	query *GetLoadTypesQuery,
) (*GetLoadTypesResponse, error) {

	integration, err := integrationDB.GetSchedulerByServiceUserIDsAndName(
		ctx,
		user.ID,
		user.ServiceID,
		query.Source,
		query.IntegrationID,
	)
	if err != nil {
		return nil, fmt.Errorf("%s: %w", dbFetchSchedulingIntegrationError, err)
	}

	client, err := scheduling.GetCachedClient(ctx, integration)
	if err != nil {
		return nil, fmt.Errorf("unable to connect to %s: %w", query.Source, err)
	}

	loadTypes, err := client.GetLoadTypes(ctx, models.GetLoadTypesRequest{
		WarehouseID: query.WarehouseID,
		Search:      `{"allowCarrierScheduling":true}`,
	})
	if err != nil {
		return nil, fmt.Errorf("%s.GetLoadTypes failed: %w", query.Source, err)
	}

	// fetching docks so we can filter out load types with no docks available
	docks, err := client.GetDocks(ctx, models.GetDocksRequest{
		Search: `{"warehouseId":"` + query.WarehouseID + `"}`,
	})
	if err != nil {
		return nil, fmt.Errorf("%s.GetDocks failed: %w", query.Source, err)
	}

	// Keeping only the information from Load Types we need
	var loadTypesResult []LoadTypesResponseObject

	for _, lt := range loadTypes {
		loadTypeDocks := []LoadTypesDockObject{}

		// TODO: optimize nested loops
		// looping over docks to make a list of ones that support the Load Type on iteration
		for _, dock := range docks {
			if slices.Contains(dock.LoadTypeIDs, lt.ID) {
				loadTypeDocks = append(loadTypeDocks, LoadTypesDockObject{
					ID:   dock.ID,
					Name: dock.Name,
				})
			}
		}

		loadTypesResult = append(loadTypesResult, LoadTypesResponseObject{
			ID:                        lt.ID,
			Name:                      lt.Name,
			Direction:                 lt.Direction,
			AllowCarrierDockSelection: lt.Settings.AllowCarrierDockSelection,
			Docks:                     loadTypeDocks,
		})
	}

	return &GetLoadTypesResponse{
		LoadTypes: loadTypesResult,
	}, nil
}

// handleYardViewLoadType checks redis cache for load types first, if not found
// then fetches from yardview via Cyclops
func handleYardViewLoadType(c *fiber.Ctx, user models.User, query *GetLoadTypesQuery) error {
	ctx := log.With(
		c.UserContext(),
		zap.String("emailAddress", user.EmailAddress),
		zap.Uint("userID", user.ID),
		zap.Uint("serviceID", user.ServiceID),
		zap.String("schedulingIntegration", query.Source),
		zap.Any("requestQuery", query),
	)

	integration, err := integrationDB.GetSchedulerByServiceUserIDsAndName(
		ctx,
		user.ID,
		user.ServiceID,
		query.Source,
		query.IntegrationID,
	)
	if err != nil {
		log.Errorf(ctx, "%s: %w", dbFetchSchedulingIntegrationError, err)
		return c.SendStatus(http.StatusInternalServerError)
	}

	// Check Redis cache for load types
	cacheKey := fmt.Sprintf(
		"yardview-load_types-%d-%s-%s",
		user.ServiceID,
		integration.Username,
		query.WarehouseID,
	)
	cachedLoadTypes, found, redisErr := redis.GetKey[GetLoadTypesResponse](ctx, cacheKey)
	if redisErr == nil && found {
		return c.Status(http.StatusOK).JSON(GetLoadTypesResponse{LoadTypes: cachedLoadTypes.LoadTypes})
	}

	client, err := scheduling.GetCachedClient(ctx, integration)
	if err != nil {
		log.Errorf(ctx, "unable to connect to %s: %w", query.Source, err)
		return c.SendStatus(http.StatusInternalServerError)
	}

	loadTypes, err := client.GetLoadTypes(ctx, models.GetLoadTypesRequest{WarehouseID: query.WarehouseID})
	if err != nil {
		log.Errorf(ctx, "%s.GetLoadTypes failed: %w", query.Source, err)
		return c.SendStatus(http.StatusInternalServerError)
	}

	var loadTypesResult []LoadTypesResponseObject

	for _, lt := range loadTypes {
		loadTypesResult = append(loadTypesResult, LoadTypesResponseObject{
			ID:   lt.ID,
			Name: lt.Name,
		})
	}

	resp := GetLoadTypesResponse{LoadTypes: loadTypesResult}
	// Store load types in Redis with 3h TTL
	err = redis.SetKey(ctx, cacheKey, resp, 3*time.Hour)
	if err != nil {
		log.WarnNoSentry(ctx, "error setting cached yardview load types in redis", zap.Error(err))
	}

	return c.Status(http.StatusOK).JSON(resp)
}
