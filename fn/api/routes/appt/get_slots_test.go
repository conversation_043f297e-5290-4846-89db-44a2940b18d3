package appt

import (
	"errors"
	"testing"
	"time"

	"github.com/drumkitai/drumkit/common/models"
	apitesting "github.com/drumkitai/drumkit/fn/api/testing"
)

func TestGetOpenSlotsValidation(t *testing.T) {
	validator := apitesting.NewValidationTest(t)

	t.Run("valid query params", func(t *testing.T) {
		query := GetOpenSlotsQuery{
			LoadTypeID: "test-load",
			GetOpenSlotsRequest: models.GetOpenSlotsRequest{
				Start:       time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC),
				End:         time.Date(2025, 1, 2, 0, 0, 0, 0, time.UTC),
				WarehouseID: "warehouse-1",
			},
		}

		if err := validator.TestQuery(query); err != nil {
			t.Errorf("Expected no error, got: %v", err)
		}
	})

	t.Run("missing required LoadTypeID", func(t *testing.T) {
		query := GetOpenSlotsQuery{
			GetOpenSlotsRequest: models.GetOpenSlotsRequest{
				Start:       time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC),
				End:         time.Date(2025, 1, 2, 0, 0, 0, 0, time.UTC),
				WarehouseID: "warehouse-1",
			},
		}

		err := validator.TestQuery(query)
		if err == nil {
			t.Error("Expected error for missing LoadTypeID, got none")
			return
		}

		var validationErr *apitesting.ValidationError
		if !errors.As(err, &validationErr) {
			t.Errorf("Expected ValidationError, got %T: %v", err, err)
			return
		}

		// Verify we got exactly one query error
		if validationErr.NumQueryErrors() != 1 {
			t.Errorf("Expected exactly 1 query validation error, got %d query errors: %v",
				validationErr.NumQueryErrors(), validationErr)
			return
		}

		// Verify it's the specific query error we expect
		if !validationErr.Contains("loadTypeID is a required field") {
			t.Errorf("Expected query error about loadTypeID field, got: %v", validationErr)
		}
	})

	t.Run("missing required Start time", func(t *testing.T) {
		query := GetOpenSlotsQuery{
			LoadTypeID: "test-load",
			GetOpenSlotsRequest: models.GetOpenSlotsRequest{
				// Start intentionally omitted to test required validation
				End:         time.Date(2025, 1, 2, 0, 0, 0, 0, time.UTC),
				WarehouseID: "warehouse-1",
			},
		}

		err := validator.TestQuery(query)
		if err == nil {
			t.Error("Expected error for missing Start time, got none")
			return
		}

		var validationErr *apitesting.ValidationError
		if !errors.As(err, &validationErr) {
			t.Errorf("Expected ValidationError, got %T: %v", err, err)
			return
		}

		// Verify we got exactly one query error
		if validationErr.NumQueryErrors() != 1 {
			t.Errorf("Expected exactly 1 query validation error, got %d query errors: %v",
				validationErr.NumQueryErrors(), validationErr)
			return
		}

		// Verify it's the specific query error we expect
		if !validationErr.Contains("start is a required field") {
			t.Errorf("Expected query error about Start time field, got: %v", validationErr)
		}
	})

	t.Run("missing required End time", func(t *testing.T) {
		query := GetOpenSlotsQuery{
			LoadTypeID: "test-load",
			GetOpenSlotsRequest: models.GetOpenSlotsRequest{
				Start: time.Now(),
				// End intentionally omitted to test required validation
			},
		}

		err := validator.TestQuery(query)
		if err == nil {
			t.Error("Expected error about missing End time, got none")
			return
		}

		var validationErr *apitesting.ValidationError
		if !errors.As(err, &validationErr) {
			t.Errorf("Expected ValidationError, got %T: %v", err, err)
			return
		}

		// Verify we got exactly one query error
		if validationErr.NumQueryErrors() != 1 {
			t.Errorf("Expected exactly 1 query validation error, got %d query errors: %v",
				validationErr.NumQueryErrors(), validationErr)
			return
		}

		// Verify it's the specific query error we expect
		if !validationErr.Contains("end is a required field") {
			t.Errorf("Expected query error about End time field, got: %v", validationErr)
		}
	})
}
