package appt

import (
	"testing"

	apitesting "github.com/drumkitai/drumkit/fn/api/testing"
)

func TestGetLoadTypes(t *testing.T) {
	validator := apitesting.NewValidationTest(t)

	t.Run("valid query params", func(t *testing.T) {
		query := GetLoadTypesQuery{
			WarehouseID: "test-load",
			Source:      "9b1beeac-c174-4282-860e-36eac63779c1",
		}

		if err := validator.TestQuery(query); err != nil {
			t.<PERSON><PERSON><PERSON>("Expected no error, got: %v", err)
		}
	})
}
