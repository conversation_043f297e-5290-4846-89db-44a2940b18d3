package appt

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"reflect"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/integrations/scheduling"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	warehouseDB "github.com/drumkitai/drumkit/common/rds/warehouses"
	"github.com/drumkitai/drumkit/fn/api/env"
)

type (
	GetWarehouseQuery struct {
		WarehouseID   string                 `json:"warehouseID"`
		Source        models.WarehouseSource `json:"source"`
		ConsigneeName string                 `json:"consigneeName"`
		IntegrationID uint                   `json:"integrationID"`
	}

	GetWarehouseResponse struct {
		WarehouseTimezone               string `json:"warehouseTimezone"`
		models.CustomApptFieldsTemplate `json:"customApptFieldsTemplate"`
		Settings                        models.WarehouseSettings `json:"settings"`
		DefaultSubscribedEmail          string                   `json:"defaultSubscribedEmail"`

		Message string `json:"message"` // User-facing error message
	}
)

const dbFetchSchedulingIntegrationError = "unable to fetch scheduling integration"

func GetWarehouse(c *fiber.Ctx) error {
	var query GetWarehouseQuery
	if err := api.Parse(c, nil, &query, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	// TODO: remove this when the frontend passes in a nonempty source
	if query.Source == "" {
		query.Source = models.OpendockSource
	}

	ctx := log.With(c.UserContext(), zap.Any("requestQuery", query))
	userID := middleware.UserIDFromContext(c)

	user, err := rds.GetUserByID(ctx, userID)
	if err != nil {
		log.Error(ctx, "GetUserByID failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	resp, err := getWarehouse(ctx, user, query)
	if err != nil {
		errStr := strings.ToLower(err.Error())
		isNotFound := strings.Contains(errStr, "not found") || strings.Contains(errStr, "404")

		if isNotFound {
			log.WarnNoSentry(ctx, "getWarehouse failed - warehouse not found", zap.String("err", errStr))
			return c.Status(http.StatusNotFound).SendString(errStr)
		}

		if strings.Contains(errStr, dbFetchSchedulingIntegrationError) &&
			errors.Is(err, gorm.ErrRecordNotFound) {

			// Check if service has any scheduling integrations and user just needs to be assigned to a group
			// (GetSchedulerByServiceUserIDsAndName already checks if user has a group, so we don't need to
			// here)
			_, err := integrationDB.GetByServiceIDAndType(ctx, user.ServiceID, models.Scheduling)
			if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
				// Possible if Appointment feature flag enabled but no scheduling integrations set up yet
				log.Warn(
					ctx,
					"service is trying appt scheduling but does not have any scheduling integrations",
					zap.Error(err),
				)

				return c.Status(http.StatusNotFound).JSON(GetWarehouseResponse{
					Message: "Please contact our team to finish setting up your scheduling integration(s)",
				})
			}

			log.WarnNoSentry(
				ctx,
				"service has only group-based scheduling integrations but user is not assigned to a group",
				zap.Error(err),
			)

			return c.Status(http.StatusNotFound).JSON(GetWarehouseResponse{
				Message: fmt.Sprintf(
					"Please go to %s/welcome to add yourself to a group in order to use their scheduling integration",
					api.GetPortalURLByEnv(env.Vars.AppEnv),
				),
			})
		}

		log.Error(ctx, "getWarehouse failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.Status(http.StatusOK).JSON(resp)
}

func getWarehouse(
	ctx context.Context,
	user models.User,
	query GetWarehouseQuery,
) (*GetWarehouseResponse, error) {

	warehouseFromDB, err := warehouseDB.GetWarehouseByIDAndSource(
		ctx,
		user.ServiceID,
		query.Source,
		query.WarehouseID,
	)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Error(ctx, "error fetching warehouse from db", zap.Error(err))
		return nil, fmt.Errorf("error fetching warehouse %w", err)
	}

	isWarehouseInfoStale := warehouseFromDB == nil || time.Now().After(warehouseFromDB.UpdatedAt.Add(24*time.Hour))

	// If wh doesn't exist, is stale, we don't have settings or appt fieds on DB,
	// fetch it from the integration and store them.
	if isWarehouseInfoStale ||
		warehouseFromDB.CustomApptFieldsTemplate == nil ||
		reflect.DeepEqual(warehouseFromDB.Settings, models.WarehouseSettings{}) {

		refreshedWH, err := fetchWarehouse(ctx, user, query)
		if err != nil {
			return nil, fmt.Errorf("error fetching warehouse from %s: %w", query.Source, err)
		}

		refreshedWH.PersistDrumkitConfig(*warehouseFromDB)
		warehouseFromDB = &refreshedWH

		err = warehouseDB.Upsert(ctx, warehouseFromDB)
		if err != nil {
			log.Error(ctx, "error updating warehouse with refreshed data", zap.Error(err))
			return nil, fmt.Errorf("error updating warehouse %w", err)
		}

	}

	showEmail := showSubscribedEmailByService(ctx, user, warehouseFromDB)
	warehouseResponse := &GetWarehouseResponse{
		WarehouseTimezone:        warehouseFromDB.WarehouseTimezone,
		CustomApptFieldsTemplate: warehouseFromDB.CustomApptFieldsTemplate,
		Settings:                 warehouseFromDB.Settings,
		DefaultSubscribedEmail:   helpers.Ternary(showEmail, warehouseFromDB.DefaultSubscribedEmail, ""),
	}

	return warehouseResponse, nil
}

func fetchWarehouse(
	ctx context.Context,
	user models.User,
	query GetWarehouseQuery,
) (models.Warehouse, error) {

	integration, err := integrationDB.GetSchedulerByServiceUserIDsAndName(
		ctx,
		user.ID,
		user.ServiceID,
		string(query.Source),
		query.IntegrationID,
	)
	if err != nil {
		return models.Warehouse{}, fmt.Errorf("%s: %w", dbFetchSchedulingIntegrationError, err)
	}

	client, err := scheduling.GetCachedClient(ctx, integration)
	if err != nil {
		return models.Warehouse{}, fmt.Errorf("unable to connect to %s: %w", integration.Name, err)
	}

	warehouse, err := client.GetWarehouse(ctx, query.WarehouseID)
	if err != nil {
		return models.Warehouse{}, fmt.Errorf("%s.GetWarehouse failed: %w", integration.Name, err)
	}

	return warehouse, nil
}

// FIXME: This is a quick hack to prevent NFI default subscribed emails from being shown to Fetch Freight users
// Follow-up ticket: ENG-3078
func showSubscribedEmailByService(ctx context.Context, user models.User, wh *models.Warehouse) bool {
	if wh == nil {
		return false
	}

	emailParts := strings.Split(user.EmailAddress, "@")

	if len(emailParts) < 2 {
		// This case should not happen
		log.Warn(ctx, "malformed user email, defaulting to false")
		return false
	}
	domain := emailParts[1]

	return strings.Contains(strings.ToLower(wh.DefaultSubscribedEmail), strings.ToLower(domain))
}
