package appt

import (
	"errors"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/tms_customer_scheduling_integration"
	tmsCustomerDB "github.com/drumkitai/drumkit/common/rds/tms_customers"
)

type GetSchedulingIntegrationsByCustomerQuery struct {
	TMSIntegrationID      uint   `json:"tmsIntegrationId" validate:"required"`
	TMSCustomerExternalID string `json:"tmsCustomerExternalId"`
	CustomerName          string `json:"customerName"`
	WarehouseID           uint   `json:"warehouseId" validate:"required"`
}

type IntegrationWithUsage struct {
	models.IntegrationCore
	UsageCount int `json:"usageCount"`
}

type GetSchedulingIntegrationsByCustomerResponse struct {
	IntegrationsWithUsage []IntegrationWithUsage `json:"integrationsWithUsage"`
}

// GetSchedulingIntegrationsByCustomer returns all scheduling integrations associated with a customer and warehouse
// Handles either TMSCustomer or CustomerName as the customer identifier.
func GetSchedulingIntegrationsByCustomer(c *fiber.Ctx) error {
	query := GetSchedulingIntegrationsByCustomerQuery{}
	if err := api.Parse(c, nil, &query, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Any("requestQuery", query))
	userServiceID := middleware.ServiceIDFromContext(c)

	tmsCustomer, err := tmsCustomerDB.GetByExternalTMSID(
		ctx,
		query.TMSIntegrationID,
		query.TMSCustomerExternalID,
	)
	log.Info(ctx, "found tms customer", zap.Any("tmsCustomer", tmsCustomer))

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.WarnNoSentry(ctx, "error getting tms customer", zap.Error(err))
		// Fail open - continue with customer name query
	}
	tmsCustomerID := tmsCustomer.ID
	log.Info(ctx, "tms customer id", zap.Uint("tmsCustomerID", tmsCustomerID))

	// If we have no TMS customer ID and no customer name, we can't proceed
	if tmsCustomerID == 0 && query.CustomerName == "" {
		log.Error(ctx, "missing customer name")
		return c.SendStatus(http.StatusBadRequest)
	}

	var integrationsWithUsage []IntegrationWithUsage
	if tmsCustomerID != 0 {
		associations, err := integrationDB.GetAllIntegrationsForTMSCustomer(
			ctx,
			userServiceID,
			query.TMSIntegrationID,
			query.WarehouseID,
			tmsCustomerID,
		)
		if err != nil {
			log.Error(ctx, "error getting scheduling integrations", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}

		for _, association := range associations {
			integrationsWithUsage = append(integrationsWithUsage, toIntegrationWithUsage(&association))
		}
	} else if query.CustomerName != "" {
		associations, err := integrationDB.GetAllIntegrationsForCustomerName(
			ctx, userServiceID, query.TMSIntegrationID, query.WarehouseID, query.CustomerName)
		if err != nil {
			log.Error(ctx, "error getting scheduling integrations", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}

		for _, association := range associations {
			integrationsWithUsage = append(integrationsWithUsage, toIntegrationWithUsage(&association))
		}
	}

	log.Info(ctx, "found scheduling integrations", zap.Any("integrationsWithUsage", integrationsWithUsage))

	return c.Status(http.StatusOK).JSON(GetSchedulingIntegrationsByCustomerResponse{
		IntegrationsWithUsage: integrationsWithUsage,
	})
}

// toIntegrationWithUsage converts a TMSCustomerSchedulingIntegration to IntegrationWithUsage
func toIntegrationWithUsage(
	association *models.TMSCustomerSchedulingIntegration,
) IntegrationWithUsage {
	return IntegrationWithUsage{
		IntegrationCore: models.IntegrationCore{
			ID:           association.SchedulingIntegration.ID,
			Name:         association.SchedulingIntegration.Name,
			Type:         association.SchedulingIntegration.Type,
			Username:     association.SchedulingIntegration.Username,
			Tenant:       association.SchedulingIntegration.Tenant,
			FeatureFlags: association.SchedulingIntegration.FeatureFlags,
		},
		UsageCount: association.UsageCount,
	}
}
