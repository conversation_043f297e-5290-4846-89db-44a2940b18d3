package appt

import (
	"context"
	"fmt"
	"net/http"
	"strings"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/integrations/scheduling"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	warehouseDB "github.com/drumkitai/drumkit/common/rds/warehouses"
)

type (
	OnboardWarehousesBySourceBody struct {
		Source models.IntegrationName `json:"source"`
	}
)

func OnboardWarehousesBySource(c *fiber.Ctx) error {
	var body OnboardWarehousesBySourceBody
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Any("requestBody", body))

	// Endpoint only meant to be used by Drumkit
	email := middleware.ClaimsFromContext(c).Email
	if !strings.HasSuffix(email, "@drumkit.ai") {
		log.WarnNoSentry(ctx, "unauthorized: non-drumkit user trying to onboard warehouses")
		return c.SendStatus(http.StatusUnauthorized)
	}

	// Drumkit service MUST have an integration for source, in order to externally fetch warehouses.
	err := onboardWarehousesBySource(ctx, body.Source)
	if err != nil {
		log.Error(ctx, "onboardWarehousesBySource failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.SendStatus(http.StatusOK)
}

func onboardWarehousesBySource(ctx context.Context, source models.IntegrationName) error {
	integration, err := integrationDB.GetInternalSchedulerByName(ctx, source)
	if err != nil {
		log.Error(ctx, "onboardWarehousesBySource failed", zap.Error(err))
		return err
	}

	var allWarehouses []models.Warehouse

	client, err := scheduling.GetCachedClient(ctx, integration)
	if err != nil {
		log.Error(ctx, "creating scheduling integration client failed", zap.Error(err))
	}

	// if token is expired, then we call onboardIntegration again
	if integration.AccessToken != "" && integration.NeedsRefresh() {
		_, err := client.OnboardScheduler(ctx)
		if err != nil {
			// Some schedulers (e.g., Retalix) do not implement token onboarding/refresh
			if errtypes.IsNotImplementedError(err) {
				log.Info(
					ctx,
					"scheduler integration does not support token refresh; skipping",
					zap.String("name", string(integration.Name)),
				)
			} else {
				log.Error(ctx, "refreshing scheduler integration failed", zap.Error(err))
				return err
			}
		}
	}

	allWarehouses, err = client.GetAllWarehouses(ctx)
	if err != nil {
		return fmt.Errorf("unable to fetch all warehouses from scheduler: %w", err)
	}

	if len(allWarehouses) > 0 {
		err = warehouseDB.OnboardWarehouses(ctx, allWarehouses)
		if err != nil {
			return fmt.Errorf("unable to onboard warehouses: %w", err)
		}
	}

	return nil
}
