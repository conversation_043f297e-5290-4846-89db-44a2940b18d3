package appt

import (
	"context"
	"errors"
	"os"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	warehouseDB "github.com/drumkitai/drumkit/common/rds/warehouses"
	apitesting "github.com/drumkitai/drumkit/fn/api/testing"
)

func TestConfirmAppt(t *testing.T) {
	validator := apitesting.NewValidationTest(t)

	t.Run("valid request body", func(t *testing.T) {
		body := ConfirmApptBody{
			LoadID:            1,
			FreightTrackingID: "12345",
			DryRun:            false,
			StopType:          PickupStopType,
			StartTime:         time.Now(),
			WarehouseID:       "warehouse-1",
			WarehouseTimezone: "America/New_York",
			DockID:            "dock-1",
			LoadTypeID:        "test-load",
			SubscribedEmail:   "<EMAIL>",
			CcEmails: []string{
				"<EMAIL>",
			},
			TrailerType: "Cargo Van",
			Notes:       "this is a note",
			PONums:      "PRO 2080005",
			RefNumber:   "",
			CustomApptFieldsTemplate: []models.CustomApptField{
				{
					Name:                 "",
					Type:                 "",
					Label:                "",
					Value:                "",
					Description:          "",
					Placeholder:          "",
					DropDownValues:       []string{},
					HiddenFromCarrier:    false,
					RequiredForCarrier:   false,
					RequiredForCheckIn:   false,
					RequiredForWarehouse: false,
					MaxLengthOrValue:     0,
					MinLengthOrValue:     0,
				},
			},
		}

		if err := validator.TestBody(body); err != nil {
			t.Errorf("Expected no error, got: %v", err)
		}
	})

	t.Run("missing required LoadID", func(t *testing.T) {
		body := ConfirmApptBody{
			FreightTrackingID: "12345",
			DryRun:            false,
			StopType:          PickupStopType,
			StartTime:         time.Now(),
			WarehouseID:       "warehouse-1",
			WarehouseTimezone: "America/New_York",
			DockID:            "dock-1",
			LoadTypeID:        "test-load",
			SubscribedEmail:   "<EMAIL>",
			CcEmails: []string{
				"<EMAIL>",
			},
			TrailerType: "Cargo Van",
			Notes:       "this is a note",
			PONums:      "PRO 2080005",
			RefNumber:   "",
			CustomApptFieldsTemplate: []models.CustomApptField{
				{
					Name:                 "",
					Type:                 "",
					Label:                "",
					Value:                "",
					Description:          "",
					Placeholder:          "",
					DropDownValues:       []string{},
					HiddenFromCarrier:    false,
					RequiredForCarrier:   false,
					RequiredForCheckIn:   false,
					RequiredForWarehouse: false,
					MaxLengthOrValue:     0,
					MinLengthOrValue:     0,
				},
			},
		}

		err := validator.TestBody(body)
		if err == nil {
			t.Error("Expected error for missing LoadID, got none")
			return
		}

		var validationErr *apitesting.ValidationError
		if !errors.As(err, &validationErr) {
			t.Errorf("Expected ValidationError, got %T: %v", err, err)
			return
		}

		// Verify we got exactly one body error
		if validationErr.NumBodyErrors() != 1 {
			t.Errorf("Expected exactly 1 body validation error, got %d body errors: %v",
				validationErr.NumBodyErrors(), validationErr)
			return
		}

		// Verify it's the specific body error we expect
		if !validationErr.Contains("loadID is a required field") {
			t.Errorf("Expected body error about LoadID field, got: %v", validationErr)
		}
	})

	t.Run("missing required StartTime", func(t *testing.T) {
		body := ConfirmApptBody{
			LoadID:            1,
			FreightTrackingID: "12345",
			DryRun:            false,
			StopType:          PickupStopType,
			WarehouseID:       "warehouse-1",
			WarehouseTimezone: "America/New_York",
			DockID:            "dock-1",
			LoadTypeID:        "test-load",
			SubscribedEmail:   "<EMAIL>",
			CcEmails: []string{
				"<EMAIL>",
			},
			TrailerType: "Cargo Van",
			Notes:       "this is a note",
			PONums:      "PRO 2080005",
			RefNumber:   "",
			CustomApptFieldsTemplate: []models.CustomApptField{
				{
					Name:                 "",
					Type:                 "",
					Label:                "",
					Value:                "",
					Description:          "",
					Placeholder:          "",
					DropDownValues:       []string{},
					HiddenFromCarrier:    false,
					RequiredForCarrier:   false,
					RequiredForCheckIn:   false,
					RequiredForWarehouse: false,
					MaxLengthOrValue:     0,
					MinLengthOrValue:     0,
				},
			},
		}

		err := validator.TestBody(body)
		if err == nil {
			t.Error("Expected error for missing StartTime, got none")
			return
		}

		var validationErr *apitesting.ValidationError
		if !errors.As(err, &validationErr) {
			t.Errorf("Expected ValidationError, got %T: %v", err, err)
			return
		}

		// Verify we got exactly one body error
		if validationErr.NumBodyErrors() != 1 {
			t.Errorf("Expected exactly 1 body validation error, got %d body errors: %v",
				validationErr.NumBodyErrors(), validationErr)
			return
		}

		// Verify it's the specific body error we expect
		if !validationErr.Contains("start is a required field") {
			t.Errorf("Expected body error about StartTime field, got: %v", validationErr)
		}
	})

	t.Run("DockID required for non-yardview source", func(t *testing.T) {
		body := ConfirmApptBody{
			LoadID:            1,
			FreightTrackingID: "12345",
			Source:            "opendock", // Non-yardview source
			DryRun:            false,
			StopType:          PickupStopType,
			StartTime:         time.Now(),
			WarehouseID:       "warehouse-1",
			WarehouseTimezone: "America/New_York",
			// DockID is intentionally omitted to test it's required for non-yardview
			LoadTypeID:      "test-load",
			SubscribedEmail: "<EMAIL>",
			CcEmails: []string{
				"<EMAIL>",
			},
			TrailerType: "Cargo Van",
			Notes:       "this is a note",
			PONums:      "PRO 2080005",
			RefNumber:   "",
			CustomApptFieldsTemplate: []models.CustomApptField{
				{
					Name:                 "",
					Type:                 "",
					Label:                "",
					Value:                "",
					Description:          "",
					Placeholder:          "",
					DropDownValues:       []string{},
					HiddenFromCarrier:    false,
					RequiredForCarrier:   false,
					RequiredForCheckIn:   false,
					RequiredForWarehouse: false,
					MaxLengthOrValue:     0,
					MinLengthOrValue:     0,
				},
			},
		}

		err := validator.TestBody(body)
		if err == nil {
			t.Error("Expected error for missing DockID with non-yardview source, got none")
			return
		}

		var validationErr *apitesting.ValidationError
		if !errors.As(err, &validationErr) {
			t.Errorf("Expected ValidationError, got %T: %v", err, err)
			return
		}

		// Verify we got exactly one body error
		if validationErr.NumBodyErrors() != 1 {
			t.Errorf("Expected exactly 1 body validation error, got %d body errors: %v",
				validationErr.NumBodyErrors(), validationErr)
			return
		}

		// Verify it's the specific body error we expect
		if !validationErr.Contains("dockId is a required field") {
			t.Errorf("Expected body error about DockID field, got: %v", validationErr)
		}
	})

	t.Run("DockID optional for yardview source", func(t *testing.T) {
		body := ConfirmApptBody{
			LoadID:            1,
			FreightTrackingID: "12345",
			Source:            "yardview", // YardView source
			DryRun:            false,
			StopType:          PickupStopType,
			StartTime:         time.Now(),
			WarehouseID:       "warehouse-1",
			WarehouseTimezone: "America/New_York",
			// DockID is intentionally omitted to test it's optional for yardview
			LoadTypeID:      "test-load",
			SubscribedEmail: "<EMAIL>",
			CcEmails: []string{
				"<EMAIL>",
			},
			TrailerType: "Cargo Van",
			Notes:       "this is a note",
			PONums:      "PRO 2080005",
			RefNumber:   "",
			CustomApptFieldsTemplate: []models.CustomApptField{
				{
					Name:                 "",
					Type:                 "",
					Label:                "",
					Value:                "",
					Description:          "",
					Placeholder:          "",
					DropDownValues:       []string{},
					HiddenFromCarrier:    false,
					RequiredForCarrier:   false,
					RequiredForCheckIn:   false,
					RequiredForWarehouse: false,
					MaxLengthOrValue:     0,
					MinLengthOrValue:     0,
				},
			},
		}

		err := validator.TestBody(body)
		if err != nil {
			t.Errorf("Expected no error for missing DockID with yardview source, got: %v", err)
		}
	})

	t.Run("missing required LoadTypeID", func(t *testing.T) {
		body := ConfirmApptBody{
			LoadID:            1,
			FreightTrackingID: "12345",
			DryRun:            false,
			StopType:          PickupStopType,
			StartTime:         time.Now(),
			WarehouseID:       "warehouse-1",
			WarehouseTimezone: "America/New_York",
			DockID:            "dock-1",
			SubscribedEmail:   "<EMAIL>",
			CcEmails: []string{
				"<EMAIL>",
			},
			TrailerType: "Cargo Van",
			Notes:       "this is a note",
			PONums:      "PRO 2080005",
			RefNumber:   "",
			CustomApptFieldsTemplate: []models.CustomApptField{
				{
					Name:                 "",
					Type:                 "",
					Label:                "",
					Value:                "",
					Description:          "",
					Placeholder:          "",
					DropDownValues:       []string{},
					HiddenFromCarrier:    false,
					RequiredForCarrier:   false,
					RequiredForCheckIn:   false,
					RequiredForWarehouse: false,
					MaxLengthOrValue:     0,
					MinLengthOrValue:     0,
				},
			},
		}

		err := validator.TestBody(body)
		if err == nil {
			t.Error("Expected error for missing LoadTypeID, got none")
			return
		}

		var validationErr *apitesting.ValidationError
		if !errors.As(err, &validationErr) {
			t.Errorf("Expected ValidationError, got %T: %v", err, err)
			return
		}

		// Verify we got exactly one body error
		if validationErr.NumBodyErrors() != 1 {
			t.Errorf("Expected exactly 1 body validation error, got %d body errors: %v",
				validationErr.NumBodyErrors(), validationErr)
			return
		}

		// Verify it's the specific body error we expect
		if !validationErr.Contains("loadTypeId is a required field") {
			t.Errorf("Expected body error about LoadTypeID field, got: %v", validationErr)
		}
	})
}

func TestFormatOpendockMultiselect(t *testing.T) {
	// Test that dropdownmultiselect fields are correctly converted from strings to arrays
	// to meet OpenDock API requirements. This prevents the "invalid value" error when
	// creating appointments with multiselect dropdown fields.
	t.Run("dropdownmultiselect string to array conversion", func(t *testing.T) {
		fields := models.CustomApptFieldsTemplate{
			{
				Name:  "regularDropdown",
				Type:  "dropdown",
				Label: "Regular Dropdown",
				Value: "Some Value",
			},
			{
				Name:  "dropdownmultiselect",
				Type:  "dropdownmultiselect",
				Label: "Dropdown Multiselect",
				Value: "Some Value",
			},
			{
				Name:  "emptyDropdownmultiselect",
				Type:  "dropdownmultiselect",
				Label: "Empty Multi",
				Value: "",
			},
			{
				Name:  "nilDropdownmultiselect",
				Type:  "dropdownmultiselect",
				Label: "Nil Multi",
				Value: nil,
			},
		}

		processed := formatOpendockMultiselect(fields)

		// Check that regular dropdown values remain unchanged
		assert.Equal(t, "Some Value", processed[0].Value)

		// Check that dropdownmultiselect values are converted to arrays
		assert.Equal(t, []string{"Some Value"}, processed[1].Value)

		// Check that empty string becomes empty array
		assert.Equal(t, []string{}, processed[2].Value)

		// Check that nil becomes empty array
		assert.Equal(t, []string{}, processed[3].Value)
	})

	// Test that non-dropdownmultiselect fields are left unchanged
	t.Run("non-dropdownmultiselect fields unchanged", func(t *testing.T) {
		fields := models.CustomApptFieldsTemplate{
			{
				Name:  "textField",
				Type:  "str",
				Label: "Text Field",
				Value: "Some text",
			},
			{
				Name:  "numberField",
				Type:  "int",
				Label: "Number Field",
				Value: 42,
			},
		}

		processed := formatOpendockMultiselect(fields)

		// Check that non-dropdownmultiselect fields are unchanged
		assert.Equal(t, "Some text", processed[0].Value)
		assert.Equal(t, 42, processed[1].Value)
	})
}

func TestTimeEqual(t *testing.T) {
	baseTime := time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC)

	tests := []struct {
		name     string
		t1       time.Time
		t2       time.Time
		expected bool
	}{
		{
			name:     "exact same time",
			t1:       baseTime,
			t2:       baseTime,
			expected: true,
		},
		{
			name:     "within 30 seconds",
			t1:       baseTime,
			t2:       baseTime.Add(30 * time.Second),
			expected: true,
		},
		{
			name:     "within 1 minute",
			t1:       baseTime,
			t2:       baseTime.Add(60 * time.Second),
			expected: true,
		},
		{
			name:     "over 1 minute difference",
			t1:       baseTime,
			t2:       baseTime.Add(90 * time.Second),
			expected: false,
		},
		{
			name:     "negative difference within tolerance",
			t1:       baseTime.Add(30 * time.Second),
			t2:       baseTime,
			expected: true,
		},
		{
			name:     "negative difference over tolerance",
			t1:       baseTime.Add(90 * time.Second),
			t2:       baseTime,
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := timeEqual(tt.t1, tt.t2)
			if result != tt.expected {
				t.Errorf("timeEqual(%v, %v) = %v, expected %v", tt.t1, tt.t2, result, tt.expected)
			}
		})
	}
}

func TestLoadToCustomWarehouse(t *testing.T) {
	load := &models.Load{
		LoadCoreInfo: models.LoadCoreInfo{
			Pickup: models.Pickup{
				CompanyCoreInfo: models.CompanyCoreInfo{
					Name:         "Pickup Facility",
					AddressLine1: "123 Main St",
					City:         "Austin",
					State:        "TX",
					Zipcode:      "73301",
				},
				Timezone: "America/Chicago",
			},
			Consignee: models.Consignee{
				CompanyCoreInfo: models.CompanyCoreInfo{
					Name:         "Dropoff Facility",
					AddressLine1: "456 Elm Ave",
					City:         "Dallas",
					State:        "TX",
					Zipcode:      "75001",
				},
				Timezone: "America/Chicago",
			},
		},
	}

	// Pickup case
	whPickup := loadToCustomWarehouse(load, PickupStopType, models.CostcoSource)
	assert.NotEmpty(t, whPickup.WarehouseID)
	assert.Equal(t, "Pickup Facility", whPickup.WarehouseName)
	assert.Equal(t, "123 Main St", whPickup.WarehouseAddressLine1)
	assert.Equal(t, "Austin, TX 73301", whPickup.WarehouseAddressLine2)
	assert.Equal(t, "123 Main St Austin, TX 73301", whPickup.WarehouseFullAddress)
	assert.Equal(t, "Pickup Facility 123 Main St Austin, TX 73301", whPickup.WarehouseFullIdentifier)
	assert.Equal(t, "America/Chicago", whPickup.WarehouseTimezone)
	assert.Equal(t, models.CostcoSource, whPickup.Source)

	// Dropoff case
	whDrop := loadToCustomWarehouse(load, DropoffStopType, models.CostcoSource)
	assert.NotEmpty(t, whDrop.WarehouseID)
	assert.Equal(t, "Dropoff Facility", whDrop.WarehouseName)
	assert.Equal(t, "456 Elm Ave", whDrop.WarehouseAddressLine1)
	assert.Equal(t, "Dallas, TX 75001", whDrop.WarehouseAddressLine2)
	assert.Equal(t, "456 Elm Ave Dallas, TX 75001", whDrop.WarehouseFullAddress)
	assert.Equal(t, "Dropoff Facility 456 Elm Ave Dallas, TX 75001", whDrop.WarehouseFullIdentifier)
	assert.Equal(t, "America/Chicago", whDrop.WarehouseTimezone)
	assert.Equal(t, models.CostcoSource, whDrop.Source)
}

func TestLoadToCustomWarehouse_DeterministicID(t *testing.T) {
	// Same physical address with different casing should yield the same SHA-1 ID per source
	load1 := &models.Load{
		LoadCoreInfo: models.LoadCoreInfo{
			Pickup: models.Pickup{
				CompanyCoreInfo: models.CompanyCoreInfo{
					Name:         "Pickup Facility",
					AddressLine1: "123 Main St",
					City:         "Austin",
					State:        "TX",
					Zipcode:      "73301",
				},
				Timezone: "America/Chicago",
			},
		},
	}

	load2 := &models.Load{
		LoadCoreInfo: models.LoadCoreInfo{
			Pickup: models.Pickup{
				CompanyCoreInfo: models.CompanyCoreInfo{
					Name:         "Pickup Facility",
					AddressLine1: "123 MAIN ST", // different casing
					City:         "AUSTIN",
					State:        "TX",
					Zipcode:      "73301",
				},
				Timezone: "America/Chicago",
			},
		},
	}

	wh1 := loadToCustomWarehouse(load1, PickupStopType, models.CostcoSource)
	wh2 := loadToCustomWarehouse(load2, PickupStopType, models.CostcoSource)

	assert.NotEmpty(t, wh1.WarehouseID)
	assert.Equal(t, wh1.WarehouseID, wh2.WarehouseID, "expected deterministic ID for same address and source")
}

func TestLoadToCustomWarehouse_DifferentSourceDifferentID(t *testing.T) {
	load := &models.Load{
		LoadCoreInfo: models.LoadCoreInfo{
			Pickup: models.Pickup{
				CompanyCoreInfo: models.CompanyCoreInfo{
					Name:         "Pickup Facility",
					AddressLine1: "123 Main St",
					City:         "Austin",
					State:        "TX",
					Zipcode:      "73301",
				},
				Timezone: "America/Chicago",
			},
		},
	}

	whCostco := loadToCustomWarehouse(load, PickupStopType, models.CostcoSource)
	whE2Open := loadToCustomWarehouse(load, PickupStopType, models.E2openSource)

	assert.NotEqual(
		t,
		whCostco.WarehouseID,
		whE2Open.WarehouseID,
		"different sources must produce different IDs for same address",
	)
}

// Run locally with:
// 1. export TEST_LIVE_DB=true
// 2. go test -v ./fn/api/routes/appt -run TestCreateCustomWarehouseFromLoad_Dedup_LiveDB
func TestCreateCustomWarehouseFromLoad_Dedup_LiveDB(t *testing.T) {
	if os.Getenv("TEST_LIVE_DB") != "true" {
		t.Skip("skipping live DB test; enable with TEST_LIVE_DB=true")
		return
	}

	ctx := rds.SetupTestDB(t)
	service := rds.CreateTestService(ctx, t)

	// Allow access to the warehouse source by creating a scheduling integration with matching name
	_ = rds.CreateTestIntegration(
		ctx,
		t,
		models.Integration{
			Name:      models.Costco,
			Type:      models.Scheduling,
			ServiceID: service.ID,
		},
	)

	load := &models.Load{
		LoadCoreInfo: models.LoadCoreInfo{
			Pickup: models.Pickup{
				CompanyCoreInfo: models.CompanyCoreInfo{
					Name:         "Pickup Facility",
					AddressLine1: "123 Main St",
					City:         "Austin",
					State:        "TX",
					Zipcode:      "73301",
				},
				Timezone: "America/Chicago",
			},
		},
	}

	wh1, err := createCustomWarehouseFromLoad(ctx, load, PickupStopType, string(models.CostcoSource))
	require.NoError(t, err)
	require.NotNil(t, wh1)

	wh2, err := createCustomWarehouseFromLoad(ctx, load, PickupStopType, string(models.CostcoSource))
	require.NoError(t, err)
	require.NotNil(t, wh2)

	assert.Equal(t, wh1.WarehouseID, wh2.WarehouseID, "Upsert should deduplicate by (warehouse_id, source)")

	// Verify persisted values via query interface
	queried, err := warehouseDB.GetWarehouseByIDAndSource(
		ctx,
		service.ID,
		models.CostcoSource,
		wh1.WarehouseID,
	)
	require.NoError(t, err)
	require.NotNil(t, queried)
}

func TestCreateCustomWarehouseFromLoad_Errors(t *testing.T) {
	ctx := context.Background()
	load := &models.Load{
		LoadCoreInfo: models.LoadCoreInfo{
			Pickup: models.Pickup{
				CompanyCoreInfo: models.CompanyCoreInfo{
					Name:         "Pickup Facility",
					AddressLine1: "123 Main St",
					City:         "Austin",
					State:        "TX",
					Zipcode:      "73301",
				},
				Timezone: "America/Chicago",
			},
		},
	}

	t.Run("yardview requires pre-created custom warehouse", func(t *testing.T) {
		wh, err := createCustomWarehouseFromLoad(ctx, load, PickupStopType, string(models.YardViewSource))
		assert.Nil(t, wh)
		if assert.Error(t, err) {
			assert.True(t, strings.Contains(err.Error(), "failed to determine warehouse support type"))
		}
	})

	t.Run("native source does not support custom warehouses", func(t *testing.T) {
		wh, err := createCustomWarehouseFromLoad(ctx, load, PickupStopType, string(models.OpendockSource))
		assert.Nil(t, wh)
		if assert.Error(t, err) {
			assert.True(t, strings.Contains(err.Error(), "does not support custom warehouses"))
		}
	})
}

// Run locally with:
// 1. export TEST_LIVE_DB=true
// 2. go test ./fn/api/routes/appt -run TestCreateCustomWarehouseFromLoad_Success_LiveDB
func TestCreateCustomWarehouseFromLoad_Success_LiveDB(t *testing.T) {
	if os.Getenv("TEST_LIVE_DB") != "true" {
		t.Skip("skipping live DB test; enable with TEST_LIVE_DB=true")
		return
	}

	ctx := rds.SetupTestDB(t)
	service := rds.CreateTestService(ctx, t)

	// Allow access to the warehouse source by creating a scheduling integration with matching name
	_ = rds.CreateTestIntegration(
		ctx,
		t,
		models.Integration{
			Name:      models.Costco,
			Type:      models.Scheduling,
			ServiceID: service.ID,
		},
	)

	load := &models.Load{
		LoadCoreInfo: models.LoadCoreInfo{
			Pickup: models.Pickup{
				CompanyCoreInfo: models.CompanyCoreInfo{
					Name:         "Pickup Facility",
					AddressLine1: "123 Main St",
					City:         "Austin",
					State:        "TX",
					Zipcode:      "73301",
				},
				Timezone: "America/Chicago",
			},
		},
	}

	wh, err := createCustomWarehouseFromLoad(ctx, load, PickupStopType, string(models.CostcoSource))
	require.NoError(t, err)
	require.NotNil(t, wh)
	require.NotZero(t, wh.ID)
	require.NotEmpty(t, wh.WarehouseID)

	// Verify persisted values via query interface
	queried, err := warehouseDB.GetWarehouseByIDAndSource(
		ctx,
		service.ID,
		models.CostcoSource,
		wh.WarehouseID,
	)
	require.NoError(t, err)
	require.NotNil(t, queried)
	assert.Equal(t, wh.WarehouseID, queried.WarehouseID)
	assert.Equal(t, "Pickup Facility", queried.WarehouseName)
	assert.Equal(t, "123 Main St", queried.WarehouseAddressLine1)
	assert.Equal(t, "Austin, TX 73301", queried.WarehouseAddressLine2)
	assert.Equal(t, "123 Main St Austin, TX 73301", queried.WarehouseFullAddress)
	assert.Equal(t, string(models.CostcoSource), string(queried.Source))
}
