package appt

import (
	"errors"
	"testing"

	apitesting "github.com/drumkitai/drumkit/fn/api/testing"
)

func TestGetWarehousesBySearch(t *testing.T) {
	validator := apitesting.NewValidationTest(t)

	t.Run("valid query params", func(t *testing.T) {
		query := GetWarehousesBySearchQuery{
			Search: "555 Warehouse St",
		}

		if err := validator.TestQuery(query); err != nil {
			t.<PERSON>("Expected no error, got: %v", err)
		}
	})

	t.Run("missing required Search", func(t *testing.T) {
		query := GetWarehousesBySearchQuery{}

		err := validator.TestQuery(query)
		if err == nil {
			t.Error("Expected error for missing Search, got none")
			return
		}

		var validationErr *apitesting.ValidationError
		if !errors.As(err, &validationErr) {
			t.<PERSON><PERSON>("Expected ValidationError, got %T: %v", err, err)
			return
		}

		// Verify we got exactly one query error
		if validationErr.NumQueryErrors() != 1 {
			t.<PERSON>rrorf("Expected exactly 1 query validation error, got %d query errors: %v",
				validationErr.NumQueryErrors(), validationErr)
			return
		}

		// Verify it's the specific query error we expect
		if !validationErr.Contains("search is a required field") {
			t.Errorf("Expected query error about Search field, got: %v", validationErr)
		}
	})
}
