package appt

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strings"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/integrations/scheduling"
	"github.com/drumkitai/drumkit/common/integrations/scheduling/opendock"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/rds"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
)

type UploadFileResponse struct {
	Key string `json:"key"`
	URL string `json:"url"`
}

const (
	LambdaMaxFileSize = 6 * 1024 * 1024 // 6MB limit for Lambda
)

// NOTE: This upload file endpoint is only for Opendock's /storage api.
func UploadFileToOpendock(c *fiber.Ctx) error {
	email := middleware.ClaimsFromContext(c).Email
	userID := middleware.UserIDFromContext(c)

	ctx := log.With(
		c.UserContext(),
		zap.String("emailAddress", email),
		zap.Uint("userID", userID),
	)

	contentType := c.Get("Content-Type")
	if !strings.Contains(contentType, "multipart/form-data") {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"error": "Content-Type must be multipart/form-data",
		})
	}

	file, err := c.FormFile("file")
	if err != nil {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"error": "No file found in request",
		})
	}

	if file.Size > LambdaMaxFileSize {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"error": "File size exceeds 6MB limit",
		})
	}

	fileReader, err := file.Open()
	if err != nil {
		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to read uploaded file",
		})
	}
	defer fileReader.Close()

	fileContent, err := io.ReadAll(fileReader)
	if err != nil {
		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to process uploaded file",
		})
	}

	user, err := rds.GetUserByID(ctx, userID)
	if err != nil {
		log.Error(ctx, "GetUserByID failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	integration, err := integrationDB.GetSchedulerByServiceUserIDsAndName(ctx, userID, user.ServiceID, "opendock", 0)
	if err != nil {
		log.Error(ctx, "Failed to get Opendock integration", zap.Error(err))
		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to get Opendock integration",
		})
	}

	client, err := scheduling.GetCachedClient(ctx, integration)
	if err != nil {
		log.Error(ctx, "Failed to create Opendock client", zap.Error(err))
		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to create scheduling client",
		})
	}

	opendockClient, ok := client.(*opendock.Opendock)
	if !ok {
		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"error": "Invalid Opendock client",
		})
	}

	uploadResult, err := opendockClient.UploadDocument(ctx, file.Filename, fileContent)
	if err != nil {
		log.Error(ctx, "Failed to upload file to Opendock", zap.Error(err))

		var httpErr errtypes.HTTPResponseError
		if errors.As(err, &httpErr) {

			var opendockError struct {
				Message string `json:"message"`
			}
			if json.Unmarshal(httpErr.ResponseBody, &opendockError) == nil && opendockError.Message != "" {
				return c.Status(httpErr.StatusCode).JSON(fiber.Map{
					"error": fmt.Sprintf("Opendock error: %s", opendockError.Message),
				})
			}

			return c.Status(httpErr.StatusCode).JSON(fiber.Map{
				"error": fmt.Sprintf("Opendock error: %s", string(httpErr.ResponseBody)),
			})
		}

		// Fallback for other errors
		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to upload file to Opendock storage",
		})
	}

	log.Info(
		ctx,
		"File uploaded successfully",
		zap.String("fileName", file.Filename),
		zap.String("opendockKey", uploadResult.Key),
		zap.Int64("fileSize", file.Size),
	)

	return c.JSON(UploadFileResponse{
		Key: uploadResult.Key,
		URL: uploadResult.URL,
	})
}
