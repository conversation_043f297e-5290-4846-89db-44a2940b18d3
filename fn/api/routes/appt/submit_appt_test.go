package appt

import (
	"errors"
	"testing"

	apitesting "github.com/drumkitai/drumkit/fn/api/testing"
)

func TestSubmitAppt(t *testing.T) {
	validator := apitesting.NewValidationTest(t)

	t.Run("valid request body", func(t *testing.T) {
		body := SubmitApptBody{
			PONumbers: []string{
				"PRO 208005",
			},
			Source:          "opendock",
			WarehouseID:     "2",
			LumperRequested: false,
			Note:            "this is a note",
		}

		if err := validator.TestBody(body); err != nil {
			t.<PERSON>rrorf("Expected no error, got: %v", err)
		}
	})

	t.Run("missing required PONumbers", func(t *testing.T) {
		body := SubmitApptBody{
			Source:          "opendock",
			WarehouseID:     "2",
			LumperRequested: false,
			Note:            "this is a note",
		}

		err := validator.TestBody(body)
		if err == nil {
			t.Error("Expected error for missing PONumbers, got none")
			return
		}

		var validationErr *apitesting.ValidationError
		if !errors.As(err, &validationErr) {
			t.<PERSON>("Expected ValidationError, got %T: %v", err, err)
			return
		}

		// Verify we got exactly one body error
		if validationErr.NumBodyErrors() != 1 {
			t.Errorf("Expected exactly 1 body validation error, got %d body errors: %v",
				validationErr.NumBodyErrors(), validationErr)
			return
		}

		// Verify it's the specific body error we expect
		if !validationErr.Contains("poNumbers is a required field") {
			t.Errorf("Expected body error about PONumbers field, got: %v", validationErr)
		}
	})

	t.Run("missing required Source", func(t *testing.T) {
		body := SubmitApptBody{
			PONumbers: []string{
				"PRO 208005",
			},
			WarehouseID:     "2",
			LumperRequested: false,
			Note:            "this is a note",
		}

		err := validator.TestBody(body)
		if err == nil {
			t.Error("Expected error for missing Source, got none")
			return
		}

		var validationErr *apitesting.ValidationError
		if !errors.As(err, &validationErr) {
			t.Errorf("Expected ValidationError, got %T: %v", err, err)
			return
		}

		// Verify we got exactly one body error
		if validationErr.NumBodyErrors() != 1 {
			t.Errorf("Expected exactly 1 body validation error, got %d body errors: %v",
				validationErr.NumBodyErrors(), validationErr)
			return
		}

		// Verify it's the specific body error we expect
		if !validationErr.Contains("source is a required field") {
			t.Errorf("Expected body error about Source field, got: %v", validationErr)
		}
	})

	t.Run("missing required WarehouseID", func(t *testing.T) {
		body := SubmitApptBody{
			PONumbers: []string{
				"PRO 208005",
			},
			Source:          "opendock",
			LumperRequested: false,
			Note:            "this is a note",
		}

		err := validator.TestBody(body)
		if err == nil {
			t.Error("Expected error for missing WarehouseID, got none")
			return
		}

		var validationErr *apitesting.ValidationError
		if !errors.As(err, &validationErr) {
			t.Errorf("Expected ValidationError, got %T: %v", err, err)
			return
		}

		// Verify we got exactly one body error
		if validationErr.NumBodyErrors() != 1 {
			t.Errorf("Expected exactly 1 body validation error, got %d body errors: %v",
				validationErr.NumBodyErrors(), validationErr)
			return
		}

		// Verify it's the specific body error we expect
		if !validationErr.Contains("warehouseId is a required field") {
			t.Errorf("Expected body error about WarehouseID field, got: %v", validationErr)
		}
	})
}
