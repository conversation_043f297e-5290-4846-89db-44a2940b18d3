package appt

import (
	"context"
	"errors"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"github.com/lib/pq"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	"github.com/drumkitai/drumkit/common/rds/emailtemplates"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	userGroupsDB "github.com/drumkitai/drumkit/common/rds/usergroups"
	apiutil "github.com/drumkitai/drumkit/fn/api/util"
)

type (
	TemplatePath struct {
		LoadID uint `params:"loadID" validate:"required"`
	}

	TemplateQuery struct {
		StopType models.StopType `query:"stopType"`
	}

	PreparedTemplate struct {
		TemplateType models.EmailTemplateType `json:"templateType"`
		CC           pq.StringArray           `json:"cc"`
		Subject      string                   `json:"subject"`
		Body         string                   `json:"body"`
		IsGeneric    bool                     `json:"isGeneric"`
		ID           uint                     `json:"id"`
	}

	TemplateResponse struct {
		ApptRequestTemplate     PreparedTemplate `json:"appointmentRequestTemplate"`
		ApptRequestTemplateHTML PreparedTemplate `json:"appointmentRequestTemplateHTML"`
	}
)

func GetApptTemplate(c *fiber.Ctx) error {
	var path TemplatePath
	var query TemplateQuery
	if err := api.Parse(c, &path, &query, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	userServiceID := middleware.ServiceIDFromContext(c)
	ctx := c.UserContext()

	user, err := userDB.GetByEmail(ctx, middleware.ClaimsFromContext(c).Email)
	if err != nil {
		return apiutil.HandleDBError(c, err, false, "user %s not found", middleware.ClaimsFromContext(c).Email)
	}

	service, err := rds.GetServiceByID(ctx, userServiceID)
	if err != nil {
		log.Error(ctx, "could not get service", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	ctx = log.With(ctx,
		zap.Uint("userID", user.ID),
		zap.Uint("serviceID", service.ID),
		zap.String("serviceName", service.Name),
		zap.String("emailAddress", user.EmailAddress),
		zap.String("emailProvider", string(user.EmailProvider)),
		zap.Uint("loadID", path.LoadID),
	)

	apptRequestTemplate, apptRequestTemplateHTML := getApptRequestTemplate(
		ctx,
		user,
		service,
		path.LoadID,
		query.StopType,
	)

	return c.Status(http.StatusOK).JSON(
		TemplateResponse{
			ApptRequestTemplate:     apptRequestTemplate, // TODO: Remove in favor of ApptRequestTemplateHTML
			ApptRequestTemplateHTML: apptRequestTemplateHTML,
		},
	)
}

func getApptRequestTemplate(
	ctx context.Context,
	user models.User,
	service models.Service,
	loadID uint,
	stopType models.StopType,
) (PreparedTemplate, PreparedTemplate) {

	ctx, metaSpan := otel.StartSpan(ctx, "getApptRequestTemplate", nil)
	defer func() { metaSpan.End(nil) }()

	userGroupIDs, err := userGroupsDB.GetAllGroupIDsByUserID(ctx, user.ID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		// Fail open, which just skips checking for user group templates
		log.Error(ctx, "error while getting user group", zap.Error(err))
	}

	templates, err := emailtemplates.GetEmailTemplateByTypeAndIDs(
		ctx,
		user.ID,
		userGroupIDs,
		service.ID,
		[]models.EmailTemplateType{
			models.AppointmentSchedulingRequest,
			models.AppointmentSchedulingRequestHTML,
		},
	)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		// Fail open, will use generic template
		log.Error(ctx, "failed to get email templates", zap.Error(err))
	}

	load, err := loadDB.GetLoadByID(ctx, loadID)
	if err != nil {
		// Log error but fail-open, as we still want to return the default template.
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Info(ctx, "no load found, using default", zap.Error(err))
		} else {
			log.Error(ctx, "failed to get load", zap.Error(err))
		}
	}

	getTemplateOrGeneric := func(templateType models.EmailTemplateType) PreparedTemplate {
		var opts []models.EmailTemplateOption

		for _, t := range templates {
			if t.TemplateType == templateType {
				opts = append(opts, models.WithTemplate(t))
				opts = append(opts, models.WithLoad(load))

				// If stopType is provided, pass it to the template for custom logic
				if stopType != "" {
					opts = append(opts, models.WithStopType(stopType))
				}

				return prepareTemplate(ctx, user, templateType, t, opts...)
			}
		}

		genericTemplate := models.GenericEmailTemplates[templateType]
		return prepareTemplate(ctx, user, templateType, genericTemplate, models.WithLoad(load))
	}

	return getTemplateOrGeneric(models.AppointmentSchedulingRequest),
		getTemplateOrGeneric(models.AppointmentSchedulingRequestHTML)
}

func prepareTemplate(
	ctx context.Context,
	user models.User,
	tmplType models.EmailTemplateType,
	tmpl models.EmailTemplate,
	opts ...models.EmailTemplateOption,
) PreparedTemplate {

	subject, body, err := models.PrepareEmailTemplate(tmplType, opts...)
	if err != nil {
		log.Error(ctx, "failed to prepare email template", zap.Error(err))
		return PreparedTemplate{
			TemplateType: tmplType,
			Subject:      tmpl.Subject,
			Body:         tmpl.Body,
			IsGeneric:    true,
			ID:           tmpl.ID,
		}
	}

	cc := pq.StringArray{""}
	if len(user.CC) > 0 {
		cc = user.CC
	}

	return PreparedTemplate{
		TemplateType: tmplType,
		CC:           cc,
		Subject:      subject,
		Body:         body,
		IsGeneric:    len(opts) == 0,
		ID:           tmpl.ID,
	}
}
