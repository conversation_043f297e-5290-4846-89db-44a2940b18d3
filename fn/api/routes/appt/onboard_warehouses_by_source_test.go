package appt

import (
	"testing"

	apitesting "github.com/drumkitai/drumkit/fn/api/testing"
)

func TestOnboardWarehousesBySource(t *testing.T) {
	validator := apitesting.NewValidationTest(t)

	t.Run("valid request body", func(t *testing.T) {
		body := OnboardWarehousesBySourceBody{
			Source: "opendock",
		}

		if err := validator.TestBody(body); err != nil {
			t.<PERSON><PERSON><PERSON>("Expected no error, got: %v", err)
		}
	})
}
