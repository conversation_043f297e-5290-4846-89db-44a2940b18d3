package appt

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers/perms"
	"github.com/drumkitai/drumkit/common/integrations/scheduling"
	"github.com/drumkitai/drumkit/common/integrations/tms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	apptDB "github.com/drumkitai/drumkit/common/rds/appt"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
	tmsCustomerSchedulingIntegrationDB "github.com/drumkitai/drumkit/common/rds/tms_customer_scheduling_integration"
	tmsCustomerDB "github.com/drumkitai/drumkit/common/rds/tms_customers"
	warehouseDB "github.com/drumkitai/drumkit/common/rds/warehouses"
	"github.com/drumkitai/drumkit/common/sentry"
)

type (
	ConfirmApptBody struct {
		LoadID            uint   `json:"loadID" validate:"required"`
		FreightTrackingID string `json:"freightTrackingId" validate:"required"`
		Source            string `json:"source"` // TODO: limit type to scheduling integrations
		IntegrationID     uint   `json:"integrationID"`
		// If DryRun=true, the appt is created in the DB but not submitted to Scheduler or TMS
		// Useful for testing appointment creation without spamming Scheduler with appointment reservations
		DryRun bool `json:"dryRun"`

		// NOTE: Opendock specific (/storage api): Pre-uploaded file keys from /appt/upload-file endpoint
		UploadedFileKeys []string `json:"uploadedFileKeys,omitempty"`

		StopType          StopType  `json:"stopType" validate:"required,oneof=pickup dropoff"`
		StartTime         time.Time `json:"start" validate:"required"`
		WarehouseID       string    `json:"warehouseID" validate:"required"`
		WarehouseTimezone string    `json:"warehouseTimezone" validate:"required"` // IANA, e.g. America/New_York
		// Yardview doesn't require DockID to make an appointment
		DockID          string   `json:"dockId" validate:"required_unless=Source yardview"`
		LoadTypeID      string   `json:"loadTypeId" validate:"required"`
		SubscribedEmail string   `json:"subscribedEmail"`
		CcEmails        []string `json:"ccEmails"`
		Operation       string   `json:"operation,omitempty"`
		Company         string   `json:"company,omitempty"`
		TrailerType     string   `json:"trailerType,omitempty"`
		Notes           string   `json:"notes"`
		PONums          string   `json:"poNums"`
		// Opendock-specific reference number, required by some warehouses
		RefNumber string `json:"refNumber"`
		// Contact information
		Phone string `json:"phone"`
		Email string `json:"email"`
		models.CustomApptFieldsTemplate
		// E2open-specific request type
		RequestType     models.RequestType `json:"requestType"`
		AppointmentType string             `json:"appointmentType"`
		AppointmentDate string             `json:"appointmentDate,omitempty"`
		ProIDFieldName  string             `json:"proIdFieldName,omitempty"`
		// For bulk appointment support
		Appointments []models.AppointmentData `json:"appointments,omitempty"`
		// Additional reservation form data for integrations like C3Reservations
		FormData        map[string]any `json:"formData,omitempty"`
		ReservationType string         `json:"reservationType,omitempty"`
		Tenant          string         `json:"tenant,omitempty"`

		// YardView-specific fields
		ApptKey     string `json:"apptKey,omitempty"`
		CarrierSCAC string `json:"carrierScac,omitempty"`
		SchedulePID string `json:"schedulePid,omitempty"`
		TrailerID   string `json:"trailerId,omitempty"`
		Weight      string `json:"weight,omitempty"`
		// End of YardView-specific fields

		FlowType string `json:"flowType,omitempty"`

		// Temp field for backwards compatibility. FE callers of this route will make a diff API call to update TMS.
		SkipTMSUpdate bool `json:"skipTMSUpdate,omitempty"` // TODO: Remove after users are on/past Vulcan v0.50.1
	}

	ConfirmApptResponse struct {
		models.Appointment
		IsAppointmentTMSUpdateEnabled bool `json:"isAppointmentTMSUpdateEnabled"`
		TMSUpdateSucceeded            bool `json:"tmsUpdateSucceeded"`
		// User-facing message, typically used if creating the Scheduler appt succeeded but not the TMS update
		Message string `json:"message"`
		URL     string `json:"url"`
	}
)

// appointmentHandler defines the interface for handling different scheduling integrations.
type appointmentHandler func(context.Context, *fiber.Ctx, models.User, *ConfirmApptBody) error

// handlers maps scheduling integration names to their handlers.
var handlers = map[string]appointmentHandler{
	string(models.OneNetwork):     handleOneNetwork,
	string(models.Opendock):       handleOpendock,
	string(models.Retalix):        handleRetalix,
	string(models.E2open):         handleE2open,
	string(models.Manhattan):      handleManhattan,
	string(models.Costco):         handleCostco,
	string(models.C3Reservations): handleC3Reservations,
	string(models.YardView):       handleYardView,
}

// ConfirmAppt handles live appointment confirmation for various scheduling integrations.
func ConfirmAppt(c *fiber.Ctx) error {
	var body ConfirmApptBody
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}
	// Default to Opendock if source is empty.
	if body.Source == "" {
		body.Source = string(models.Opendock)
	}

	email := middleware.ClaimsFromContext(c).Email
	userID := middleware.UserIDFromContext(c)

	ctx := log.With(
		c.UserContext(),
		zap.String("integration", body.Source),
		zap.Any("requestBody", abridgeRequestBody(body)),
	)

	user, err := rds.GetUserByID(ctx, userID)
	if err != nil {
		log.Error(ctx, "GetUserByID failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	body.CcEmails = append(body.CcEmails, body.SubscribedEmail)

	// Internal emails are not allowed to schedule real PROs.
	if perms.IsInternalEmail(ctx, email) && body.Source == string(models.Opendock) && !body.DryRun {
		log.Error(ctx, "Internal email not allowed to schedule production load")

		return c.Status(http.StatusForbidden).JSON(&ConfirmApptResponse{
			Message: "Not allowed to schedule production load",
		})
	}

	handler, ok := handlers[body.Source]
	if !ok {
		log.Warn(ctx, "unsupported scheduling integration")

		return c.Status(http.StatusBadRequest).JSON(&ConfirmApptResponse{
			Message: "unsupported scheduling integration",
		})
	}

	return handler(ctx, c, user, &body)
}

// handleOpendock processes Opendock appointments with TMS updates.
func handleOpendock(ctx context.Context, c *fiber.Ctx, user models.User, body *ConfirmApptBody) error {
	tmsIntegration, err := getTMSIntegration(ctx, user.ServiceID, body.FreightTrackingID)
	if err != nil {
		log.Error(ctx, "Failed to get TMS integration", zap.Error(err))

		return c.Status(http.StatusInternalServerError).JSON(&ConfirmApptResponse{
			Message: "Failed to get TMS integration",
		})
	}

	warehouse, err := warehouseDB.GetWarehouseByIDAndSource(
		ctx,
		user.ServiceID,
		models.OpendockSource,
		body.WarehouseID,
	)
	if err != nil {
		log.Error(ctx, "failed to get opendock warehouse", zap.Error(err))

		return c.SendStatus(http.StatusInternalServerError)
	}

	tmsClient, err := tms.New(ctx, *tmsIntegration)
	if err != nil {
		log.Error(ctx, "error creating TMS client", zap.Error(err))

		return c.SendStatus(http.StatusInternalServerError)
	}

	return createAndUpdate(ctx, c, user, body, tmsIntegration, tmsClient, warehouse)
}

// handleRetalix processes Retalix appointments without TMS updates.
func handleRetalix(ctx context.Context, c *fiber.Ctx, user models.User, body *ConfirmApptBody) error {
	warehouse, err := warehouseDB.GetWarehouseByIDAndSource(
		ctx,
		user.ServiceID,
		models.RetalixSource,
		body.WarehouseID,
	)
	if err != nil {
		log.Error(ctx, "failed to get retalix warehouse", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return createAndUpdate(ctx, c, user, body, nil, nil, warehouse)
}

// handleE2open processes E2open appointments.
func handleE2open(ctx context.Context, c *fiber.Ctx, user models.User, body *ConfirmApptBody) error {
	return createAndUpdate(ctx, c, user, body, nil, nil, nil)
}

// handleOneNetwork processes OneNetwork appointments.
func handleOneNetwork(ctx context.Context, c *fiber.Ctx, user models.User, body *ConfirmApptBody) error {
	return createAndUpdate(ctx, c, user, body, nil, nil, nil)
}

// handleManhattan processes Manhattan appointments.
func handleManhattan(ctx context.Context, c *fiber.Ctx, user models.User, body *ConfirmApptBody) error {
	return createAndUpdate(ctx, c, user, body, nil, nil, nil)
}

// handleCostco processes Costco appointments.
func handleCostco(ctx context.Context, c *fiber.Ctx, user models.User, body *ConfirmApptBody) error {
	return createAndUpdate(ctx, c, user, body, nil, nil, nil)
}

// handleC3Reservations processes C3Reservations appointments.
func handleC3Reservations(ctx context.Context, c *fiber.Ctx, user models.User, body *ConfirmApptBody) error {
	return createAndUpdate(ctx, c, user, body, nil, nil, nil)
}

// handleYardView processes YardView appointments.
func handleYardView(ctx context.Context, c *fiber.Ctx, user models.User, body *ConfirmApptBody) error {
	warehouse, err := warehouseDB.GetWarehouseByIDAndSource(
		ctx,
		user.ServiceID,
		models.YardViewSource,
		body.WarehouseID,
	)
	if err != nil || warehouse == nil {
		log.Error(ctx, "failed to get yardview warehouse", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	// Check slot capacity before attempting to create appointment
	if !body.DryRun {
		hasCapacity, err := checkYardViewSlotCapacity(ctx, user, body, warehouse)
		if err != nil {
			log.Error(ctx, "failed to check slot capacity", zap.Error(err))

			return c.Status(http.StatusInternalServerError).JSON(&ConfirmApptResponse{
				Message: "Failed to check slot capacity",
			})
		}
		if !hasCapacity {
			log.WarnNoSentry(
				ctx,
				"selected time slot has no available capacity",
				zap.Time("startTime", body.StartTime),
				zap.String("warehouseID", body.WarehouseID),
			)

			return c.Status(http.StatusConflict).JSON(&ConfirmApptResponse{
				Message: "The selected time slot is no longer available. Please select a different time slot.",
			})
		}
	}

	tmsIntegration, err := getTMSIntegration(ctx, user.ServiceID, body.FreightTrackingID)
	if err != nil {
		log.Error(ctx, "Failed to get TMS integration", zap.Error(err))

		return c.Status(http.StatusInternalServerError).JSON(&ConfirmApptResponse{
			Message: "Failed to get TMS integration",
		})
	}

	tmsClient, err := tms.New(ctx, *tmsIntegration)
	if err != nil {
		log.Error(ctx, "error creating TMS client", zap.Error(err))

		return c.SendStatus(http.StatusInternalServerError)
	}

	return createAndUpdate(ctx, c, user, body, tmsIntegration, tmsClient, warehouse)
}

// createAndUpdate creates an appointment in rds AND in scheduling portal (if dry run is false).
// Creates warehouse in rds if one doesn't exist (for custom wh support type integrations)
// OR if warehouse support type is native, creates warehouse address association to existing native warehouse.
// Also records usage of this scheduling integration for the customer and warehouse.
// Updates TMS if IsAppointmentTMSUpdateEnabled feature flag is enabled.
func createAndUpdate(
	ctx context.Context,
	c *fiber.Ctx,
	user models.User,
	body *ConfirmApptBody,
	tmsIntegration *models.Integration,
	tmsClient tms.Interface,
	warehouse *models.Warehouse,
) error {

	ctx = log.With(ctx, zap.Time("apptTime", body.StartTime), zap.Uint("serviceID", user.ServiceID))

	// Create appointment in scheduling portal (this does not add appt to RDS yet)
	appt, err := createAppointment(ctx, body, user.EmailAddress, user.ID, user.ServiceID, warehouse)
	if err != nil {
		// Check for user-facing errors first (these include parsed Opendock validation errors)
		var userFacingErr errtypes.UserFacingError
		if errors.As(err, &userFacingErr) {
			log.Warn(ctx, "user-facing appointment creation error", zap.Error(err))

			return c.Status(http.StatusBadRequest).JSON(&ConfirmApptResponse{
				Message: userFacingErr.Error(),
			})
		}

		var httpErr errtypes.HTTPResponseError
		if errors.As(err, &httpErr) && httpErr.StatusCode == http.StatusConflict {
			return c.Status(http.StatusConflict).JSON(&ConfirmApptResponse{
				Message: "An appointment already exists for this reference number. " +
					"Please check existing appointments.",
			})
		}

		var cyclopsErr *models.CyclopsError
		if errors.As(err, &cyclopsErr) {
			for _, cyclopsErrMsg := range cyclopsErr.Errors {
				if strings.Contains(cyclopsErrMsg, "already exists") {
					return c.Status(http.StatusConflict).JSON(&ConfirmApptResponse{
						Message: "An appointment already exists for this reference number. " +
							"Please check existing appointments.",
					})
				}
			}

			return c.Status(http.StatusInternalServerError).JSON(&ConfirmApptResponse{
				Message: cyclopsErr.Message,
			})
		}

		log.Error(ctx, "failed to create appointment", zap.Error(err))

		// If no user facing error to return return generic error
		return c.Status(http.StatusInternalServerError).JSON(&ConfirmApptResponse{
			Message: "Failed to create appointment",
		})
	}

	// Associate customer with appointment (both TMS customer and name)
	load, err := associateCustomerWithAppt(ctx, &appt, tmsIntegration)
	if err != nil {
		log.Warn(ctx, "failed to associate customer with appointment", zap.Error(err))
		// Continue with appointment creation even if customer association fails
	}

	if err := apptDB.Create(ctx, &appt); err != nil {
		log.Error(ctx, "failed to create appt record in DB", zap.Error(err))
		return c.Status(http.StatusInternalServerError).JSON(&ConfirmApptResponse{
			Message: "Failed to save appointment",
		})
	}

	// After appt creation ensure warehouse exists before recording customer scheduling integration usage
	var existingWarehouse, newWarehouse *models.Warehouse
	if warehouse != nil {
		existingWarehouse = warehouse
	} else {
		// If no warehouse was provided from caller, we need to ensure the warehouse exists or create it if we're
		// scheduling for a integration that we build custom warehouses for.
		// See `models/warehouse.go` for more details on native vs custom warehouse support.
		existingWarehouse, err = warehouseDB.GetWarehouseByIDAndSource(
			ctx,
			user.ServiceID,
			models.WarehouseSource(body.Source),
			body.WarehouseID,
		)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			log.Warn(ctx, "error while trying to get existing warehouse", zap.Error(err))
		}
		// If no existing warehouse, we need to create a new one if the integration supports custom warehouses
		if existingWarehouse == nil && load != nil {
			createdWarehouse, err := createCustomWarehouseFromLoad(ctx, load, body.StopType, body.Source)
			if err != nil {
				log.Warn(ctx, "failed to create custom warehouse from load", zap.Error(err))
			} else {
				newWarehouse = createdWarehouse
			}
		}
	}

	tmsIntegrationID := getTMSIntegrationID(load, tmsIntegration)
	warehouseIDForUsage := getWarehouseIDForUsage(warehouse, existingWarehouse, newWarehouse)

	if warehouseIDForUsage == 0 || tmsIntegrationID == 0 {
		log.Warn(ctx, "missing warehouse or TMS integration ID for usage recording",
			zap.Uint("warehouseID", warehouseIDForUsage),
			zap.Uint("tmsIntegrationID", tmsIntegrationID),
		)
		// Skipping customer warehouse scheduler integration usage recording
	} else {
		// Asynchronously record usage of this scheduling integration for the customer and warehouse.
		go sentry.WithHub(ctx, func(ctx context.Context) {
			asyncCtx := log.InheritContext(ctx, context.Background())
			asyncCtx, cancel := context.WithTimeout(asyncCtx, 2*time.Minute)
			defer cancel()

			recordCustomerIntegrationUsage(
				asyncCtx,
				&appt,
				load,
				warehouseIDForUsage,
				appt.IntegrationID,
				tmsIntegrationID,
			)
		})
	}

	resp := ConfirmApptResponse{Appointment: appt}

	if body.Source == string(models.YardView) {
		warehouseDomain := warehouse.Settings.Domain
		resp.URL = fmt.Sprintf(
			"https://%s/appointmentpages/appointments.aspx?PageMode=Edit&ApptPID=%d",
			warehouseDomain,
			appt.ExternalPrivateID,
		)
	}

	if body.DryRun || body.SkipTMSUpdate || tmsIntegration == nil || tmsClient == nil {
		return c.Status(http.StatusOK).JSON(resp)
	}

	service, err := rds.GetServiceByID(ctx, user.ServiceID)
	if err != nil || !service.IsAppointmentTMSUpdateEnabled {
		if err != nil {
			log.Warn(ctx, "Failed to get service feature flag", zap.Error(err))
		}

		return c.Status(http.StatusOK).JSON(resp)
	}

	resp.IsAppointmentTMSUpdateEnabled = true
	if err := updateTMSWithAppointment(ctx, body, &appt, tmsIntegration, tmsClient); err != nil {
		log.Error(ctx, "Failed to update TMS", zap.Error(err))

		return c.Status(http.StatusOK).JSON(resp)
	}

	resp.TMSUpdateSucceeded = true

	return c.Status(http.StatusOK).JSON(resp)
}

// createAppointment creates an appointment in scheduling portal if not dry-run.
// Returns appointment object to be inserted in DB
func createAppointment(
	ctx context.Context,
	body *ConfirmApptBody,
	email string,
	userID,
	serviceID uint,
	warehouse *models.Warehouse,
) (models.Appointment, error) {

	warehouseID := uint(0)
	if warehouse != nil {
		warehouseID = warehouse.ID
	}

	if body.DryRun {
		return models.Appointment{
			Account:             email,
			UserID:              userID,
			FreightTrackingID:   body.FreightTrackingID,
			LoadID:              body.LoadID,
			IntegrationID:       body.IntegrationID,
			ExternalID:          strconv.Itoa(-int(time.Now().Unix())),
			ConfirmationNo:      strconv.Itoa(-int(time.Now().Unix())),
			ExternalWarehouseID: body.WarehouseID,
			WarehouseID:         warehouseID,
			LoadTypeID:          body.LoadTypeID,
			DockID:              body.DockID,
			PONums:              body.PONums,
			RefNumber:           body.RefNumber,
			Date:                body.StartTime.Format(time.DateOnly),
			StartTime:           body.StartTime,
			Status:              "{drumkitDryRun: true}",
		}, nil
	}

	integration, err := integrationDB.GetSchedulerByServiceUserIDsAndName(
		ctx,
		userID,
		serviceID,
		body.Source,
		body.IntegrationID,
	)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("get scheduler integration: %w", err)
	}

	ctx = log.With(
		ctx,
		zap.String("schedulingUsername", integration.Username),
		zap.Uint("integrationID", integration.ID),
	)

	client, err := scheduling.GetCachedClient(ctx, integration)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("create scheduler client: %w", err)
	}

	formattedCustomFields := body.CustomApptFieldsTemplate
	if body.Source == string(models.Opendock) {
		formattedCustomFields = formatOpendockMultiselect(body.CustomApptFieldsTemplate)
	}

	req := models.MakeAppointmentRequest{
		CcEmails:        body.CcEmails,
		CustomFields:    formattedCustomFields,
		DockID:          body.DockID,
		LoadTypeID:      body.LoadTypeID,
		Notes:           body.Notes,
		RefNumber:       body.RefNumber,
		StartTime:       body.StartTime,
		ReservationType: body.ReservationType,
		ContactDetails: models.ContactDetails{
			Phone: body.Phone,
			Email: body.Email,
		},
		FormData: body.FormData,
	}

	var opts []models.SchedulingOption

	// Handle pre-uploaded file keys (specific to opendock)
	if len(body.UploadedFileKeys) > 0 {
		opts = append(opts, models.WithUploadedKeys(body.UploadedFileKeys))
	}

	switch body.Source {
	case string(models.C3Reservations):
		// C3Reservations uses standard MakeAppointmentRequest
		// formData can be passed through options if needed in the future
		req.WarehouseID = body.WarehouseID
		req.WarehouseTimezone = body.WarehouseTimezone
		req.PONums = body.PONums

	case string(models.E2open):
		req.RequestType = string(body.RequestType)
		req.AppointmentType = body.AppointmentType
		req.LoadTypeID = body.FreightTrackingID
		req.Appointments = body.Appointments
		req.StopType = string(body.StopType)
		req.Company = body.Company
		req.Operation = body.Operation
		req.ProIDFieldName = body.ProIDFieldName

		// Add appointmentDate to scheduling options if provided
		if body.AppointmentDate != "" {
			opts = append(opts, models.WithAppointmentDate(body.AppointmentDate))
		}

		// Add proIdFieldName to scheduling options if provided
		if body.ProIDFieldName != "" {
			opts = append(opts, models.WithProIDFieldName(body.ProIDFieldName))
		}

	case string(models.Manhattan):
		req.Appointments = body.Appointments

	case string(models.OneNetwork):
		req.PONums = body.PONums
		req.LoadTypeID = body.FreightTrackingID

	case string(models.Costco):
		req.Appointments = body.Appointments

	case string(models.Retalix):
		req.PONums = body.PONums
		opts = append(
			opts,
			models.WithWarehouse(
				models.Warehouse{
					WarehouseID:   warehouse.WarehouseID,
					WarehouseName: warehouse.WarehouseName,
				},
			),
		)

	case string(models.YardView):
		req.RequestType = string(body.RequestType)
		req.WarehouseID = body.WarehouseID
		req.WarehouseTimezone = body.WarehouseTimezone
		req.DockID = body.DockID
		req.PONums = body.PONums
		req.LoadTypeID = body.PONums
		req.Notes = body.Notes
		req.ApptKey = body.ApptKey
		req.TrailerID = body.TrailerID

		if body.Weight != "" {
			weight, err := strconv.Atoi(body.Weight)
			if err != nil {
				return models.Appointment{}, fmt.Errorf("invalid weight: %w", err)
			}

			req.Weight = weight
		}

		loadTypePID, err := strconv.Atoi(body.LoadTypeID)
		if err != nil {
			return models.Appointment{}, fmt.Errorf("invalid load type: %w", err)
		}

		opts = append(
			opts,
			models.WithCarrierSCAC(body.CarrierSCAC),
			models.WithSchedulePID(body.SchedulePID),
			models.WithLoadTypePID(loadTypePID),
		)
	}

	if integration.Tenant != "" {
		opts = append(opts, models.WithTenant(integration.Tenant))
	}

	if body.FlowType != "" {
		opts = append(opts, models.WithFlowType(body.FlowType))
	}

	appt, err := client.MakeAppointment(ctx, req, opts...)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("make appointment: %w", err)
	}

	appt.Account = email
	appt.UserID = userID
	if body.Source == string(models.E2open) ||
		body.Source == string(models.Manhattan) ||
		body.Source == string(models.Costco) {
		appt.FreightTrackingID = body.Appointments[0].FreightTrackingID
	} else {
		appt.FreightTrackingID = body.FreightTrackingID
	}
	appt.LoadID = body.LoadID
	appt.IntegrationID = body.IntegrationID
	appt.ServiceID = serviceID
	appt.Source = models.IntegrationName(body.Source)
	appt.DockID = body.DockID
	appt.ExternalWarehouseID = body.WarehouseID
	appt.WarehouseID = warehouseID

	if body.Source == string(models.E2open) ||
		body.Source == string(models.Manhattan) ||
		body.Source == string(models.Costco) {
		// TODO: Update these things according to multiple appointments
		layout := "2006-01-02T15:04:05Z07:00" // RFC3339 or use the layout your string matches
		parsedTime, err := time.Parse(layout, body.Appointments[0].Start)
		if err != nil {
			log.Error(ctx, "invalid start time format", zap.Error(err))
			appt.StartTime = body.StartTime
		} else {
			appt.StartTime = parsedTime
		}

		appt.Date = appt.StartTime.Format(time.DateOnly)
	} else {
		appt.Date = body.StartTime.Format(time.DateOnly)
		appt.StartTime = body.StartTime
	}

	// TODO: do this directly in the body instead
	// Validate and set source only if it's a valid scheduling integration
	switch models.IntegrationName(body.Source) {
	case models.C3Reservations,
		models.DataDocks,
		models.DaySmart,
		models.E2open,
		models.Manhattan,
		models.Costco,
		models.Opendock,
		models.Retalix,
		models.Turvo,
		models.Velostics,
		models.YardView:

		appt.Source = models.IntegrationName(body.Source)

	default:
		log.Warn(
			ctx,
			"unsupported scheduling integration source for appt",
			zap.String("scheduler", body.Source),
		)
	}

	if status, err := json.Marshal(appt.Status); err == nil {
		appt.Status = string(status)
	}

	log.Info(
		ctx,
		"appointment created successfully",
		zap.Uint("apptID", appt.ID),
		zap.String("confirmationNo", appt.ConfirmationNo),
	)

	// Try to create warehouse-address association for native sources if we have a warehouse
	go sentry.WithHub(ctx, func(ctx context.Context) {
		asyncCtx := log.InheritContext(ctx, context.Background())
		asyncCtx, cancel := context.WithTimeout(asyncCtx, 2*time.Minute)
		defer cancel()

		// Only associate addresses for native-supported warehouse sources
		supportType, err := models.GetWarehouseSupportType(models.WarehouseSource(body.Source))
		if err != nil {
			log.WarnNoSentry(asyncCtx, "failed to determine warehouse support type for association", zap.Error(err))
			return
		}

		if supportType == models.NativeWarehouseSupportType && warehouse != nil {
			err := createWarehouseAddressAssociation(
				asyncCtx,
				warehouse,
				serviceID,
				body.Source,
				body.RequestType,
			)
			if err != nil {
				log.WarnNoSentry(asyncCtx, "failed to create warehouse-address association", zap.Error(err))
			}
		}
	})

	return appt, nil
}

// createWarehouseAddressAssociation attempts to create a warehouse-address and association to the selected warehouse

func createWarehouseAddressAssociation(
	ctx context.Context,
	warehouse *models.Warehouse,
	serviceID uint,
	integrationName string,
	requestType models.RequestType,
) error {

	if string(requestType) == "" {
		return fmt.Errorf("empty request type for %s integration", integrationName)
	}

	if warehouse == nil {
		return fmt.Errorf("empty warehouse for %s integration", integrationName)
	}

	load, err := loadDB.GetLoadByWarehouseID(
		ctx,
		serviceID,
		warehouse.ID,
		requestType,
	)
	if err != nil {
		return fmt.Errorf("failed to get load: %w", err)
	}

	err = warehouseDB.CreateWarehouseAddress(
		ctx,
		*warehouse,
		load,
		requestType,
	)
	if err != nil {
		return fmt.Errorf("failed to create warehouse-address association: %w", err)
	}

	log.Info(ctx, "successfully created warehouse-address association")

	return nil
}

// checkYardViewSlotCapacity verifies that the selected time slot has available capacity
// The capacity of the slot could go from 1/4 to 0/4 between get open slots call and this create appt request
// because someone else booked in that slot.
// Yardview create appt (API) does not have a validation check thus without doing this check we can overbook appts,
// leading to 5/4 appts scheduled. The case that raised this concern was 7 appts booked when slot has 3 capacity.
func checkYardViewSlotCapacity(
	ctx context.Context,
	user models.User,
	body *ConfirmApptBody,
	warehouse *models.Warehouse,
) (bool, error) {
	// Get YardView scheduling integration
	integration, err := integrationDB.GetSchedulerByServiceUserIDsAndName(
		ctx,
		user.ID,
		user.ServiceID,
		string(models.YardView),
		body.IntegrationID,
	)
	if err != nil {
		return false, fmt.Errorf("get scheduler integration: %w", err)
	}

	client, err := scheduling.GetCachedClient(ctx, integration)
	if err != nil {
		return false, fmt.Errorf("create scheduler client: %w", err)
	}

	// Create a date range around the requested start time to get slots
	startDate := body.StartTime.Truncate(24 * time.Hour)
	endDate := startDate.Add(24 * time.Hour)

	slotsRequest := models.GetOpenSlotsRequest{
		Start:       startDate,
		End:         endDate,
		WarehouseID: body.WarehouseID,
		Warehouse:   warehouse,
	}

	slots, err := client.GetOpenSlots(ctx, body.LoadTypeID, slotsRequest)
	if err != nil {
		return false, fmt.Errorf("GetOpenSlots failed: %w", err)
	}

	slots = FilterSlotsByCurrentShift(ctx, *warehouse, slots)

	appts, err := client.GetAppointments(
		ctx,
		models.GetAppointmentsRequest{
			StartDate:                startDate,
			EndDate:                  endDate,
			WarehouseID:              body.WarehouseID,
			Warehouse:                warehouse,
			Status:                   models.AppointmentStatusPending,
			ErrorOnEmptyAppointments: false,
		},
	)
	if err != nil {
		return false, fmt.Errorf("GetAppointments failed: %w", err)
	}

	slots = FilterSlotsByAppointments(ctx, slots, appts)

	// Check if the requested time slot has capacity
	requestedTime := body.StartTime
	for _, slot := range slots {
		for _, startTime := range slot.StartTimes {
			// Compare times with some tolerance for minor differences
			if timeEqual(startTime, requestedTime) && slot.Capacity > 0 {
				log.Info(
					ctx,
					"slot capacity check passed",
					zap.Time("requestedTime", requestedTime),
					zap.Time("slotTime", startTime),
					zap.Int("capacity", slot.Capacity),
				)
				return true, nil
			}
		}
	}

	log.WarnNoSentry(
		ctx,
		"slot capacity check failed - no available capacity",
		zap.Time("requestedTime", requestedTime),
		zap.Int("totalSlotsChecked", len(slots)),
	)
	return false, nil
}

// timeEqual checks if two times are equal within a 1-minute tolerance to handle minor diffs in time.
func timeEqual(t1, t2 time.Time) bool {
	diff := t1.Sub(t2)
	if diff < 0 {
		diff = -diff
	}
	return diff <= time.Minute
}

func getTMSIntegration(ctx context.Context, serviceID uint, freightID string) (*models.Integration, error) {
	load, err := loadDB.GetLoadByFreightIDAndService(ctx, serviceID, freightID)
	if err == nil {
		return &load.TMS, nil
	}

	tms, err := integrationDB.MatchTMSByServiceAndFreightID(ctx, serviceID, freightID)
	if err != nil {
		return nil, fmt.Errorf("could not get tms integration: %w", err)
	}

	return tms, nil
}

// updateTMSWithAppointment updates load in TMS with appt details. Requires TMS integration to update load implemented.
// TODO: deprecate calling this, frontend will make a separate call to update load in TMS with appt details.
func updateTMSWithAppointment(
	ctx context.Context,
	body *ConfirmApptBody,
	appt *models.Appointment,
	tmsIntegration *models.Integration,
	tmsClient tms.Interface,
) error {

	load, _, err := tmsClient.GetLoad(ctx, body.FreightTrackingID)
	if err != nil {
		return fmt.Errorf("get TMS load: %w", err)
	}

	normalizedTime, err := getNormalizedTime(body, *tmsIntegration)
	if err != nil {
		log.Error(ctx, "error loading location", zap.String("timezone", body.WarehouseTimezone), zap.Error(err))
		return fmt.Errorf("normalize time: %w", err)
	}

	reqLoad := load
	updateLoadDetails(&load, &reqLoad, body.StopType, appt.ConfirmationNo, body.Source, normalizedTime)

	updatedLoad, _, err := tmsClient.UpdateLoad(ctx, &load, &reqLoad)
	if err != nil {
		log.Error(ctx, "failed to update TMS load with appt details", zap.Error(err))
		return fmt.Errorf("update TMS load: %w", err)
	}

	if err := loadDB.UpsertLoad(ctx, &updatedLoad, &tmsIntegration.Service); err != nil {
		log.Error(ctx, "error updating load DB with appt details", zap.Error(err))
	}

	return nil
}

// getNormalizedTime adjusts the appointment time based on TMS integration requirements.
func getNormalizedTime(body *ConfirmApptBody, tmsIntegration models.Integration) (time.Time, error) {
	if tmsIntegration.Name != models.Aljex {
		return body.StartTime, nil
	}

	loc, err := time.LoadLocation(body.WarehouseTimezone)
	if err != nil {
		return time.Time{}, fmt.Errorf("load timezone %s: %w", body.WarehouseTimezone, err)
	}

	// Use .In() in case JSON timestamp had an offset
	t := body.StartTime.In(loc)

	return time.Date(t.Year(), t.Month(), t.Day(), t.Hour(), t.Minute(), t.Second(), t.Nanosecond(), time.UTC), nil
}

// updateLoadDetails updates only the specific appointment fields based on stop type
func updateLoadDetails(
	curLoad,
	reqLoad *models.Load,
	stopType StopType,
	confirmationNo,
	schedulerIntegrationName string,
	t time.Time,
) {

	nullTime := models.NullTime{Time: t, Valid: true}
	ref := schedulerIntegrationName + " " + confirmationNo

	if stopType == PickupStopType {
		reqLoad.Pickup.RefNumber = appendWithSeparator(curLoad.Pickup.RefNumber, confirmationNo)
		reqLoad.Pickup.ApptNote = appendWithSeparator(curLoad.Pickup.ApptNote, ref)
		reqLoad.Pickup.ApptStartTime = nullTime
		reqLoad.Pickup.ReadyTime = nullTime
		return
	}

	// For dropoff/consignee stop
	reqLoad.Consignee.RefNumber = appendWithSeparator(curLoad.Consignee.RefNumber, confirmationNo)
	reqLoad.Consignee.ApptNote = appendWithSeparator(curLoad.Consignee.ApptNote, ref)
	reqLoad.Consignee.ApptStartTime = nullTime
	reqLoad.Consignee.MustDeliver = nullTime
}

func appendWithSeparator(base, addition string) string {
	if base == "" {
		return addition
	}

	return base + " - " + addition
}

// formatOpendockMultiselect converts OpenDock's dropdownmultiselect values from strings
// to arrays in order to meet API requirements.
// Example: "Test Value" -> ["Test Value"]
func formatOpendockMultiselect(fields models.CustomApptFieldsTemplate) models.CustomApptFieldsTemplate {
	processed := make(models.CustomApptFieldsTemplate, len(fields))

	for i, field := range fields {
		processed[i] = field

		if field.Type == "dropdownmultiselect" {
			switch v := field.Value.(type) {
			case string:
				if v != "" {
					processed[i].Value = []string{v}
				} else {
					processed[i].Value = []string{}
				}
			case nil:
				processed[i].Value = []string{}
			}
		}
	}

	return processed
}

// associateCustomerWithAppt links customer information to the appointment.
// Always sets CustomerName if available (for fallback queries and tracking).
// Additionally links TMSCustomerID if TMS integration and external TMS ID are available.
// Returns the load for reuse in other operations.
func associateCustomerWithAppt(
	ctx context.Context,
	appt *models.Appointment,
	tmsIntegration *models.Integration,
) (*models.Load, error) {
	if appt.LoadID == 0 {
		return nil, nil // No load, nothing to associate
	}

	// Get the load to access customer information
	load, err := loadDB.GetLoadByID(ctx, appt.LoadID)
	if err != nil {
		return nil, fmt.Errorf("failed to get load by ID %d: %w", appt.LoadID, err)
	}

	// Always set customer name if available (for fallback queries and tracking)
	if load.Customer.Name != "" {
		appt.CustomerName = &load.Customer.Name
		log.Info(
			ctx,
			"set customer name on appointment",
			zap.Uint("appointmentLoadID", appt.LoadID),
			zap.String("customerName", load.Customer.Name),
		)
	}

	// Additionally, try to link TMS customer if we have both TMS integration and customer external TMS ID
	if tmsIntegration != nil && load.Customer.ExternalTMSID != "" {
		tmsCustomer, err := tmsCustomerDB.GetByExternalTMSID(
			ctx,
			tmsIntegration.ID,
			load.Customer.ExternalTMSID,
		)
		if err != nil {
			log.Warn(
				ctx,
				"failed to find TMS customer, will rely on customer name",
				zap.String("externalTMSID", load.Customer.ExternalTMSID),
				zap.Error(err),
			)
		} else {
			// Successfully linked TMS customer
			appt.TMSCustomerID = &tmsCustomer.ID
			log.Info(
				ctx,
				"linked TMS customer to appointment",
				zap.Uint("appointmentLoadID", appt.LoadID),
				zap.String("customerExternalTMSID", load.Customer.ExternalTMSID),
				zap.Uint("tmsCustomerID", tmsCustomer.ID),
				zap.String("tmsCustomerName", tmsCustomer.Name),
			)
		}
	}

	return &load, nil
}

// recordCustomerIntegrationUsage records that a customer used a specific scheduling integration.
// Priority 1: Use TMS customer ID if appointment has it linked
// Priority 2: Use customer name from load if TMS customer not available
func recordCustomerIntegrationUsage(
	ctx context.Context,
	appt *models.Appointment,
	load *models.Load,
	warehouseID,
	schedulingIntegrationID,
	tmsIntegrationID uint,
) {

	if schedulingIntegrationID == 0 || tmsIntegrationID == 0 {
		log.Warn(
			ctx,
			"missing required IDs for usage recording",
			zap.Uint("schedulingIntegrationID", schedulingIntegrationID),
			zap.Uint("tmsIntegrationID", tmsIntegrationID),
		)
		return
	}

	// Validate we have either TMS customer or load with customer name
	if appt == nil || (appt.TMSCustomerID == nil && (load == nil || load.Customer.Name == "")) {
		log.Warn(ctx, "no customer information available for usage recording")
		return
	}

	serviceID := appt.ServiceID
	if serviceID == 0 && load != nil {
		serviceID = load.ServiceID
	}

	// Priority 1: Use TMS customer ID if linked
	if appt.TMSCustomerID != nil {
		err := tmsCustomerSchedulingIntegrationDB.RecordUsageByTMSCustomerID(
			ctx,
			serviceID,
			tmsIntegrationID,
			warehouseID,
			*appt.TMSCustomerID,
			schedulingIntegrationID,
		)
		if err != nil {
			log.Warn(
				ctx,
				"failed to record customer integration usage by TMS customer ID",
				zap.Error(err),
			)
		} else {
			log.Info(
				ctx,
				"recorded customer integration usage by TMS customer ID",
				zap.Uint("tmsIntegrationID", tmsIntegrationID),
				zap.Uint("warehouseID", warehouseID),
				zap.Uint("tmsCustomerID", *appt.TMSCustomerID),
				zap.Uint("schedulingIntegrationID", schedulingIntegrationID),
			)
		}
		return
	}

	// Priority 2: Use customer name as fallback
	if load != nil && load.Customer.Name != "" {
		err := tmsCustomerSchedulingIntegrationDB.RecordUsageByCustomerName(
			ctx,
			serviceID,
			tmsIntegrationID,
			warehouseID,
			load.Customer.Name,
			schedulingIntegrationID,
		)
		if err != nil {
			log.Warn(
				ctx,
				"failed to record customer integration usage by customer name",
				zap.Error(err),
			)
		} else {
			log.Info(
				ctx,
				"recorded customer integration usage by customer name",
				zap.Uint("tmsIntegrationID", tmsIntegrationID),
				zap.String("customerName", load.Customer.Name),
				zap.Uint("schedulingIntegrationID", schedulingIntegrationID),
			)
		}
	}
}

// abridgeRequestBody removes high-volume data from the request body to reduce log size & noise.
func abridgeRequestBody(body ConfirmApptBody) ConfirmApptBody {
	dupe := body
	dupe.CustomApptFieldsTemplate = models.CustomApptFieldsTemplate{
		{
			Name:  "redacted to reduce log size & noise",
			Value: "original array length = " + strconv.Itoa(len(body.CustomApptFieldsTemplate)),
		},
	}

	return dupe
}
