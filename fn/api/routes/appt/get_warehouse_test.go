package appt

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/drumkitai/drumkit/common/models"
	apitesting "github.com/drumkitai/drumkit/fn/api/testing"
)

func TestGetWarehouse(t *testing.T) {
	validator := apitesting.NewValidationTest(t)

	t.Run("valid query params", func(t *testing.T) {
		query := GetWarehouseQuery{
			WarehouseID:   "test-load",
			Source:        "opendock",
			ConsigneeName: "John Doe",
		}

		if err := validator.TestQuery(query); err != nil {
			t.Errorf("Expected no error, got: %v", err)
		}
	})
}

func TestShowSubscribedEmailByService(t *testing.T) {
	ctx := context.Background()

	tests := []struct {
		name     string
		user     models.User
		wh       *models.Warehouse
		expected bool
	}{
		{
			name:     "Matching domain",
			user:     models.User{EmailAddress: "<EMAIL>"},
			wh:       &models.Warehouse{DefaultSubscribedEmail: "<EMAIL>"},
			expected: true,
		},
		{
			name:     "Non-matching domain",
			user:     models.User{EmailAddress: "<EMAIL>"},
			wh:       &models.Warehouse{DefaultSubscribedEmail: "<EMAIL>"},
			expected: false,
		},
		{
			name:     "Case insensitive match",
			user:     models.User{EmailAddress: "<EMAIL>"},
			wh:       &models.Warehouse{DefaultSubscribedEmail: "<EMAIL>"},
			expected: true,
		},
		{
			name:     "Malformed email (no @)",
			user:     models.User{EmailAddress: "invalidemail"},
			wh:       &models.Warehouse{DefaultSubscribedEmail: "<EMAIL>"},
			expected: false,
		},
		{
			name:     "Empty email",
			user:     models.User{EmailAddress: ""},
			wh:       &models.Warehouse{DefaultSubscribedEmail: "<EMAIL>"},
			expected: false,
		},
		{
			name:     "Empty warehouse subscription email",
			user:     models.User{EmailAddress: "<EMAIL>"},
			wh:       &models.Warehouse{DefaultSubscribedEmail: ""},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := showSubscribedEmailByService(ctx, tt.user, tt.wh)
			assert.Equal(t, tt.expected, result)
		})
	}
}
