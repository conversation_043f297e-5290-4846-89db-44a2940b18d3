package carrierverification

import (
	"errors"
	"fmt"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/integrations/carrierverification"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/rds"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
)

type GetCarrierVerificationQuery struct {
	Email string `json:"email"`
}

func GetCarrierVerification(c *fiber.Ctx) error {
	var query GetCarrierVerificationQuery
	if err := api.Parse(c, nil, &query, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext())
	userServiceID := middleware.ServiceIDFromContext(c)

	service, err := rds.GetServiceByID(ctx, userServiceID)
	if err != nil {
		log.Error(ctx, "could not get service", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	integration, err := integrationDB.GetCarrierVerificationByServiceID(ctx, service.ID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(http.StatusNotFound).SendString("carrier verification integration not found")
		}

		log.Error(
			ctx,
			"Get carrier verification from DB failed",
			zap.Error(fmt.Errorf("error fetching carrier verification integration: %w", err)),
		)

		return c.SendStatus(http.StatusInternalServerError)
	}

	client := carrierverification.MustNew(ctx, integration)

	carrier, err := client.GetCarrier(ctx, query.Email)
	if err != nil {
		if errtypes.IsEntityNotFoundError(err) {
			log.WarnNoSentry(ctx, "Carrier not found", zap.Error(err))
			return c.SendStatus(http.StatusNotFound)
		}

		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.Status(http.StatusOK).JSON(carrier)
}
