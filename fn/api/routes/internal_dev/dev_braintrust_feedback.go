package internaldev

import (
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers"
	braintrustutil "github.com/drumkitai/drumkit/common/helpers/braintrust"
	"github.com/drumkitai/drumkit/common/integrations/tms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
	quoteRequestDB "github.com/drumkitai/drumkit/common/rds/quote_request"
	suggestionsDB "github.com/drumkitai/drumkit/common/rds/suggestedloadchanges"
	apiutil "github.com/drumkitai/drumkit/fn/api/util"
)

type (
	SubmitManualBraintrustFeedbackBody struct {
		InternalDrumkitKey string `json:"internalDrumkitKey" validate:"required"`
		SuggestionID       uint   `json:"suggestionID" validate:"required"`
		LoadID             uint   `json:"loadID" validate:"required_without=LoadFreightTrackingID"`
		// If LoadID is not provided, we use LoadFreightTrackingID and the serviceID in JWT claims to look up the load
		LoadFreightTrackingID string `json:"loadFreightTrackingID" validate:"required_without=LoadID"`
		//nolint:lll
		Pipeline models.SuggestionPipeline `json:"pipeline" validate:"required,oneof=load_building_pipeline quick_quote_pipeline"`
	}
)

// SubmitManualBraintrustFeedback is a route for submitting manual Braintrust feedback for load building
// and quote request suggestions. It looks up the provided load and suggestion ID and updates
// the database with the applied data and submits the feedback to Braintrust.
// NOTE: This endpoint is internal only and is not used in production.
func SubmitManualBraintrustFeedback(c *fiber.Ctx) error {
	var body SubmitManualBraintrustFeedbackBody
	if err := c.BodyParser(&body); err != nil {
		return c.Status(fiber.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()
	userServiceID := middleware.ServiceIDFromContext(c)

	isValid, err := helpers.ValidateInternalDrumkitKey(ctx, body.InternalDrumkitKey)
	if !isValid {
		return c.Status(fiber.StatusUnauthorized).SendString(err.Error())
	}

	// Get load from TMS
	var load models.Load
	if body.LoadID != 0 {
		load, err = loadDB.GetLoadByID(ctx, body.LoadID)
		if err != nil {
			return apiutil.HandleDBError(c, err, true, "load ID %d not found", body.LoadID)
		}

		client, err := tms.New(ctx, load.TMS)
		if err != nil {
			log.ErrorNoSentry(ctx, "error creating TMS client", zap.Error(err))
			return c.Status(fiber.StatusInternalServerError).SendString("error creating TMS client")
		}

		load, _, err = client.GetLoad(ctx, load.FreightTrackingID)
		if err != nil {
			log.ErrorNoSentry(ctx, "error getting load from TMS", zap.Error(err))
			return c.Status(fiber.StatusInternalServerError).SendString("error getting load from TMS")
		}
	} else {
		// Match TMS by FreightID and get load from TMS
		tmsIntegration, err := integrationDB.MatchTMSByServiceAndFreightID(
			ctx,
			userServiceID,
			body.LoadFreightTrackingID,
		)
		if err != nil {
			log.Error(ctx, "could not get tms integration:", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}

		client, err := tms.New(ctx, *tmsIntegration)
		if err != nil {
			log.ErrorNoSentry(ctx, "error creating TMS client", zap.Error(err))
			return c.Status(fiber.StatusInternalServerError).SendString("error creating TMS client")
		}

		load, _, err = client.GetLoad(ctx, body.LoadFreightTrackingID)
		if err != nil {
			log.ErrorNoSentry(ctx, "error getting load from TMS", zap.Error(err))
			return c.Status(fiber.StatusInternalServerError).SendString("error getting load from TMS")
		}
	}

	if body.Pipeline == models.LoadBuildingPipeline {
		suggestion, err := suggestionsDB.GetSuggestionByID(ctx, body.SuggestionID)
		if err != nil {
			return apiutil.HandleDBError(c, err, true, "suggestion ID %d not found", body.SuggestionID)
		}

		braintrustutil.UpdateAppliedSuggestionWithLoad(load, suggestion)

		// 1. Update database
		if err = suggestionsDB.UpdateSuggestion(ctx, suggestion); err != nil {
			// Fail-open
			log.WarnNoSentry(ctx, "error updating suggestion's applied data", zap.Error(err))
		}

		// 2. Submit to Braintrust
		err = braintrustutil.SubmitBraintrustLoadBuildingFeedback(ctx, *suggestion)
		if err != nil {
			return c.Status(fiber.StatusInternalServerError).SendString(err.Error())
		}

		return c.Status(fiber.StatusOK).SendString("Braintrust load building feedback submitted successfully")
	}

	qrSuggestion, err := quoteRequestDB.GetRequestByID(ctx, body.SuggestionID)
	if err != nil {
		return apiutil.HandleDBError(c, err, true, "suggestion ID %d not found", body.SuggestionID)
	}

	// 1. Update database
	if err = quoteRequestDB.UpdateAppliedRequest(ctx, &qrSuggestion); err != nil {
		// Fail-open
		log.WarnNoSentry(ctx, "error updating quote request's applied data", zap.Error(err))
	}

	// 2. Submit to Braintrust
	err = braintrustutil.SubmitBraintrustQuoteRequestFeedback(ctx, qrSuggestion)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).SendString(err.Error())
	}

	return c.Status(fiber.StatusOK).SendString("Braintrust quote request feedback submitted successfully")

}
