package carriergroups

import (
	"context"
	"errors"
	"fmt"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	carrierGroupDB "github.com/drumkitai/drumkit/common/rds/carrier_group"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	tmsCarrierDB "github.com/drumkitai/drumkit/common/rds/tms_carrier"
)

type CarrierInfo struct {
	Name  string `json:"name"`
	Email string `json:"email"`
}

type CarrierGroup struct {
	Name     string        `json:"name"`
	Email    string        `json:"email,omitempty"` // optional (Outlook Distribution Lists have an email)
	Carriers []CarrierInfo `json:"carriers"`
}

type CreationRequest struct {
	CarrierGroups      []CarrierGroup `json:"carrierGroups"`
	ServiceID          uint           `json:"serviceID"`
	InternalDrumkitKey string         `json:"internalDrumkitKey"`
	IntegrationName    string         `json:"integrationName"`
}

// CreateCarrierGroups creates new carrier groups from a JSON body
// Carriers within a group are either associated with existing TMSCarriers or created as new ones
func CreateCarrierGroups(c *fiber.Ctx) error {
	ctx := c.UserContext()

	req, integration, err := parseAndValidateRequest(c)
	if err != nil {
		var fiberErr *fiber.Error
		if errors.As(err, &fiberErr) {
			return c.Status(fiberErr.Code).JSON(fiber.Map{
				"error": fiberErr.Message,
			})
		}
		log.ErrorNoSentry(ctx, "Unhandled error in CreateCarrierGroups validation", zap.Error(err))
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "An unexpected error occurred.",
		})
	}

	var successfullyProcessedCount int
	var processingErrors []map[string]any

	for _, carrierGroup := range req.CarrierGroups {
		err := upsertCarrierGroupAndProcessCarriers(
			ctx,
			carrierGroup,
			req.ServiceID,
			integration.ID,
			&processingErrors,
		)
		if err == nil {
			successfullyProcessedCount++
		}
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"message":                    "Carrier group processing complete.",
		"successfullyProcessedCount": successfullyProcessedCount,
		"processingErrors":           processingErrors,
	})
}

// parseAndValidateRequest parses the request body, validates it, and retrieves/creates the necessary integration.
// It returns the parsed request, the integration, and an error if any step fails.
func parseAndValidateRequest(c *fiber.Ctx) (CreationRequest, models.Integration, error) {
	ctx := c.UserContext()
	var req CreationRequest
	var integration models.Integration

	if err := c.BodyParser(&req); err != nil {
		log.Error(ctx, "Failed to parse request body", zap.Error(err))
		return req, integration, fiber.NewError(
			fiber.StatusBadRequest,
			fmt.Sprintf("Invalid JSON body: %s", err.Error()),
		)
	}

	isValid, err := helpers.ValidateInternalDrumkitKey(ctx, req.InternalDrumkitKey)
	if !isValid {
		return req, integration, fiber.NewError(fiber.StatusUnauthorized, err.Error())
	}

	if req.ServiceID == 0 {
		log.Warn(ctx, "Missing ServiceID in request body")
		return req, integration, fiber.NewError(fiber.StatusBadRequest, "Missing ServiceID in request body")
	}

	if req.IntegrationName == "" {
		log.Warn(ctx, "Missing integration name in request body")
		return req, integration, fiber.NewError(fiber.StatusBadRequest, "Missing integration name in request body")
	}

	// Carrier groups are only supported for Trifecta and ProdDemo
	if req.IntegrationName != string(models.Trifecta) && req.IntegrationName != string(models.ProdDemo) {
		log.Warn(ctx, "Unsupported integration name", zap.String("integrationName", req.IntegrationName))
		return req, integration, fiber.NewError(
			fiber.StatusBadRequest,
			"Unsupported integration name. Currently, only 'trifecta' and 'proddemo' are supported.",
		)
	}

	// Validate that the integration exists and is of type CustomerType, or create it.
	var getIntegrationErr error
	integration, getIntegrationErr = integrationDB.GetByName(
		ctx,
		req.ServiceID,
		models.IntegrationName(req.IntegrationName),
	)
	if getIntegrationErr != nil {
		if errors.Is(getIntegrationErr, gorm.ErrRecordNotFound) {
			log.Info(
				ctx,
				"Customer integration not found, creating new one",
				zap.String("integrationName", req.IntegrationName),
				zap.Uint("serviceID", req.ServiceID),
			)
			newIntegration := &models.Integration{
				Name:          models.IntegrationName(req.IntegrationName),
				Type:          models.CustomerType,
				ServiceID:     req.ServiceID,
				IsServiceWide: true,
			}
			if createErr := integrationDB.Create(ctx, newIntegration); createErr != nil {
				log.Error(
					ctx,
					"Failed to create customer integration",
					zap.String("integrationName", req.IntegrationName),
					zap.Uint("serviceID", req.ServiceID),
					zap.Error(createErr),
				)
				return req, integration, fiber.NewError(
					fiber.StatusInternalServerError,
					fmt.Sprintf("Failed to create customer integration '%s'.", req.IntegrationName),
				)
			}
			integration = *newIntegration
		} else {
			log.Error(
				ctx,
				"Failed to get integration by name during validation",
				zap.String("integrationName", req.IntegrationName),
				zap.Uint("serviceID", req.ServiceID),
				zap.Error(getIntegrationErr),
			)
			return req, integration, fiber.NewError(
				fiber.StatusInternalServerError,
				"Failed to validate integration.",
			)
		}
	}

	if integration.Type != models.CustomerType {
		log.Warn(
			ctx,
			"Integration is not of customer type",
			zap.String("integrationName", req.IntegrationName),
			zap.String("integrationType", string(integration.Type)),
		)
		return req, integration, fiber.NewError(
			fiber.StatusBadRequest,
			fmt.Sprintf("Integration '%s' is not of type 'customer'.", req.IntegrationName),
		)
	}

	return req, integration, nil
}

// upsertCarrierGroupAndProcessCarriers handles the database operations for a single carrier group
// and its carriers. It returns true if successful, false otherwise.
func upsertCarrierGroupAndProcessCarriers(
	ctx context.Context,
	carrierGroup CarrierGroup,
	serviceID uint,
	integrationID uint,
	processingErrors *[]map[string]any,
) error {

	dbCarrierGroup := models.CarrierGroup{
		Name:      carrierGroup.Name,
		Email:     carrierGroup.Email,
		ServiceID: serviceID,
	}

	if err := carrierGroupDB.Upsert(ctx, &dbCarrierGroup); err != nil {
		logFields := []zap.Field{zap.String("groupName", carrierGroup.Name), zap.Error(err)}
		errorEntry := map[string]any{
			"groupName": carrierGroup.Name,
			"error":     err.Error(),
		}

		recordAndLogProcessingError(
			ctx,
			processingErrors,
			false,
			"Error upserting carrier group",
			logFields,
			errorEntry,
		)

		return fmt.Errorf("error upserting carrier group %s: %w", carrierGroup.Name, err)
	}

	var tmsCarriersToAssociate []*models.TMSCarrier
	for _, carrierInfo := range carrierGroup.Carriers {
		tmsCarrier, err := getOrCreateTMSCarrierForProcessing(
			ctx,
			carrierInfo,
			carrierGroup.Name,
			serviceID,
			integrationID,
			processingErrors,
		)
		if err == nil && tmsCarrier != nil {
			tmsCarriersToAssociate = append(tmsCarriersToAssociate, tmsCarrier)
		}
	}

	if len(tmsCarriersToAssociate) > 0 {
		db := rds.WithContext(ctx)
		err := db.Model(&dbCarrierGroup).Association("TMSCarriers").Replace(tmsCarriersToAssociate)
		if err != nil {
			logFields := []zap.Field{
				zap.String("groupName", dbCarrierGroup.Name),
				zap.Error(err),
			}
			errorEntry := map[string]any{
				"groupName": dbCarrierGroup.Name,
				"error":     err.Error(),
			}

			recordAndLogProcessingError(
				ctx,
				processingErrors,
				false,
				"Error associating TMS carriers with group",
				logFields,
				errorEntry,
			)

			return fmt.Errorf("error associating TMS carriers with group %s: %w", dbCarrierGroup.Name, err)
		}
	}
	return nil
}

// getOrCreateTMSCarrierForProcessing handles fetching or creating a TMSCarrier for a given CarrierInfo.
// Returns the TMSCarrier or nil if an error prevents its retrieval or creation.
func getOrCreateTMSCarrierForProcessing(
	ctx context.Context,
	carrierInfo CarrierInfo,
	groupName string,
	serviceID uint,
	integrationID uint,
	processingErrors *[]map[string]any,
) (*models.TMSCarrier, error) {

	if carrierInfo.Email == "" {
		const errMsg = "carrier email is empty"
		logFields := []zap.Field{
			zap.String("groupName", groupName),
			zap.String("carrierName", carrierInfo.Name),
		}
		errorEntry := map[string]any{
			"groupName":   groupName,
			"carrierName": carrierInfo.Name,
			"error":       errMsg,
		}

		recordAndLogProcessingError(
			ctx,
			processingErrors,
			true,
			"Skipping carrier with empty email for group",
			logFields,
			errorEntry,
		)

		return nil, errors.New(errMsg)
	}

	tmsCarrier, err := tmsCarrierDB.GetByEmail(ctx, serviceID, carrierInfo.Email)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			newTMSCarrier := models.TMSCarrier{
				TMSIntegrationID: integrationID,
				ServiceID:        serviceID,
				CompanyCoreInfo: models.CompanyCoreInfo{
					Name:          carrierInfo.Name,
					Email:         carrierInfo.Email,
					ExternalTMSID: carrierInfo.Email,
				},
				Emails: []string{carrierInfo.Email},
			}

			if createErr := tmsCarrierDB.CreateCarrier(ctx, &newTMSCarrier); createErr != nil {
				logFields := []zap.Field{
					zap.String("carrierEmail", carrierInfo.Email),
					zap.String("groupName", groupName),
					zap.Error(createErr),
				}
				errorEntry := map[string]any{
					"groupName":    groupName,
					"carrierEmail": carrierInfo.Email,
					"error":        createErr.Error(),
				}

				recordAndLogProcessingError(
					ctx,
					processingErrors,
					false,
					"Error creating TMS carrier",
					logFields,
					errorEntry,
				)

				return nil, fmt.Errorf(
					"failed to create new TMS carrier %s for group %s: %w",
					carrierInfo.Email,
					groupName,
					createErr,
				)
			}

			return &newTMSCarrier, nil
		}

		// Non Gorm record not found error from tmsCarrierDB.GetByEmail
		logFields := []zap.Field{
			zap.String("carrierEmail", carrierInfo.Email),
			zap.String("groupName", groupName),
			zap.Error(err),
		}
		errorEntry := map[string]any{
			"groupName":    groupName,
			"carrierEmail": carrierInfo.Email,
			"error":        err.Error(),
		}

		recordAndLogProcessingError(
			ctx,
			processingErrors,
			false,
			"Error fetching TMS carrier",
			logFields,
			errorEntry,
		)

		return nil, fmt.Errorf("failed to get TMS carrier %s for group %s: %w", carrierInfo.Email, groupName, err)
	}

	if tmsCarrier.Name != carrierInfo.Name {
		log.Info(
			ctx,
			"Updating carrier name",
			zap.String("carrierEmail", carrierInfo.Email),
			zap.String("oldName", tmsCarrier.Name),
			zap.String("newName", carrierInfo.Name),
		)
		tmsCarrier.Name = carrierInfo.Name
		_ = rds.WithContext(ctx).Save(tmsCarrier).Error
	}

	return tmsCarrier, nil
}

// recordAndLogProcessingError abstracts the logic for logging a processing error/warning
// and appending it to the list of processing errors.
func recordAndLogProcessingError(
	ctx context.Context,
	processingErrors *[]map[string]any,
	isWarning bool,
	logMessage string,
	logFields []zap.Field,
	errorEntry map[string]any,
) {
	if isWarning {
		log.Warn(ctx, logMessage, logFields...)
	} else {
		log.Error(ctx, logMessage, logFields...)
	}

	*processingErrors = append(*processingErrors, errorEntry)
}
