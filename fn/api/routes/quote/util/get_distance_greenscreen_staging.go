package quoteutil

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/drumkitai/drumkit/common/integrations/pricing/greenscreens"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
)

func GetDistanceWithStagingGreenscreens(
	ctx context.Context,
	originCity,
	originState,
	destinationCity,
	destinationState string,
) (float64, error) {

	gsStaging, err := integrationDB.GetInternalGreenscreensStaging(ctx)
	if err != nil {
		return 0, fmt.Errorf("error getting Greenscreens staging integration: %w", err)
	}

	if gsStaging.Name != models.Greenscreens {
		return 0, errors.New("greenscreens integration not found")
	}

	client, err := greenscreens.CachedClient(ctx, gsStaging)
	if err != nil {
		return 0, fmt.Errorf("unable to create service greenscreens client: %w", err)
	}

	var stops = []greenscreens.RatePredictionStop{
		{
			Order:   1,
			Country: "US",
			State:   originState,
			City:    originCity,
			Zip:     "",
		},
		{
			Order:   2,
			Country: "US",
			State:   destinationState,
			City:    destinationCity,
			Zip:     "",
		},
	}

	laneRatePrediction, err := client.GetLaneRatePrediction(
		ctx,
		gsStaging.ServiceID,
		"",
		&greenscreens.GetRatePredictionRequest{
			PickupDateTime: time.Now(),
			TransportType:  greenscreens.TransportType(models.VanTransportType),
			Stops:          stops,
			Currency:       greenscreens.Currency("USD"),
		},
	)
	if err != nil {
		return 0, fmt.Errorf("failed to get greenscreens rate prediction for distance caching: %w", err)
	}

	return laneRatePrediction.Distance, nil
}
