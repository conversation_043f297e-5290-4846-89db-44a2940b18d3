package quoteutil

import (
	"context"
	"errors"
	"fmt"

	"github.com/jftuga/geodist"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/log"
)

func GetDistanceWithCoordinates(
	ctx context.Context,
	originCity,
	originState,
	destinationCity,
	destinationState string,
) (dist float64, err error) {

	originLocation, err := helpers.AwsLocationLookup(ctx, originCity, originState, "")
	if err != nil {
		log.Warn(ctx, "failed to lookup origin location", zap.Error(err))
		return 0, fmt.Errorf("failed to lookup origin location: %w", err)
	}

	if len(originLocation.Results) == 0 {
		return 0, errors.New("no results found for origin location")
	}

	originCoords := geodist.Coord{
		Lon: originLocation.Results[0].Place.Geometry.Point[0],
		Lat: originLocation.Results[0].Place.Geometry.Point[1],
	}

	destinationLocation, err := helpers.AwsLocationLookup(ctx, destinationCity, destinationState, "")
	if err != nil {
		log.Warn(ctx, "failed to lookup destination location", zap.Error(err))
		return 0, fmt.Errorf("failed to lookup destination location: %w", err)
	}

	if len(destinationLocation.Results) == 0 {
		return 0, errors.New("no results found for destination location")
	}

	destinationCoords := geodist.Coord{
		Lon: destinationLocation.Results[0].Place.Geometry.Point[0],
		Lat: destinationLocation.Results[0].Place.Geometry.Point[1],
	}

	hDistance, _ := geodist.HaversineDistance(originCoords, destinationCoords)

	return hDistance, nil
}
