package quoteutil

import (
	"context"
	"errors"
	"time"

	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	tmsCustomerDB "github.com/drumkitai/drumkit/common/rds/tms_customers"
)

// PrepareCustomerAndDates handles looking up the customer (by external TMS ID)
// and defaulting pickup/delivery dates for quick quote and batch quote flows.
func PrepareCustomerAndDates(
	ctx context.Context,
	service models.Service,
	customerName string,
	pickupDate time.Time,
	deliveryDate time.Time,
) (models.TMSCustomer, time.Time, time.Time, error) {

	var customer models.TMSCustomer
	if customerName != "" {
		tmsIntegrations, err := integrationDB.GetTMSListByServiceID(ctx, service.ID)
		if err != nil {
			return customer, pickupDate, deliveryDate, err
		}

		if len(tmsIntegrations) == 0 {
			return customer, pickupDate, deliveryDate, ErrNoTMSIntegrations
		}

		customer, err = tmsCustomerDB.GetByExternalTMSID(ctx, tmsIntegrations[0].ID, customerName)
		if err != nil {
			return customer, pickupDate, deliveryDate, err
		}
	}

	// If pickup date isn't provided, set it to:
	// - today + 1 day if delivery date also isn't provided
	// - delivery date - 1 day otherwise, assuming 1 day delivery
	if pickupDate.IsZero() {
		if !deliveryDate.IsZero() {
			pickupDate = deliveryDate.AddDate(0, 0, -1)
		} else {
			pickupDate = time.Now().AddDate(0, 0, 1)
		}
	}

	// If delivery date isn't provided, set it to:
	// - today + 2 days if pickup date also isn't provided
	// - pickup date + 1 day otherwise, assuming 1 day delivery
	if deliveryDate.IsZero() {
		if !pickupDate.IsZero() {
			deliveryDate = pickupDate.AddDate(0, 0, 1)
		} else {
			deliveryDate = time.Now().AddDate(0, 0, 2)
		}
	}

	return customer, pickupDate, deliveryDate, nil
}

var ErrNoTMSIntegrations = errors.New("no TMS integrations found")
