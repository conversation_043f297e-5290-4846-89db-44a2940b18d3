package quote

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/integrations/pricing/dat"
	"github.com/drumkitai/drumkit/common/integrations/pricing/greenscreens"
	"github.com/drumkitai/drumkit/common/integrations/pricing/truckstop"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	quoteDB "github.com/drumkitai/drumkit/common/rds/quick_quote"
)

const (
	CountryUS     CountryName = "US"
	CountryCanada CountryName = "CA"
)

type (
	CountryName string

	SelectedRateName string

	RateData struct {
		ExternalID      string
		TargetBuyRate   float64
		LowBuyRate      float64
		HighBuyRate     float64
		StartBuyRate    float64
		FuelRate        float64
		Distance        float64
		ConfidenceLevel float64
	}
)

var ErrNoConfidentQuote = errors.New("unable to give a confident quote at the moment")

// Enum for rates that can be selected as default
const (
	Network     SelectedRateName = "NETWORK"
	BuyPower    SelectedRateName = "BUYPOWER"
	Booked      SelectedRateName = "TRUCKSTOP_BOOKED"
	Posted      SelectedRateName = "TRUCKSTOP_POSTED"
	DATRateView SelectedRateName = "DAT_RATE_VIEW"
)

func FetchTruckStopQuotes(
	ctx context.Context,
	service models.Service,
	integration models.Integration,
	email string,
	loadID string,
	origin truckstop.LocationDetails,
	destination truckstop.LocationDetails,
	transportType models.TransportType,
	pickupDate time.Time,
	deliveryDate time.Time,
	numStopsInBetween int,
) (*truckstop.BookedRateEstimateResp, *truckstop.PostedRateResp, error) {

	client, err := truckstop.New(ctx, integration)
	if err != nil {
		return nil, nil, fmt.Errorf("unable to create service truckstop client: %w", err)
	}

	// TODO : need to confirm transportation type
	var transportationMode string
	var equipmentCode string
	switch transportType {
	case models.LTLTransportType:
		transportationMode = "LTL"
		equipmentCode = "V"
	case models.VanTransportType:
		transportationMode = "TL"
		equipmentCode = "V"
	case models.FlatbedTransportType:
		transportationMode = "TL"
		equipmentCode = "F"
	case models.ReeferTransportType:
		transportationMode = "TL"
		equipmentCode = "R"
	case models.FTLTransportType:
		transportationMode = "TL"
		equipmentCode = "V"
	}

	if loadID == "" {
		loadID = uuid.New().String()
	}

	bookedPostReq := &truckstop.BookedRateTrendline{
		LoadID:           loadID,
		ShipDateTime:     pickupDate.Format(time.RFC3339),
		DeliveryDateTime: deliveryDate.Format(time.RFC3339),
		RateDateTime:     time.Now().Format(time.RFC3339),
		BookedDateTime:   time.Now().Format(time.RFC3339),
		Origin: truckstop.LocationDetails{
			// Address:   origin.Address,
			// Latitude:  origin.Latitude,
			// Longitude: origin.Longitude,
			ZipCode:   origin.ZipCode,
			City:      origin.City,
			StateCode: origin.StateCode,
		},
		Destination: truckstop.LocationDetails{
			// Address:   destination.Address,
			// Latitude:  destination.Latitude,
			// Longitude: destination.Longitude,
			ZipCode:   destination.ZipCode,
			City:      destination.City,
			StateCode: destination.StateCode,
		},
		TransportationMode: transportationMode,
		EquipmentCode:      equipmentCode,
		MultiPickDrop:      helpers.Ternary(numStopsInBetween == 0, "NO", "YES"),
	}

	postedPostReq := &truckstop.PostedRateTrendline{
		LoadID:           loadID,
		EntryDateTime:    time.Now().Format(time.RFC3339),
		PostedDateTime:   time.Now().Format(time.RFC3339),
		PickUpDateTime:   pickupDate.Format(time.RFC3339),
		DeliveryDateTime: deliveryDate.Format(time.RFC3339),
		Origin: truckstop.LocationDetails{
			Address:   origin.Address,
			Latitude:  origin.Latitude,
			Longitude: origin.Longitude,
			ZipCode:   origin.ZipCode,
			City:      origin.City,
			StateCode: origin.StateCode,
		},
		Destination: truckstop.LocationDetails{
			Address:   destination.Address,
			Latitude:  destination.Latitude,
			Longitude: destination.Longitude,
			ZipCode:   destination.ZipCode,
			City:      destination.City,
			StateCode: destination.StateCode,
		},
		Stops:              numStopsInBetween,
		TransportationMode: transportationMode,
		EquipmentCode:      equipmentCode,
		FuelPricePerGallon: 2.5, // TODO: actually get a real value here
	}

	bookedRateEstimate, err := client.GetBookedRate(ctx, service.ID, email, bookedPostReq)
	if err != nil {
		return nil, nil, err
	}

	postedRate, err := client.GetPostedRate(ctx, service.ID, email, postedPostReq)
	if err != nil {
		return nil, nil, err
	}

	return bookedRateEstimate, &postedRate, nil
}

func FetchGreenscreensInfo(
	ctx context.Context,
	service models.Service,
	integration models.Integration,
	stops []models.Stop,
	email string,
	transportType models.TransportType,
	pickupDate time.Time,
	isZipCodeLookup bool,
) (*greenscreens.RatePredictionDetail, *greenscreens.RatePredictionDetail, []greenscreens.RatePredictionStop, error) {

	var (
		laneRatePrediction        = new(greenscreens.RatePredictionDetail)
		networkLaneRatePrediction = new(greenscreens.RatePredictionDetail)
	)

	var client greenscreens.API
	var err error

	client, err = greenscreens.CachedClient(ctx, integration)
	if err != nil {
		return nil, nil, nil, fmt.Errorf("unable to create service greenscreens client: %w", err)
	}

	var ratePredictionStops []greenscreens.RatePredictionStop
	for _, stop := range stops {
		ratePredictionStops = append(ratePredictionStops, greenscreens.RatePredictionStop{
			Order:   stop.StopNumber,
			Country: stop.Address.Country,
			State:   stop.Address.State,
			City:    stop.Address.City,
			Zip:     helpers.Ternary(isZipCodeLookup, stop.Address.Zip, ""),
		})
	}

	// We need to fetch the confidence level, which represents the likelihood of successfully booking a truck
	// for that rate before we make pricing predictions.
	laneRatePrediction, err = client.GetLaneRatePrediction(
		ctx,
		service.ID,
		email,
		&greenscreens.GetRatePredictionRequest{
			PickupDateTime: pickupDate,
			TransportType:  greenscreens.TransportType(transportType),
			Stops:          ratePredictionStops,
			Currency:       greenscreens.Currency("USD"),
		},
	)
	if err != nil {
		return nil, nil, nil, fmt.Errorf("Greenscreens.GetLaneRatePrediction failed: %w", err)
	}

	networkLaneRatePrediction, err = client.GetNetworkLaneRatePrediction(
		ctx,
		service.ID,
		email,
		&greenscreens.GetRatePredictionRequest{
			PickupDateTime: pickupDate,
			TransportType:  greenscreens.TransportType(transportType),
			Stops:          ratePredictionStops,
			Currency:       greenscreens.Currency("USD"),
		})
	if err != nil {
		return nil, nil, nil, fmt.Errorf("Greenscreens.GetNetworkLaneRatePrediction failed: %w", err)
	}

	if service.QuickQuoteConfig != nil {
		lowConfidenceLevel := service.QuickQuoteConfig.LowConfidenceThreshold
		omitLowConfidenceLevel := service.QuickQuoteConfig.OmitUnderLowThreshold

		if omitLowConfidenceLevel &&
			laneRatePrediction.ConfidenceLevel < lowConfidenceLevel &&
			networkLaneRatePrediction.ConfidenceLevel < lowConfidenceLevel {
			return nil, nil, nil, ErrNoConfidentQuote
		}
	}

	return laneRatePrediction, networkLaneRatePrediction, ratePredictionStops, nil
}

func GetDATLaneRate(
	ctx context.Context,
	integration models.Integration,
	datEmailAddress string,
	req *[]dat.RateRequest,
) (*dat.GetLaneRateResponse, error) {

	client, err := dat.New(ctx, integration, datEmailAddress)
	if err != nil {
		return nil, fmt.Errorf("unable to create DAT client: %w", err)
	}

	resp, err := client.GetLaneRate(ctx, *req)
	if err != nil {
		return nil, fmt.Errorf("DAT GetLaneRate failed: %w", err)
	}

	return resp, nil
}

func GetGSMostConfidentQuote(
	laneRatePrediction *greenscreens.RatePredictionDetail,
	networkLaneRatePrediction *greenscreens.RatePredictionDetail,
) (*RateData, SelectedRateName) {

	var selectedRateName SelectedRateName
	selectedRatePrediction := new(greenscreens.RatePredictionDetail)

	if laneRatePrediction.ConfidenceLevel >= 75 {
		if networkLaneRatePrediction.ConfidenceLevel >= 75 {
			// Both have sufficient confidence, select the one with the lower TargetBuyRate
			if laneRatePrediction.TargetBuyRate < networkLaneRatePrediction.TargetBuyRate {
				selectedRatePrediction = laneRatePrediction
				selectedRateName = BuyPower
			} else {
				selectedRatePrediction = networkLaneRatePrediction
				selectedRateName = Network
			}
		} else {
			// Only laneRatePrediction has sufficient confidence
			selectedRatePrediction = laneRatePrediction
			selectedRateName = BuyPower
		}
	} else if networkLaneRatePrediction.ConfidenceLevel >= 75 {
		// Only networkLaneRatePrediction has sufficient confidence
		selectedRatePrediction = networkLaneRatePrediction
		selectedRateName = Network
	}

	return &RateData{
		ExternalID:      selectedRatePrediction.ID,
		TargetBuyRate:   selectedRatePrediction.TargetBuyRate,
		LowBuyRate:      selectedRatePrediction.LowBuyRate,
		HighBuyRate:     selectedRatePrediction.HighBuyRate,
		StartBuyRate:    selectedRatePrediction.StartBuyRate,
		FuelRate:        selectedRatePrediction.FuelRate,
		Distance:        selectedRatePrediction.Distance,
		ConfidenceLevel: float64(selectedRatePrediction.ConfidenceLevel),
	}, selectedRateName
}

func CreateQuoteRecord(
	ctx context.Context,
	service models.Service,
	userID,
	emailID uint,
	_ string,
	quoteRequestID uint,
	stops []models.Stop,
	transportType models.TransportType,
	pickupDate,
	deliveryDate time.Time,
	rateData RateData,
	targetSellCost,
	minTargetSellCost,
	maxTargetSellCost float64,
	source models.QuoteSource,
	typeInSource models.QuoteTypeInSource,
	datMetadata *models.QuoteDATMetadata,
) *models.QuickQuote {

	quoteRecord := models.QuickQuote{
		ExternalID:     rateData.ExternalID,
		ServiceID:      service.ID,
		UserID:         userID,
		QuoteRequestID: quoteRequestID,
		Pipeline:       models.APIPipeline,
		QuoteLoadInfo: models.QuoteLoadInfo{
			TransportType: transportType,
			Distance:      rateData.Distance,
			PickupDate:    models.NullTime{Time: pickupDate, Valid: true},
			DeliveryDate:  models.NullTime{Time: deliveryDate, Valid: true},
		},
		TargetBuyRate:     rateData.TargetBuyRate,
		LowBuyRate:        rateData.LowBuyRate,
		HighBuyRate:       rateData.HighBuyRate,
		StartBuyRate:      rateData.StartBuyRate,
		FuelRate:          rateData.FuelRate,
		ConfidenceLevel:   rateData.ConfidenceLevel,
		Currency:          "USD",
		TotalCost:         targetSellCost,
		MinMarkup:         1.07,
		MaxMarkup:         1.10,
		MinTargetSellCost: minTargetSellCost,
		MaxTargetSellCost: maxTargetSellCost,
		Source:            source,
		TypeInSource:      typeInSource,
	}

	if emailID != 0 {
		quoteRecord.LookedAtEmailID = emailID
	}

	if datMetadata != nil {
		quoteRecord.DATTimeframe = datMetadata.DATTimeframe
		quoteRecord.DATOriginName = datMetadata.DATOriginName
		quoteRecord.DATOriginType = datMetadata.DATOriginType
		quoteRecord.DATDestinationName = datMetadata.DATDestinationName
		quoteRecord.DATDestinationType = datMetadata.DATDestinationType
	}

	quoteRecord.Stops = stops
	quoteRecord.PickupLocation = stops[0].Address
	quoteRecord.DeliveryLocation = stops[len(stops)-1].Address

	if rateData.Distance > 0 {
		quoteRecord.Distance = rateData.Distance
	}

	if err := quoteDB.Create(ctx, &quoteRecord); err != nil {
		log.Error(
			ctx,
			"failed to create quick quote record in DB",
			zap.Any("record", quoteRecord),
			zap.Error(err),
		)
	}

	return &quoteRecord
}

func ParseGSStops(gsStops []greenscreens.RatePredictionStop) []models.Stop {
	var stops []models.Stop

	// TODO: Stop number should be explicitly set in the frontend, although index works too.
	for i, stop := range gsStops {
		currentStop := models.Stop{
			StopNumber: i,
			Order:      i,
			Address: models.Address{
				Country: stop.Country,
				City:    stop.City,
				State:   stop.State,
				Zip:     stop.Zip,
			},
		}

		stops = append(stops, currentStop)
	}

	return stops
}
