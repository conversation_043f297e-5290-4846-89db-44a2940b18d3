package quote

import (
	"context"
	"errors"
	"testing"

	"github.com/aws/aws-sdk-go-v2/service/location"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/drumkitai/drumkit/common/helpers"
)

func TestValidateLocation(t *testing.T) {
	ctx := context.Background()

	t.Run("success - map city/state to zip", func(t *testing.T) {
		// Create validator with mock functions
		validator := NewLocationValidator(
			WithAwsLookupZipFunc(func(_ context.Context, _, _ string) (string, error) {
				return "12345", nil
			}),
			WithUspsLookupCityStateFunc(func(_ context.Context, _, _ string) (helpers.CityStateLookupResponse, error) {
				return helpers.CityStateLookupResponse{}, nil
			}),
		)

		city, state, zip, country := "TestCity", "TS", "", CountryUS
		err := validator.ValidateLocation(ctx, &city, &state, &zip, &country)
		require.NoError(t, err)
		assert.Equal(t, "12345", zip)
		assert.Equal(t, "TestCity", city)
		assert.Equal(t, "TS", state)
	})

	t.Run("success - map zip to city/state", func(t *testing.T) {
		// Create validator with mock functions
		validator := NewLocationValidator(
			WithAwsLookupZipFunc(func(_ context.Context, _, _ string) (string, error) {
				return "", nil
			}),
			WithUspsLookupCityStateFunc(func(_ context.Context, _, _ string) (helpers.CityStateLookupResponse, error) {
				return helpers.CityStateLookupResponse{
					ZipCode: struct {
						Zip5  string                `xml:"Zip5"`
						City  string                `xml:"City"`
						State string                `xml:"State"`
						Error *helpers.ErrorDetails `xml:"Error"`
					}{
						Zip5:  "12345",
						City:  "TestCity",
						State: "TS",
						Error: nil,
					},
				}, nil
			}),
		)

		city, state, zip, country := "", "", "12345", CountryUS
		err := validator.ValidateLocation(ctx, &city, &state, &zip, &country)
		require.NoError(t, err)
		assert.Equal(t, "TestCity", city)
		assert.Equal(t, "TS", state)
		assert.Equal(t, "12345", zip)
	})

	t.Run("success - Canada postal code lookup", func(t *testing.T) {
		// Create validator with mock Canada location function that returns valid data
		// This bypasses the AWS dependency and tests the success path directly
		validator := NewLocationValidator(
			WithCanadaLocationFunc(func(context.Context, *string) (string, string, error) {
				return "Toronto", "Ontario", nil
			}),
		)

		city, state, zip, country := "", "", "M5V3A8", CountryCanada
		err := validator.ValidateLocation(ctx, &city, &state, &zip, &country)
		require.NoError(t, err)
		assert.Equal(t, "Toronto", city)
		assert.Equal(t, "Ontario", state)
		assert.Equal(t, "M5V3A8", zip)
		assert.Equal(t, CountryCanada, country)
	})

	t.Run("error - Canada postal code lookup with AWS service error", func(t *testing.T) {
		// Create validator with mock AWS location function that returns an error
		// This tests the error handling path since we can't easily mock the AWS SDK types
		validator := NewLocationValidator(
			WithAWSLocationFunc(
				func(_ context.Context, _, _, _ string) (*location.SearchPlaceIndexForTextOutput, error) {
					return nil, errors.New("AWS location service unavailable")
				}),
		)

		city, state, zip, country := "", "", "M5V3A8", CountryCanada
		err := validator.ValidateLocation(ctx, &city, &state, &zip, &country)
		require.Error(t, err)
		assert.Contains(t, err.Error(), "error mapping canada postal code to city/state")
	})

	t.Run("error - invalid city/state", func(t *testing.T) {
		// Create validator with mock functions
		validator := NewLocationValidator(
			WithAwsLookupZipFunc(func(_ context.Context, _, _ string) (string, error) {
				return "", errors.New("invalid city/state")
			}),
		)

		city, state, zip, country := "InvalidCity", "IS", "", CountryUS
		err := validator.ValidateLocation(ctx, &city, &state, &zip, &country)
		require.Error(t, err)
		assert.Empty(t, zip)
	})

	t.Run("error - no valid zips found", func(t *testing.T) {
		// Create validator with mock functions
		validator := NewLocationValidator(
			WithAwsLookupZipFunc(func(_ context.Context, _, _ string) (string, error) {
				return "", nil
			}),
		)

		city, state, zip, country := "NoZipCity", "NZ", "", CountryUS
		err := validator.ValidateLocation(ctx, &city, &state, &zip, &country)
		require.Error(t, err)
		assert.Empty(t, zip)
	})

	t.Run("error - invalid zip", func(t *testing.T) {
		// Create validator with mock functions
		validator := NewLocationValidator(
			WithUspsLookupCityStateFunc(func(context.Context, string, string) (helpers.CityStateLookupResponse, error) {
				return helpers.CityStateLookupResponse{}, errors.New("invalid zip")
			}),
		)

		city, state, zip, country := "", "", "99999", CountryUS
		err := validator.ValidateLocation(ctx, &city, &state, &zip, &country)
		require.Error(t, err)
		assert.Empty(t, city)
		assert.Empty(t, state)
	})
}

func TestValidateLocationConvenience(t *testing.T) {
	ctx := context.Background()

	t.Run("convenience function works with defaults", func(t *testing.T) {
		// This test uses the convenience function which should use default lookup functions
		// We'll test with valid inputs that don't require lookups
		city, state, zip, country := "TestCity", "TS", "12345", CountryUS
		err := ValidateLocation(ctx, &city, &state, &zip, &country)
		require.NoError(t, err)
		assert.Equal(t, "TestCity", city)
		assert.Equal(t, "TS", state)
		assert.Equal(t, "12345", zip)
	})
}
