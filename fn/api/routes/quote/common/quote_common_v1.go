package quote

import (
	"context"
	"fmt"
	"time"

	"github.com/drumkitai/drumkit/common/integrations/pricing/greenscreens"
	"github.com/drumkitai/drumkit/common/integrations/pricing/truckstop"
	"github.com/drumkitai/drumkit/common/models"
)

func GetBestTruckStopQuoteV1(bookedQuote *truckstop.BookedRateEstimateResp, postedQuote *truckstop.PostedRateResp) (
	string, SelectedRateName) {
	var selectedRateName SelectedRateName

	if bookedQuote.DataRecordsScore > postedQuote.DataRecordsScore {
		selectedRateName = Booked
	} else {
		selectedRateName = Posted
	}

	return "", selectedRateName
}

func ParseGSStopsV1(gsStops []greenscreens.RatePredictionStop) []models.Stop {
	var stops []models.Stop

	// TODO: Stop number should be explicitly set in the frontend, although index works too.
	for i, stop := range gsStops {
		currentStop := models.Stop{
			StopNumber: i,
			Order:      i,
			Address: models.Address{
				Country: stop.Country,
				City:    stop.City,
				State:   stop.State,
				Zip:     stop.Zip,
			},
		}

		stops = append(stops, currentStop)
	}

	return stops
}

func FetchGreenscreensInfoV1(
	ctx context.Context,
	service models.Service,
	integration models.Integration,
	ratePredictionStops []greenscreens.RatePredictionStop,
	email string,
	transportType models.TransportType,
	pickupDate time.Time,
) (*greenscreens.RatePredictionDetail, *greenscreens.RatePredictionDetail, []greenscreens.RatePredictionStop, error) {

	var (
		laneRatePrediction        = new(greenscreens.RatePredictionDetail)
		networkLaneRatePrediction = new(greenscreens.RatePredictionDetail)
	)

	var client greenscreens.API
	var err error

	client, err = greenscreens.CachedClient(ctx, integration)
	if err != nil {
		return nil, nil, nil, fmt.Errorf("unable to create service greenscreens client: %w", err)
	}

	// We need to fetch the confidence level, which represents the likelihood of successfully booking a truck
	// for that rate before we make pricing predictions.
	laneRatePrediction, err = client.GetLaneRatePrediction(
		ctx,
		service.ID,
		email,
		&greenscreens.GetRatePredictionRequest{
			PickupDateTime: pickupDate,
			TransportType:  greenscreens.TransportType(transportType),
			Stops:          ratePredictionStops,
			Currency:       greenscreens.Currency("USD"),
		},
	)
	if err != nil {
		return nil, nil, nil, fmt.Errorf("Greenscreens.GetLaneRatePrediction failed: %w", err)
	}

	networkLaneRatePrediction, err = client.GetNetworkLaneRatePrediction(
		ctx,
		service.ID,
		email,
		&greenscreens.GetRatePredictionRequest{
			PickupDateTime: pickupDate,
			TransportType:  greenscreens.TransportType(transportType),
			Stops:          ratePredictionStops,
			Currency:       greenscreens.Currency("USD"),
		})
	if err != nil {
		return nil, nil, nil, fmt.Errorf("Greenscreens.GetNetworkLaneRatePrediction failed: %w", err)
	}

	if service.QuickQuoteConfig != nil {
		lowConfidenceLevel := service.QuickQuoteConfig.LowConfidenceThreshold
		omitLowConfidenceLevel := service.QuickQuoteConfig.OmitUnderLowThreshold

		if omitLowConfidenceLevel &&
			laneRatePrediction.ConfidenceLevel < lowConfidenceLevel &&
			networkLaneRatePrediction.ConfidenceLevel < lowConfidenceLevel {
			return nil, nil, nil, ErrNoConfidentQuote
		}
	}

	return laneRatePrediction, networkLaneRatePrediction, ratePredictionStops, nil
}
