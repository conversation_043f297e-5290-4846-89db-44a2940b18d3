package quote

import (
	"context"
	"fmt"
	"strings"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/redis"
	quoteutil "github.com/drumkitai/drumkit/fn/api/routes/quote/util"
)

func redisMileDistanceBetweenLocationsKey(
	originCity,
	originState,
	destinationCity,
	destinationState string,
) string {

	// Preventing mismatches such as "Opa Locka" and "Opa-Locka". This works because this function is
	// used both for getting keys to check with as well as getting the keys being set.
	originCity = strings.ReplaceAll(originCity, " ", "-")
	destinationCity = strings.ReplaceAll(destinationCity, " ", "-")

	return fmt.Sprintf(
		"mile-distance-between-locations-%s-%s-%s-%s",
		strings.ToLower(originCity),
		strings.ToLower(originState),
		strings.ToLower(destinationCity),
		strings.ToLower(destinationState),
	)
}

// This function allows both lookups by city and state or zipcode, caching results in Redis
func GetRedisMileDistanceBetweenLocations(
	ctx context.Context,
	originCity,
	originState,
	destinationCity,
	destinationState string,
) (float64, error) {

	redisKey := redisMileDistanceBetweenLocationsKey(originCity, originState, destinationCity, destinationState)

	cachedMileDistance, _, err := redis.GetKey[float64](ctx, redisKey)
	if err == nil {
		log.Info(
			ctx,
			"found mile distance between locations in Redis",
			zap.String("redisKey", redisKey),
		)
		return cachedMileDistance, nil
	}

	return 0, err
}

func GetRedisMileDistanceBetweenLocationsWithFallback(
	ctx context.Context,
	originCity,
	originState,
	destinationCity,
	destinationState string,
) (float64, error) {

	cachedDist, err := GetRedisMileDistanceBetweenLocations(
		ctx,
		originCity,
		originState,
		destinationCity,
		destinationState,
	)
	if err == nil {
		return cachedDist, nil
	}

	// If distance isn't found in cache, fallback to fetching from Greenscreens staging
	quoteMileDistance, err := quoteutil.GetDistanceWithStagingGreenscreens(
		ctx,
		originCity,
		originState,
		destinationCity,
		destinationState,
	)
	if err != nil {
		// If fetching from Greenscreens staging also fails, fallback to last resort and calculate using coordinates
		return 0, err
	}

	err = SetRedisMileDistanceBetweenLocations(
		ctx,
		originCity,
		originState,
		destinationCity,
		destinationState,
		quoteMileDistance,
	)
	if err != nil {
		log.Warn(ctx, "failed to cache mile distance between locations", zap.Error(err))
	}

	return quoteMileDistance, nil
}

func SetRedisMileDistanceBetweenLocations(
	ctx context.Context,
	originCity,
	originState,
	destinationCity,
	destinationState string,
	mileDistance float64,
) error {

	redisKey := redisMileDistanceBetweenLocationsKey(originCity, originState, destinationCity, destinationState)

	// Check if Redis is initialized
	if redis.RDB == nil {
		log.Warn(ctx, "Redis RDB is not initialized, skipping cache operation")
		return nil
	}

	// Check if mile distance is already cached
	cachedMileDistance, _, err := redis.GetKey[float64](ctx, redisKey)
	if err == nil && cachedMileDistance != 0 {
		log.Info(
			ctx,
			"found mile distance between locations in Redis",
			zap.String("redisKey", redisKey),
		)
		return nil
	}

	if err := redis.SetKey(ctx, redisKey, mileDistance, 0); err != nil {
		log.WarnNoSentry(
			ctx,
			"error setting mile distance between locations in redis",
			zap.Error(err),
			zap.Any("key", redisKey),
		)

		return err
	}

	log.Info(
		ctx,
		"mile distance between locations stored successfully on redis",
		zap.Any("redisKey", redisKey),
	)

	return nil
}
