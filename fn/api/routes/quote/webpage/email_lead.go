package quotepublic

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"os"
	"strconv"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/integrations/email/msclient"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	emailDB "github.com/drumkitai/drumkit/common/rds/email"
	"github.com/drumkitai/drumkit/fn/api/env"
)

type (
	EmailLeadBody struct {
		QuoteID uint `json:"id"`
		QuickQuoteBody
	}
)

func EmailLead(c *fiber.Ctx) error {
	var path QuickQuotePath
	var body EmailLeadBody
	if err := api.Parse(c, &path, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Any("requestBody", body))

	if body.DOB != "" {
		return c.SendStatus(http.StatusBadRequest)
	}

	if isInvalidDomain(body.Email) {
		// Because multiple 400s are possible, front-end checks for this error message in the body.
		return c.Status(http.StatusBadRequest).SendString("Invalid email.")
	}

	service, err := rds.GetServiceByNicknameWithPreload(ctx, path.Nickname)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(http.StatusUnauthorized).SendString(
				fmt.Sprintf("broker with service %s does not exist", path.Nickname))
		}

		return c.SendStatus(http.StatusInternalServerError)
	}

	if service.QuickQuoteConfig == nil {
		log.Error(ctx, "service quick quote not configured", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	// Quote ID is optional; user may request to get in touch after error during quote generation
	var quote *models.QuickQuote

	if body.QuoteID != 0 {
		var dbQuote models.QuickQuote
		if err := rds.GetByID(ctx, body.QuoteID, &dbQuote); err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return c.Status(http.StatusNotFound).
					SendString(fmt.Sprintf("quote with ID %d not found", body.QuoteID))
			}
		}

		quote = &dbQuote
		dbQuote.LeadContacted = true

		if err := rds.Update(ctx, &dbQuote); err != nil {
			log.Warn(ctx, "quoteDB error", zap.Error(err))
		}
	}

	email, err := emailLeadAndBroker(ctx, &body, quote, &service)
	if err != nil {
		log.WarnNoSentry(ctx, "error sending email to lead, trying again", zap.Error(err))
		time.Sleep(2 * time.Second)
		email, err = emailLeadAndBroker(ctx, &body, quote, &service)
		if err != nil {
			log.Error(ctx, "error sending email to lead", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}
	}

	err = emailDB.UpsertEmailAndLoads(ctx, email)
	if err != nil {
		// Fail-open because email was still sent
		log.Error(ctx, "error adding email to DB", zap.Error(err))
	}

	return c.SendStatus(http.StatusOK)
}

// Template: https://www.notion.so/axleapi/Beacon-Quick-Quote-Wicker-Park-5a6874d8deb24170935006da6eb04f99
//
// NOTE:
//   - Our Gmail @drumkit.ai domain conflicts with our Microsoft one. <NAME_EMAIL> does not exist
//     on Outlook and/or it does not have a MS365 license, sending to that account will fail.
//     Instead, send test emails to @78n517.onmicrosoft.com accounts.
//   - Our @78n517.onmicrosoft.com dev domain is a Trial tenant, so while it can receive emails, it does not have
//     permissions to send emails via the API.
func emailLeadAndBroker(
	ctx context.Context,
	body *EmailLeadBody,
	quote *models.QuickQuote,
	service *models.Service,
) (*models.Email, error) {

	pickupStop := body.Stops[0]
	dropoffStop := body.Stops[len(body.Stops)-1]
	pickupDateStr := body.PickupDate.Format("1/2/2006")
	dropoffDateStr := body.DeliveryDate.Format("1/2/2006")
	subject := fmt.Sprintf("Wicker Park Logistics Quote for %s to %s on %s",
		pickupStop.Zip, dropoffStop.Zip, pickupDateStr)

	meetingURL := "https://meetings.hubspot.com/nathan-mcguire"
	pickupStr := pickupStop.Zip
	dropoffStr := dropoffStop.Zip

	if quote != nil {
		pickupStr = quote.PickupLocation.ToString()
		dropoffStr = quote.DeliveryLocation.ToString()
	}

	// N/A if there was an error during quote generation
	rateRangeStr := "N/A"
	if quote != nil {
		rateRangeStr = fmt.Sprintf("$%s - $%s (%s)",
			strconv.FormatFloat(quote.MinTargetSellCost, 'f', 0, 64),
			strconv.FormatFloat(quote.MaxTargetSellCost, 'f', 0, 64),
			quote.Currency)
	}

	// Eventually this customization will be moved to service.QuickQuoteConfig and intake form
	signatureHTML, err := os.ReadFile("./routes/quote/webpage/customers/wickerpark-email-signature.html")
	if err != nil {
		return nil, fmt.Errorf("error reading signature HTML file: %w", err)
	}

	// Construct HTML message body
	bodyContent := fmt.Sprintf(`
		Hi %s,<br><br>
		We would love to help you with your load! Let’s find a <a href="%s">time to chat</a>!<br><br>
		<b>Your load</b><br>
		Transport type: %s<br>
		Pickup: %s on %s<br>
		Dropoff: %s on %s<br><br>
		Our rates: %s<br>
		<em style="font-size: small;">Please note that this range is intended to provide a baseline rate for the
		provided information. Please speak with a Wicker Park Logistics representative to confirm details.</em><br><br>
		Best,<br>
		Nathan<br><br>
		<div style="width: 50%%;">%s</div>
	`, body.FirstName, meetingURL, body.TransportType, pickupStr, pickupDateStr, dropoffStr,
		dropoffDateStr, rateRangeStr, string(signatureHTML))

	ccRecipients := []msclient.RecipientCollection{}
	for _, recipient := range service.QuickQuoteConfig.CC {
		if recipient != "" {
			ccRecipients = append(ccRecipients, msclient.RecipientCollection{
				EmailAddress: msclient.EmailAddress{Address: recipient},
			})
		}
	}

	// BCC sender to satisfy Microsoft constraints
	bcc := []msclient.RecipientCollection{}
	for _, recipient := range service.QuickQuoteConfig.BCC {
		if recipient != "" {
			bcc = append(bcc, msclient.RecipientCollection{
				EmailAddress: msclient.EmailAddress{Address: recipient},
			})
		}
	}

	if len(bcc) == 0 {
		bcc = append(bcc, msclient.RecipientCollection{
			EmailAddress: msclient.EmailAddress{Address: service.QuickQuoteConfig.Sender.EmailAddress},
		})
	}

	msg := msclient.Message{
		CreatedDateTime:      time.Now(),
		LastModifiedDateTime: time.Now(),
		HasAttachments:       false,
		InternetMessageID:    "",
		Subject:              subject,
		WebLink:              "",
		Categories:           []string{},
		Body: &msclient.Body{
			ContentType: "html",
			Content:     bodyContent,
		},
		Sender: msclient.Sender{
			EmailAddress: msclient.EmailAddress{
				Address: service.QuickQuoteConfig.Sender.EmailAddress,
			},
		},
		From: msclient.From{
			EmailAddress: msclient.EmailAddress{
				Address: service.QuickQuoteConfig.Sender.EmailAddress,
			},
		},
		ToRecipients: []msclient.RecipientCollection{
			{
				EmailAddress: msclient.EmailAddress{
					Name:    "",
					Address: body.Email,
				},
			},
		},
		// cc, bcc, replyTo cannot be null but it can have null values.
		CcRecipients:            ccRecipients,
		BccRecipients:           bcc,
		ReplyTo:                 ccRecipients,
		Importance:              "normal",
		InferenceClassification: "other",
		Flag:                    msclient.Flag{FlagStatus: "notFlagged"},
	}

	msClient, err := msclient.New(ctx,
		env.Vars.MicrosoftClientID, env.Vars.MicrosoftClientSecret,
		service.QuickQuoteConfig.Sender)
	if err != nil {
		return nil, fmt.Errorf(
			"error creating MS client for %s: %w",
			service.QuickQuoteConfig.Sender.EmailAddress,
			err,
		)
	}

	// Must first draft email to get its immutable ID, then send it
	// https://learn.microsoft.com/en-us/graph/outlook-immutable-id#immutable-id-with-sending-mail
	if err = msClient.DraftMessage(ctx, &msg); err != nil {
		return nil, fmt.Errorf("error drafting email: %w", err)
	}

	log.Info(ctx, "drumkit-generated message ID", zap.String("newMsgID", msg.ID))

	if err = msClient.SendMessage(ctx, &msg); err != nil {
		return nil, fmt.Errorf("error sending email: %w", err)
	}

	dbEmail := msg.ToEmailModel(ctx, service.QuickQuoteConfig.Sender)
	dbEmail.BeaconGenerated = true

	return &dbEmail, nil
}
