package quoteprivate

import (
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/integrations/email/msclient"
	"github.com/drumkitai/drumkit/common/log"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	"github.com/drumkitai/drumkit/fn/api/env"
	apiutil "github.com/drumkitai/drumkit/fn/api/util"
)

type (
	QuickQuoteReplyDraftBody struct {
		ThreadItemID string `json:"threadItemId" validate:"required"`
		DraftBody    string `json:"draftBody" validate:"required"`
	}
)

func PostQuickQuoteReplyDraft(c *fiber.Ctx) error {
	var body QuickQuoteReplyDraftBody
	var err error
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()

	body.ThreadItemID, err = apiutil.DecodeOutlookID(body.ThreadItemID)
	if err != nil {
		log.Error(ctx, "error decoding threadItemID", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	email := middleware.ClaimsFromContext(c).Email
	user, err := userDB.GetByEmail(ctx, email)
	if err != nil {
		log.Error(ctx, "could not get user from email", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	graphClient, err := msclient.New(ctx, env.Vars.MicrosoftClientID, env.Vars.MicrosoftClientSecret, &user)
	if err != nil {
		log.Error(ctx, "could not get microsoft outlook client when creating quote reply draft", zap.Error(err))
		return err
	}

	_, err = graphClient.DraftReplyAll(ctx, body.ThreadItemID, body.DraftBody)
	if err != nil {
		log.Error(ctx, "error while creating quick quote reply draft", zap.Error(err))
		return err
	}

	return c.SendStatus(http.StatusCreated)
}
