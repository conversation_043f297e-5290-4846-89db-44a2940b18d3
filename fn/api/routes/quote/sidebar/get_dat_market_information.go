package quoteprivate

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/integrations/pricing/dat"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
)

type (
	GetDATMarketInformationBody struct {
		IntegrationID uint                 `json:"integrationID"`
		TransportType models.TransportType `json:"transportType"`
		Stops         []Stop               `json:"stops"`
	}

	DATMarketInformation struct {
		StopLocation string `json:"stopLocation"`
		// Inbound
		InboundLoadToTruckRatio                string `json:"inboundLoadToTruckRatio"`
		InboundMarketConditionsIndexPrevDay    int    `json:"inboundMarketConditionsIndexPrevDay"`
		InboundMarketConditionsIndexPrevWeek   int    `json:"inboundMarketConditionsIndexPrevWeek"`
		InboundMarketConditionsForecastNextDay int    `json:"inboundMarketConditionsForecastNextDay"`
		// Outbound
		OutboundLoadToTruckRatio                string `json:"outboundLoadToTruckRatio"`
		OutboundMarketConditionsIndexPrevDay    int    `json:"outboundMarketConditionsIndexPrevDay"`
		OutboundMarketConditionsIndexPrevWeek   int    `json:"outboundMarketConditionsIndexPrevWeek"`
		OutboundMarketConditionsForecastNextDay int    `json:"outboundMarketConditionsForecastNextDay"`
	}

	GetDATMarketInformationResponse struct {
		PickupMarketInformation  DATMarketInformation `json:"pickupMarketInformation"`
		DropoffMarketInformation DATMarketInformation `json:"dropoffMarketInformation"`
	}
)

func GetDATMarketInformation(c *fiber.Ctx) error {
	var body GetDATMarketInformationBody
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Any("requestBody", body))
	userServiceID := middleware.ServiceIDFromContext(c)
	userID := middleware.UserIDFromContext(c)

	service, err := rds.GetServiceWithPreload(ctx, userServiceID)
	if err != nil {
		log.Error(ctx, "could not get service", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if !service.IsDATMarketConditionsEnabled && !service.IsDATLoadToTruckRatioEnabled {
		return c.Status(http.StatusForbidden).SendString(
			"Both DAT Market Conditions and Load to Truck Ratio are disabled for this service",
		)
	}

	integration, err := integrationDB.Get(ctx, body.IntegrationID)
	if err != nil {
		log.Error(ctx, "could not get integration", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	user, err := userDB.GetByID(ctx, userID)
	if err != nil {
		log.Error(ctx, "could not get user", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	pickupMarketInformation, dropoffMarketInformation, err := getDATMarketInformation(
		ctx,
		integration,
		user,
		service,
		body.TransportType,
		body.Stops,
	)
	if err != nil {
		log.Error(ctx, "failed to get DAT market conditions", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.Status(http.StatusOK).JSON(GetDATMarketInformationResponse{
		PickupMarketInformation:  pickupMarketInformation,
		DropoffMarketInformation: dropoffMarketInformation,
	})
}

func getDATMarketInformation(
	ctx context.Context,
	integration models.Integration,
	user models.User,
	service models.Service,
	transportType models.TransportType,
	stops []Stop,
) (DATMarketInformation, DATMarketInformation, error) {

	var err error

	if len(stops) != 2 {
		if len(stops) < 2 {
			return DATMarketInformation{},
				DATMarketInformation{},
				errors.New("at least two stops are required")
		}

		return DATMarketInformation{},
			DATMarketInformation{},
			errors.New("dat mci is not supported for multi-stop quotes")
	}

	// DAT MCI (Market Conditions Index)
	var originLoadToTruckRatio, destinationLoadToTruckRatio *dat.LocationLoadToTruckRatios
	var originMarketConditionsForecast, destinationMarketConditionsForecast *dat.LocationMarketConditionsForecast
	var originMarketConditionsIndex, destinationMarketConditionsIndex *dat.LocationMarketConditionsIndex

	// Market Conditions Base Request
	pickupMCIRequest := dat.GetMarketInformationRequest{
		AreaType:        dat.MCLocationTypeMarketArea,
		TransportType:   transportType,
		City:            stops[0].City,
		StateOrProvince: stops[0].State,
		PostalCode:      stops[0].Zip,
	}

	dropoffMCIRequest := dat.GetMarketInformationRequest{
		AreaType:        dat.MCLocationTypeMarketArea,
		TransportType:   transportType,
		City:            stops[1].City,
		StateOrProvince: stops[1].State,
		PostalCode:      stops[1].Zip,
	}

	// Market Conditions Forecast
	if service.IsDATMarketConditionsForecastEnabled {
		originMarketConditionsForecast, err = GetDATMarketConditionsForecast(
			ctx,
			integration,
			user.DATEmailAddress,
			pickupMCIRequest,
		)
		if err != nil {
			log.Error(ctx, "failed to get DAT market conditions forecast for origin", zap.Error(err))
		}

		destinationMarketConditionsForecast, err = GetDATMarketConditionsForecast(
			ctx,
			integration,
			user.DATEmailAddress,
			dropoffMCIRequest,
		)
		if err != nil {
			log.Error(ctx, "failed to get DAT market conditions forecast for destination", zap.Error(err))
		}
	}

	// Market Conditions Index
	if service.IsDATMarketConditionsEnabled {
		originMarketConditionsIndex, err = GetDATMarketConditionsScore(
			ctx,
			integration,
			user.DATEmailAddress,
			pickupMCIRequest,
		)
		if err != nil {
			log.Error(ctx, "failed to get DAT market conditions index for origin", zap.Error(err))
		}

		destinationMarketConditionsIndex, err = GetDATMarketConditionsScore(
			ctx,
			integration,
			user.DATEmailAddress,
			dropoffMCIRequest,
		)
		if err != nil {
			log.Error(ctx, "failed to get DAT market conditions index for destination", zap.Error(err))
		}
	}

	// Load to truck ratio
	if service.IsDATLoadToTruckRatioEnabled {
		prevBusinessDay := helpers.GetPreviousBusinessDay(time.Now()).Format(time.DateOnly)

		originLoadToTruckRatio, err = GetDATLoadToTruckRatio(
			ctx,
			integration,
			user.DATEmailAddress,
			dat.GetLoadToTruckRatioRequest{
				GetMarketInformationRequest: pickupMCIRequest,
				StartDate:                   prevBusinessDay,
				EndDate:                     prevBusinessDay,
			},
		)
		if err != nil {
			// Fail-open: should simply return quote without MCI load to truck ratio
			log.Error(ctx, "failed to get DAT load to truck ratio for origin", zap.Error(err))
		}

		destinationLoadToTruckRatio, err = GetDATLoadToTruckRatio(
			ctx,
			integration,
			user.DATEmailAddress,
			dat.GetLoadToTruckRatioRequest{
				GetMarketInformationRequest: dropoffMCIRequest,
				StartDate:                   prevBusinessDay,
				EndDate:                     prevBusinessDay,
			},
		)
		if err != nil {
			// Fail-open: should simply return quote without MCI load to truck ratio
			log.Error(ctx, "failed to get DAT load to truck ratio for destination", zap.Error(err))
		}
	}

	pickupMarketInformation := DATMarketInformation{
		StopLocation: fmt.Sprintf("%s, %s", stops[0].City, stops[0].State),
	}
	dropoffMarketInformation := DATMarketInformation{
		StopLocation: fmt.Sprintf("%s, %s", stops[1].City, stops[1].State),
	}

	if originLoadToTruckRatio != nil {
		pickupMarketInformation.InboundLoadToTruckRatio = originLoadToTruckRatio.Inbound.Ratio
		pickupMarketInformation.OutboundLoadToTruckRatio = originLoadToTruckRatio.Outbound.Ratio
	}

	if originMarketConditionsIndex != nil {
		pickupMarketInformation.InboundMarketConditionsIndexPrevDay =
			originMarketConditionsIndex.Inbound[0].MciScore
		pickupMarketInformation.InboundMarketConditionsIndexPrevWeek =
			originMarketConditionsIndex.InboundSummarized
		pickupMarketInformation.OutboundMarketConditionsIndexPrevDay =
			originMarketConditionsIndex.Outbound[0].MciScore
		pickupMarketInformation.OutboundMarketConditionsIndexPrevWeek =
			originMarketConditionsIndex.OutboundSummarized
	}

	if originMarketConditionsForecast != nil {
		pickupMarketInformation.InboundMarketConditionsForecastNextDay =
			originMarketConditionsForecast.Inbound[0].MciScore
		pickupMarketInformation.OutboundMarketConditionsForecastNextDay =
			originMarketConditionsForecast.Outbound[0].MciScore
	}

	if destinationLoadToTruckRatio != nil {
		dropoffMarketInformation.InboundLoadToTruckRatio =
			destinationLoadToTruckRatio.Inbound.Ratio
		dropoffMarketInformation.OutboundLoadToTruckRatio =
			destinationLoadToTruckRatio.Outbound.Ratio
	}
	if destinationMarketConditionsIndex != nil {
		dropoffMarketInformation.InboundMarketConditionsIndexPrevDay =
			destinationMarketConditionsIndex.Inbound[0].MciScore
		dropoffMarketInformation.InboundMarketConditionsIndexPrevWeek =
			destinationMarketConditionsIndex.InboundSummarized
		dropoffMarketInformation.OutboundMarketConditionsIndexPrevDay =
			destinationMarketConditionsIndex.Outbound[0].MciScore
		dropoffMarketInformation.OutboundMarketConditionsIndexPrevWeek =
			destinationMarketConditionsIndex.OutboundSummarized
	}
	if destinationMarketConditionsForecast != nil {
		dropoffMarketInformation.InboundMarketConditionsForecastNextDay =
			destinationMarketConditionsForecast.Inbound[0].MciScore
		dropoffMarketInformation.OutboundMarketConditionsForecastNextDay =
			destinationMarketConditionsForecast.Outbound[0].MciScore
	}

	return pickupMarketInformation, dropoffMarketInformation, nil
}

func GetDATLoadToTruckRatio(
	ctx context.Context,
	integration models.Integration,
	datEmailAddress string,
	req dat.GetLoadToTruckRatioRequest,
) (*dat.LocationLoadToTruckRatios, error) {

	client, err := dat.New(ctx, integration, datEmailAddress)
	if err != nil {
		return nil, fmt.Errorf("unable to create DAT client: %w", err)
	}

	req.Direction = dat.MCDirectionOutbound
	outboundResp, err := client.GetLoadToTruckRatio(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("DAT GetLoadToTruckRatio failed: %w", err)
	}

	if len(outboundResp.LoadToTruckRatios) == 0 {
		return nil, errors.New("no load to truck ratio for outbound")
	}

	req.Direction = dat.MCDirectionInbound
	inboundResp, err := client.GetLoadToTruckRatio(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("DAT GetLoadToTruckRatio failed: %w", err)
	}

	if len(inboundResp.LoadToTruckRatios) == 0 {
		return nil, errors.New("no load to truck ratio for inbound")
	}

	return &dat.LocationLoadToTruckRatios{
		Inbound:  inboundResp.LoadToTruckRatios[0],
		Outbound: outboundResp.LoadToTruckRatios[0],
	}, nil
}

func GetDATMarketConditionsForecast(
	ctx context.Context,
	integration models.Integration,
	datEmailAddress string,
	req dat.GetMarketInformationRequest,
) (*dat.LocationMarketConditionsForecast, error) {

	client, err := dat.New(ctx, integration, datEmailAddress)
	if err != nil {
		return nil, fmt.Errorf("unable to create DAT client: %w", err)
	}

	req.Direction = dat.MCDirectionOutbound
	outboundResp, err := client.GetMarketConditionsForecast(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("DAT GetMarketConditionsForecast failed: %w", err)
	}

	if len(outboundResp.MarketConditionsForecast) == 0 {
		return nil, errors.New("no market conditions forecast for outbound")
	}

	req.Direction = dat.MCDirectionInbound
	inboundResp, err := client.GetMarketConditionsForecast(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("DAT GetMarketConditionsForecast failed: %w", err)
	}

	if len(inboundResp.MarketConditionsForecast) == 0 {
		return nil, errors.New("no market conditions forecast for inbound")
	}

	return &dat.LocationMarketConditionsForecast{
		Inbound:  inboundResp.MarketConditionsForecast,
		Outbound: outboundResp.MarketConditionsForecast,
	}, nil
}

func GetDATMarketConditionsScore(
	ctx context.Context,
	integration models.Integration,
	datEmailAddress string,
	req dat.GetMarketInformationRequest,
) (*dat.LocationMarketConditionsIndex, error) {

	client, err := dat.New(ctx, integration, datEmailAddress)
	if err != nil {
		return nil, fmt.Errorf("unable to create DAT client: %w", err)
	}

	req.Direction = dat.MCDirectionOutbound
	outboundResp, err := client.GetMarketConditionsScore(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("DAT GetMarketConditionsScore failed: %w", err)
	}

	if len(outboundResp.MarketConditionsIndexes) == 0 {
		return nil, errors.New("no market conditions score for outbound")
	}

	req.Direction = dat.MCDirectionInbound
	inboundResp, err := client.GetMarketConditionsScore(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("DAT GetMarketConditionsScore failed: %w", err)
	}

	if len(inboundResp.MarketConditionsIndexes) == 0 {
		return nil, errors.New("no market conditions score for inbound")
	}

	return &dat.LocationMarketConditionsIndex{
		Inbound:            inboundResp.MarketConditionsIndexes,
		InboundSummarized:  inboundResp.SummarizedMciScore,
		Outbound:           outboundResp.MarketConditionsIndexes,
		OutboundSummarized: outboundResp.SummarizedMciScore,
	}, nil
}
