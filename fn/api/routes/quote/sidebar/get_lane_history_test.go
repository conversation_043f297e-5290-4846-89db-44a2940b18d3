package quoteprivate

import (
	"context"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/integrations/pricing/greenscreens"
	"github.com/drumkitai/drumkit/common/models"
	quoteCommon "github.com/drumkitai/drumkit/fn/api/routes/quote/common"
	apiutil "github.com/drumkitai/drumkit/fn/api/util"
)

// Freeze time for testing consistency
var latestDate = time.Date(2025, 1, 25, 0, 0, 0, 0, time.UTC)

func TestCalculateFourWeekData(t *testing.T) {
	ctx := context.Background()

	dummyData := []greenscreens.Item{
		{
			LoadPickupDate:   "2025-01-25",
			TotalCarrierCost: 1000.0,
			CarrierName:      "Carrier A",
		},
		{
			LoadPickupDate:   "2025-01-18",
			TotalCarrierCost: 1200.0,
			CarrierName:      "Carrier B",
		},
		{
			LoadPickupDate:   "2025-01-11",
			TotalCarrierCost: 800.0,
			CarrierName:      "Carrier C",
		},
		{
			LoadPickupDate:   "2025-01-04",
			TotalCarrierCost: 900.0,
			CarrierName:      "Carrier D",
		},
		{
			LoadPickupDate:   "2024-12-25",
			TotalCarrierCost: 1500.0,
			CarrierName:      "Carrier E",
		}, // Outdated (more than 4 weeks ago)
	}

	t.Run("Valid Four Week Data", func(t *testing.T) {
		result := apiutil.CalculateWeekData(ctx, dummyData, &latestDate, defaultNumLookbackDays)
		require.NotNil(t, result)
		assert.Len(t, result, 4, "Result should contain exactly 4 weeks of data")

		// Validate Week 0
		week0 := result[0]
		assert.Equal(t, 1, week0.Count)
		assert.Equal(t, 1000.0, week0.MinRate)
		assert.Equal(t, "Carrier A", week0.MinCarrier)
		assert.Equal(t, 1000.0, week0.TotalRate)

		// Validate Week 1
		week1 := result[1]
		assert.Equal(t, 1, week1.Count)
		assert.Equal(t, 1200.0, week1.MinRate)
		assert.Equal(t, "Carrier B", week1.MinCarrier)
		assert.Equal(t, 1200.0, week1.TotalRate)

		// Validate Week 2
		week2 := result[2]
		assert.Equal(t, 1, week2.Count)
		assert.Equal(t, 800.0, week2.MinRate)
		assert.Equal(t, "Carrier C", week2.MinCarrier)
		assert.Equal(t, 800.0, week2.TotalRate)

		// Validate Week 3
		week3 := result[3]
		assert.Equal(t, 1, week3.Count)
		assert.Equal(t, 900.0, week3.MinRate)
		assert.Equal(t, "Carrier D", week3.MinCarrier)
		assert.Equal(t, 900.0, week3.TotalRate)
	})

	t.Run("No Data", func(t *testing.T) {
		result := apiutil.CalculateWeekData(
			ctx,
			[]models.LaneHistoryRawDataAccessor(nil),
			&latestDate,
			defaultNumLookbackDays,
		)
		require.Nil(t, result)
	})

	t.Run("Invalid Dates", func(t *testing.T) {
		invalidData := []greenscreens.Item{
			{
				LoadPickupDate:   "invalid-date",
				TotalCarrierCost: 1000.0,
				CarrierName:      "Carrier A",
			},
		}

		result := apiutil.CalculateWeekData(ctx, invalidData, &latestDate, defaultNumLookbackDays)
		require.NotNil(t, result)
		assert.Empty(t, result, "Result should be empty when all dates are invalid")
	})
}

// TestOrderWeekData tests the orderWeekData function.
func TestOrderWeekData(t *testing.T) {
	t.Run("Valid Week Data", func(t *testing.T) {
		weekDataMap := map[int]*apiutil.WeekData{
			0: {TotalRate: 3000, MinRate: 800, MinCarrier: "Carrier C", Count: 3},
			1: {TotalRate: 2000, MinRate: 900, MinCarrier: "Carrier D", Count: 2},
			2: {TotalRate: 1500, MinRate: 1200, MinCarrier: "Carrier B", Count: 1},
			3: {TotalRate: 1000, MinRate: 1000, MinCarrier: "Carrier A", Count: 1},
		}

		result := apiutil.OrderWeekData(weekDataMap, &latestDate, defaultNumLookbackDays/7)

		expected := []apiutil.WeekResponseItem{
			{
				Week:              "Dec 29 - Jan 4",
				AverageRate:       1000,
				LowestRate:        1000,
				Quotes:            1,
				LowestCarrierName: "Carrier A",
				MaxRate:           0,
				MaxRateCarrier:    "",
			},
			{
				Week:              "Jan 5 - Jan 11",
				AverageRate:       1500,
				LowestRate:        1200,
				Quotes:            1,
				LowestCarrierName: "Carrier B",
				MaxRate:           0,
				MaxRateCarrier:    "",
			},
			{
				Week:              "Jan 12 - Jan 18",
				AverageRate:       1000,
				LowestRate:        900,
				Quotes:            2,
				LowestCarrierName: "Carrier D",
				MaxRate:           0,
				MaxRateCarrier:    "",
			},
			{
				Week:              "Jan 19 - Jan 25",
				AverageRate:       1000,
				LowestRate:        800,
				Quotes:            3,
				LowestCarrierName: "Carrier C",
				MaxRate:           0,
				MaxRateCarrier:    "",
			},
		}

		assert.Equal(t, expected, result)
	})

	t.Run("Empty Week Data", func(t *testing.T) {
		weekDataMap := map[int]*apiutil.WeekData{}
		result := apiutil.OrderWeekData(weekDataMap, &latestDate, defaultNumLookbackDays/7)

		expected := []apiutil.WeekResponseItem{
			{Week: "Dec 29 - Jan 4"},
			{Week: "Jan 5 - Jan 11"},
			{Week: "Jan 12 - Jan 18"},
			{Week: "Jan 19 - Jan 25"},
		}

		assert.Equal(t, expected, result)
	})

	t.Run("Missing Weeks", func(t *testing.T) {
		weekDataMap := map[int]*apiutil.WeekData{
			0: {TotalRate: 3000, MinRate: 800, MinCarrier: "Carrier C", Count: 3},
			2: {TotalRate: 1500, MinRate: 1200, MinCarrier: "Carrier B", Count: 1},
		}

		result := apiutil.OrderWeekData(weekDataMap, &latestDate, defaultNumLookbackDays/7)

		expected := []apiutil.WeekResponseItem{
			{
				Week:              "Dec 29 - Jan 4",
				AverageRate:       0,
				LowestRate:        0,
				Quotes:            0,
				LowestCarrierName: "",
				MaxRate:           0,
				MaxRateCarrier:    "",
			},
			{
				Week:              "Jan 5 - Jan 11",
				AverageRate:       1500,
				LowestRate:        1200,
				Quotes:            1,
				LowestCarrierName: "Carrier B",
				MaxRate:           0,
				MaxRateCarrier:    "",
			},
			{
				Week:              "Jan 12 - Jan 18",
				AverageRate:       0,
				LowestRate:        0,
				Quotes:            0,
				LowestCarrierName: "",
				MaxRate:           0,
				MaxRateCarrier:    "",
			},
			{
				Week:              "Jan 19 - Jan 25",
				AverageRate:       1000,
				LowestRate:        800,
				Quotes:            3,
				LowestCarrierName: "Carrier C",
				MaxRate:           0,
				MaxRateCarrier:    "",
			},
		}

		assert.Equal(t, expected, result)
	})
}

func TestPopulateQueryForTier(t *testing.T) {
	t.Run("3DigitZipTier", func(t *testing.T) {
		t.Run("valid zips", func(t *testing.T) {
			query := &models.SearchLoadsQuery{}
			body := LaneHistoryRequestBody{
				OriginZip:      "12345",
				DestinationZip: "67890",
			}

			err := populateQueryForTier(query, _3DigitZipLaneTier, body)
			require.NoError(t, err)
			assert.Equal(t, "123", query.Pickup.Zip)
			assert.Equal(t, "678", query.Dropoff.Zip)
			// Assert other fields are unchanged/empty
			assert.Empty(t, query.Pickup.City)
			assert.Empty(t, query.Dropoff.City)
			assert.Empty(t, query.Pickup.State)
			assert.Empty(t, query.Dropoff.State)
			assert.Empty(t, query.FromDate)
			assert.Empty(t, query.ToDate)
			assert.Empty(t, query.TransportType)
			assert.Empty(t, query.Status)
		})

		t.Run("invalid zips", func(t *testing.T) {
			query := &models.SearchLoadsQuery{}
			body := LaneHistoryRequestBody{
				OriginZip:      "123",
				DestinationZip: "678",
			}

			err := populateQueryForTier(query, _3DigitZipLaneTier, body)
			require.Error(t, err)
			assert.Contains(t, err.Error(), "insufficient zips provided")
			assert.Empty(t, query)
		})
	})

	t.Run("CityLaneTier", func(t *testing.T) {
		t.Run("valid city/state", func(t *testing.T) {
			query := &models.SearchLoadsQuery{}
			body := LaneHistoryRequestBody{
				OriginCity:       "Chicago",
				OriginState:      "IL",
				DestinationCity:  "Dallas",
				DestinationState: "TX",
			}

			err := populateQueryForTier(query, CityLaneTier, body)
			require.NoError(t, err)
			assert.Equal(t, "Chicago", query.Pickup.City)
			assert.Equal(t, "IL", query.Pickup.State)
			assert.Equal(t, "Dallas", query.Dropoff.City)
			assert.Equal(t, "TX", query.Dropoff.State)
			// Assert other fields are unchanged/empty
			assert.Empty(t, query.Pickup.Zip)
			assert.Empty(t, query.Dropoff.Zip)
			assert.Empty(t, query.FromDate)
			assert.Empty(t, query.ToDate)
			assert.Empty(t, query.TransportType)
			assert.Empty(t, query.Status)
		})

		t.Run("missing city", func(t *testing.T) {
			query := &models.SearchLoadsQuery{}
			body := LaneHistoryRequestBody{
				OriginState:      "IL",
				DestinationCity:  "Dallas",
				DestinationState: "TX",
			}

			err := populateQueryForTier(query, CityLaneTier, body)
			require.Error(t, err)
			assert.Contains(t, err.Error(), "insufficient city/state provided")
			assert.Empty(t, query)
		})
	})

	t.Run("StateLaneTier", func(t *testing.T) {
		t.Run("valid states", func(t *testing.T) {
			query := &models.SearchLoadsQuery{}
			body := LaneHistoryRequestBody{
				OriginState:      "IL",
				DestinationState: "TX",
			}

			err := populateQueryForTier(query, StateLaneTier, body)
			require.NoError(t, err)
			assert.Equal(t, "IL", query.Pickup.State)
			assert.Equal(t, "TX", query.Dropoff.State)
			// Assert other fields are unchanged/empty
			assert.Empty(t, query.Pickup.City)
			assert.Empty(t, query.Dropoff.City)
			assert.Empty(t, query.Pickup.Zip)
			assert.Empty(t, query.Dropoff.Zip)
			assert.Empty(t, query.FromDate)
			assert.Empty(t, query.ToDate)
			assert.Empty(t, query.TransportType)
			assert.Empty(t, query.Status)
		})

		t.Run("missing state", func(t *testing.T) {
			query := &models.SearchLoadsQuery{}
			body := LaneHistoryRequestBody{
				OriginState: "IL",
			}

			err := populateQueryForTier(query, StateLaneTier, body)
			require.Error(t, err)
			assert.Contains(t, err.Error(), "insufficient state provided")
			assert.Empty(t, query)
		})
	})

	t.Run("unknown tier", func(t *testing.T) {
		query := &models.SearchLoadsQuery{}
		body := LaneHistoryRequestBody{}
		err := populateQueryForTier(query, LaneTier("invalid"), body)
		require.Error(t, err)
		assert.Contains(t, err.Error(), "unknown tier type")
	})
}

func TestProcessTMSResults_CreatesQuotes_McLeod(t *testing.T) {
	ctx := context.Background()

	var createQuoteRecordCalls uint
	mockService := models.Service{
		Model: gorm.Model{ID: 1},
		Name:  "Test Service",
		FeatureFlags: models.FeatureFlags{
			IsTMSLaneHistoryEnabled: true,
		},
	}
	mockUser := models.User{
		Model:        gorm.Model{ID: 10},
		EmailAddress: "<EMAIL>",
		ServiceID:    1,
	}
	body := LaneHistoryRequestBody{
		QuoteRequestID:     123,
		OriginCity:         "Chicago",
		OriginState:        "IL",
		OriginZip:          "60606",
		OriginCountry:      quoteCommon.CountryUS,
		DestinationCity:    "Dallas",
		DestinationState:   "TX",
		DestinationZip:     "75201",
		DestinationCountry: quoteCommon.CountryUS,
		TransportType:      models.VanTransportType,
	}

	expectedAvgDistance := 250.0
	expectedAvgCost := 500.0
	expectedMinCost := 450.0
	expectedMaxCost := 550.0
	expectedAvgRatePerMile := expectedAvgCost / expectedAvgDistance
	expectedMinRatePerMile := expectedMinCost / expectedAvgDistance
	expectedMaxRatePerMile := expectedMaxCost / expectedAvgDistance

	validTMSResults := []SourceHistory{
		{
			Source:   models.McleodEnterprise,
			LaneTier: CityLaneTier,
			CalculatedQuote: &apiutil.CalculatedQuote{
				AvgDistance:    expectedAvgDistance,
				AvgCost:        expectedAvgCost,
				MinCost:        expectedMinCost,
				MaxCost:        expectedMaxCost,
				AvgRatePerMile: expectedAvgRatePerMile,
				MinRatePerMile: expectedMinRatePerMile,
				MaxRatePerMile: expectedMaxRatePerMile,
			},
			Weeks: []apiutil.WeekResponseItem{{Quotes: 1}}, // Ensure hasValidWeekData passes
		},
		{
			Source:   models.McleodEnterprise,
			LaneTier: StateLaneTier,
			CalculatedQuote: &apiutil.CalculatedQuote{
				AvgDistance:    300.0,
				AvgCost:        600.0,
				MinCost:        550.0,
				MaxCost:        650.0,
				AvgRatePerMile: 2.0,
				MinRatePerMile: 1.83,
				MaxRatePerMile: 2.17,
			},
			Weeks: []apiutil.WeekResponseItem{{Quotes: 1}},
		},
	}

	var capturedStops []models.Stop
	var capturedRateData []quoteCommon.RateData
	var capturedTypeInSource []models.QuoteTypeInSource

	// Store original and defer restore
	originalCreateQuoteRecord := createQuoteRecord
	defer func() { createQuoteRecord = originalCreateQuoteRecord }()

	createQuoteRecord = func(
		_ context.Context, service models.Service, userID uint, _ uint, threadID string, quoteRequestID uint,
		stops []models.Stop, transportType models.TransportType, _ time.Time, deliveryDate time.Time,
		rateData quoteCommon.RateData, _ float64, _ float64, _ float64,
		source models.QuoteSource, typeInSource models.QuoteTypeInSource, datMetadata *models.QuoteDATMetadata,
	) *models.QuickQuote {
		createQuoteRecordCalls++
		// Will capture the last call's stops if not careful,
		// but for this test it's okay as stops are same
		capturedStops = stops
		capturedRateData = append(capturedRateData, rateData)
		capturedTypeInSource = append(capturedTypeInSource, typeInSource)

		assert.Equal(t, mockService.ID, service.ID)
		assert.Equal(t, mockUser.ID, userID)
		assert.Equal(t, "", threadID)
		assert.Equal(t, body.QuoteRequestID, quoteRequestID)
		assert.Equal(t, body.TransportType, transportType)
		assert.True(t, deliveryDate.IsZero(), "DeliveryDate should be zero")
		assert.Equal(t, models.TmsLaneHistorySource, source)
		assert.Nil(t, datMetadata)
		// Specific cost assertions will be done outside based on which result is processed

		// Return a dummy quote
		return &models.QuickQuote{Model: gorm.Model{ID: uint(900) + createQuoteRecordCalls}}
	}

	results := processTMSResultsAndCreateQuotes(
		ctx,
		mockService,
		mockUser,
		body,
		validTMSResults,
	)

	require.NotNil(t, results)
	assert.Equal(t, uint(2), createQuoteRecordCalls, "CreateQuoteRecord should have been called twice")

	// Assertions for the first call
	expectedTypeInSource1 := models.QuoteTypeInSource(
		strings.ToLower(
			string(models.McleodEnterprise) + "-" + string(CityLaneTier),
		),
	)
	assert.Equal(t, expectedTypeInSource1, capturedTypeInSource[0])
	assert.Equal(t, expectedAvgRatePerMile, capturedRateData[0].TargetBuyRate)
	assert.Equal(t, expectedMinRatePerMile, capturedRateData[0].LowBuyRate)
	assert.Equal(t, expectedMaxRatePerMile, capturedRateData[0].HighBuyRate)
	assert.Equal(t, expectedAvgDistance, capturedRateData[0].Distance)

	// Assertions for the second call
	expectedTypeInSource2 := models.QuoteTypeInSource(
		strings.ToLower(
			string(models.McleodEnterprise) + "-" + string(StateLaneTier),
		),
	)
	assert.Equal(t, expectedTypeInSource2, capturedTypeInSource[1])
	assert.Equal(t, 2.0, capturedRateData[1].TargetBuyRate)

	require.Len(t, capturedStops, 2)
	assert.Equal(t, 0, capturedStops[0].StopNumber)
	assert.Equal(t, "Chicago", capturedStops[0].Address.City)
	assert.Equal(t, 1, capturedStops[1].StopNumber)
	assert.Equal(t, "Dallas", capturedStops[1].Address.City)
}

func TestProcessTMSResults_CreatesQuotes_Turvo(t *testing.T) {
	ctx := context.Background()

	var createQuoteRecordCalls uint
	mockService := models.Service{
		Model: gorm.Model{ID: 2},
		Name:  "Test Service Turvo",
		FeatureFlags: models.FeatureFlags{
			IsTMSLaneHistoryEnabled: true,
		},
	}
	mockUser := models.User{
		Model:        gorm.Model{ID: 20},
		EmailAddress: "<EMAIL>",
		ServiceID:    2,
	}
	body := LaneHistoryRequestBody{
		QuoteRequestID:     456,
		OriginCity:         "Los Angeles",
		OriginState:        "CA",
		OriginZip:          "90001",
		OriginCountry:      quoteCommon.CountryUS,
		DestinationCity:    "Phoenix",
		DestinationState:   "AZ",
		DestinationZip:     "85001",
		DestinationCountry: quoteCommon.CountryUS,
		TransportType:      models.ReeferTransportType,
	}

	expectedAvgDistance := 370.0
	expectedAvgCost := 740.0
	expectedMinCost := 700.0
	expectedMaxCost := 780.0
	expectedAvgRatePerMile := expectedAvgCost / expectedAvgDistance
	expectedMinRatePerMile := expectedMinCost / expectedAvgDistance
	expectedMaxRatePerMile := expectedMaxCost / expectedAvgDistance

	validTMSResults := []SourceHistory{
		{
			Source:   models.Turvo, // Simulate Turvo source
			LaneTier: CityLaneTier,
			CalculatedQuote: &apiutil.CalculatedQuote{
				AvgDistance:    expectedAvgDistance,
				AvgCost:        expectedAvgCost,
				MinCost:        expectedMinCost,
				MaxCost:        expectedMaxCost,
				AvgRatePerMile: expectedAvgRatePerMile,
				MinRatePerMile: expectedMinRatePerMile,
				MaxRatePerMile: expectedMaxRatePerMile,
			},
			Weeks: []apiutil.WeekResponseItem{{Quotes: 1}},
		},
	}

	var capturedTypeInSource models.QuoteTypeInSource // Only one call expected

	originalCreateQuoteRecord := createQuoteRecord
	defer func() { createQuoteRecord = originalCreateQuoteRecord }()

	createQuoteRecord = func(
		_ context.Context, service models.Service, userID uint, _ uint, threadID string, quoteRequestID uint,
		_ []models.Stop, transportType models.TransportType, _ time.Time, deliveryDate time.Time,
		_ quoteCommon.RateData, targetSellCost float64, _ float64, _ float64,
		source models.QuoteSource, typeInSource models.QuoteTypeInSource, _ *models.QuoteDATMetadata,
	) *models.QuickQuote {
		createQuoteRecordCalls++
		capturedTypeInSource = typeInSource

		assert.Equal(t, mockService.ID, service.ID)
		assert.Equal(t, mockUser.ID, userID)
		assert.Equal(t, "", threadID)
		assert.Equal(t, body.QuoteRequestID, quoteRequestID)
		assert.Equal(t, body.TransportType, transportType)
		assert.True(t, deliveryDate.IsZero(), "DeliveryDate should be zero")
		assert.Equal(t, models.TmsLaneHistorySource, source)
		assert.Equal(t, targetSellCost, expectedAvgCost) // Verify correct cost is passed

		return &models.QuickQuote{Model: gorm.Model{ID: 1001}}
	}

	results := processTMSResultsAndCreateQuotes(
		ctx,
		mockService,
		mockUser,
		body,
		validTMSResults,
	)

	require.NotNil(t, results)
	assert.Equal(t, uint(1), createQuoteRecordCalls, "CreateQuoteRecord should have been called once for Turvo")
	expectedTypeInSourceTurvo := models.QuoteTypeInSource(
		strings.ToLower(
			string(models.Turvo) + "-" + string(CityLaneTier),
		),
	)
	assert.Equal(t, expectedTypeInSourceTurvo, capturedTypeInSource)
}

func TestProcessTMSResults_CreatesQuotes_UnknownTMS(t *testing.T) {
	ctx := context.Background()

	var createQuoteRecordCalls uint
	mockService := models.Service{
		Model: gorm.Model{ID: 3},
		Name:  "Test Service Unknown",
		FeatureFlags: models.FeatureFlags{
			IsTMSLaneHistoryEnabled: true,
		},
	}
	mockUser := models.User{
		Model:        gorm.Model{ID: 30},
		EmailAddress: "<EMAIL>",
		ServiceID:    3,
	}
	body := LaneHistoryRequestBody{
		QuoteRequestID:     789,
		OriginCity:         "Miami",
		OriginState:        "FL",
		OriginZip:          "33101",
		OriginCountry:      quoteCommon.CountryUS,
		DestinationCity:    "Atlanta",
		DestinationState:   "GA",
		DestinationZip:     "30301",
		DestinationCountry: quoteCommon.CountryUS,
		TransportType:      models.FlatbedTransportType,
	}

	unknownTMSSourceName := models.IntegrationName("some_future_tms")

	validTMSResults := []SourceHistory{
		{
			Source:   unknownTMSSourceName, // Simulate an unknown TMS source
			LaneTier: CityLaneTier,
			CalculatedQuote: &apiutil.CalculatedQuote{
				AvgDistance:    660.0,
				AvgCost:        1320.0,
				MinCost:        1300.0,
				MaxCost:        1340.0,
				AvgRatePerMile: 2.0,
				MinRatePerMile: 1.96,
				MaxRatePerMile: 2.03,
			},
			Weeks: []apiutil.WeekResponseItem{{Quotes: 1}},
		},
	}

	var capturedTypeInSource models.QuoteTypeInSource

	originalCreateQuoteRecord := createQuoteRecord
	defer func() { createQuoteRecord = originalCreateQuoteRecord }()

	createQuoteRecord = func(
		_ context.Context, service models.Service, userID uint, _ uint, threadID string, quoteRequestID uint,
		_ []models.Stop, transportType models.TransportType, _ time.Time, deliveryDate time.Time,
		_ quoteCommon.RateData, _ float64, _ float64, _ float64,
		source models.QuoteSource, typeInSource models.QuoteTypeInSource, _ *models.QuoteDATMetadata,
	) *models.QuickQuote {
		createQuoteRecordCalls++
		capturedTypeInSource = typeInSource

		assert.Equal(t, mockService.ID, service.ID)
		assert.Equal(t, mockUser.ID, userID)
		assert.Equal(t, "", threadID)
		assert.Equal(t, body.QuoteRequestID, quoteRequestID)
		assert.Equal(t, body.TransportType, transportType)
		assert.True(t, deliveryDate.IsZero(), "DeliveryDate should be zero")
		assert.Equal(t, models.TmsLaneHistorySource, source)

		// Key assertion for this test case
		// For unknown TMS, currentTypeInSource in the helper will be "", LaneTier is CityLaneTier
		expectedTypeInSourceUnknown := models.QuoteTypeInSource(
			strings.ToLower(string(unknownTMSSourceName) + "-" + string(CityLaneTier)),
		)
		assert.Equal(t, expectedTypeInSourceUnknown, typeInSource,
			"TypeInSource should be the lowercase concatenation of empty string, dash, and lane tier for unknown TMS")

		return &models.QuickQuote{Model: gorm.Model{ID: 2001}}
	}

	results := processTMSResultsAndCreateQuotes(
		ctx,
		mockService,
		mockUser,
		body,
		validTMSResults,
	)

	require.NotNil(t, results)
	assert.Equal(t, uint(1), createQuoteRecordCalls, "CreateQuoteRecord should have been called once for a unknown TMS")
	expectedCapturedTypeInSourceUnknown := models.QuoteTypeInSource(
		strings.ToLower(
			string(unknownTMSSourceName) + "-" + string(CityLaneTier),
		),
	)
	assert.Equal(
		t, expectedCapturedTypeInSourceUnknown, capturedTypeInSource,
		"Captured TypeInSource should match expected for unknown TMS",
	)
}
