package quoteprivate

import (
	"context"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/integrations/email/gmailclient"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	emailDB "github.com/drumkitai/drumkit/common/rds/email"
	quoteRequestDB "github.com/drumkitai/drumkit/common/rds/quote_request"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	apiutil "github.com/drumkitai/drumkit/fn/api/util"
)

type (
	GetQuoteRequestPath struct {
		ThreadID string `json:"threadID"`
	}

	GetQuoteRequestResponse struct {
		ID       uint   `json:"id"`
		EmailID  uint   `json:"emailID"`
		ThreadID string `json:"threadID"`
		// From models.Quote Specifications section
		TransportType    models.TransportType  `json:"transportType"`
		Commodity        string                `json:"commodity"`
		WeightLbs        float64               `json:"weightLbs"`
		Pallets          models.Pallets        `json:"pallets"`
		PickupLocation   models.Address        `json:"pickupLocation"` // First element from the stops array
		PickupDate       models.NullTime       `json:"pickupDate"`
		DeliveryLocation models.Address        `json:"deliveryLocation"` // Last element from the stops array
		DeliveryDate     models.NullTime       `json:"deliveryDate"`
		CarrierEmails    []MiniEmail           `json:"carrierNetworkEmails"`
		CarrierQuotes    []models.CarrierQuote `json:"carrierQuotes"`

		// TODO: Deprecate, accidentally nested here, used in EmailCarriersForQuotes
		Carriers []models.LocationWithDistance `json:"carriers"`
		From     string                        `json:"from"`
	}

	MiniEmail struct {
		ID         uint     `json:"id"`
		ExternalID string   `json:"externalID"`
		ThreadID   string   `json:"threadID"`
		Recipients []string `json:"recipients"`
		Subject    string   `json:"subject"`
		Bounced    bool     `json:"bounced"`
		URL        string   `json:"url"` // URL-encoded
	}
)

func GetQuoteRequest(c *fiber.Ctx) error {
	var path GetQuoteRequestPath
	if err := api.Parse(c, &path, nil, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()
	userID := middleware.UserIDFromContext(c)
	serviceID := middleware.ServiceIDFromContext(c)

	var err error

	path.ThreadID, err = apiutil.DecodeOutlookID(path.ThreadID)
	if err != nil {
		log.Error(ctx, "error decoding threadID", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	service, err := rds.GetServiceByID(ctx, serviceID)
	if err != nil {
		// Fail-open; this is to allow showing CQs for shared inboxes
		log.Warn(ctx, "error fetching service to get IsDelegatedInboxEnabled flag", zap.Error(err))
	}

	req, err := quoteRequestDB.GetRequestByThreadIDPreloadAssociation(ctx, userID, path.ThreadID, service)
	if err != nil {
		return apiutil.HandleDBError(c, err, true, "no quote requests for thread %s", path.ThreadID)
	}

	user, err := userDB.GetByEmail(ctx, middleware.ClaimsFromContext(c).Email)
	if err != nil {
		// Fail-open as we only use this to construct the URL
		log.WarnNoSentry(ctx, "error fetching user from BD", zap.Error(err))
	}

	resp := GetQuoteRequestResponse{
		ID:               req.ID,
		EmailID:          req.EmailID,
		ThreadID:         req.ThreadID,
		TransportType:    req.SuggestedRequest.TransportType,
		Commodity:        req.SuggestedRequest.Commodity,
		WeightLbs:        req.SuggestedRequest.WeightLbs,
		Pallets:          req.SuggestedRequest.Pallets,
		PickupLocation:   req.SuggestedRequest.PickupLocation,
		PickupDate:       req.SuggestedRequest.PickupDate,
		DeliveryLocation: req.SuggestedRequest.DeliveryLocation,
		DeliveryDate:     req.SuggestedRequest.DeliveryDate,
	}

	for _, email := range req.CarrierEmails {
		resp.CarrierEmails = append(resp.CarrierEmails, MiniEmail{
			ID:         email.ID,
			ExternalID: email.ExternalID,
			ThreadID:   email.ThreadID,
			Recipients: email.Recipients,
			Subject:    email.Subject,
			Bounced:    email.Bounced,
			URL:        constructURL(ctx, &user, &service, email),
		})
	}

	resp.CarrierQuotes = append(resp.CarrierQuotes, req.CarrierQuotes...)

	return c.Status(http.StatusOK).JSON(resp)
}

func constructURL(ctx context.Context, user *models.User, service *models.Service, email models.GeneratedEmail) string {
	switch user.EmailProvider {
	case models.FrontEmailProvider:
		// TODO: Front URL
		return ""

	case models.GmailEmailProvider:
		// If service has delegated/shared inboxes, then User B should be able to see thread from shared inbox A.
		transformedThreadID, err := emailDB.TransformSharedThread(ctx, user.ID, email.ThreadID, service)
		if err != nil {
			log.Warn(ctx, "error transforming threadID", zap.Error(err))
			return gmailclient.InboxBaseURL + email.ThreadID
		}
		log.Info(
			ctx,
			"transformed threadID",
			zap.String("originalThreadID", email.ThreadID),
			zap.String("transformedThreadID", transformedThreadID),
		)

		return gmailclient.InboxBaseURL + transformedThreadID

	default:
		// TODO: Outlook URL brings to first message, not thread. Need to transform threadID to match user's copy
		return email.WebLink
	}
}
