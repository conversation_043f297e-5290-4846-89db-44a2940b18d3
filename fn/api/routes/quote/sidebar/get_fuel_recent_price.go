package quoteprivate

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/PuerkitoBio/goquery"
	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/redis"
)

const EIAFuelPriceURL = "https://www.eia.gov/dnav/pet/pet_pri_gnd_dcus_nus_w.htm"
const EIAFuelPriceRedisKey = "fuel-price-eia"

type GetFuelRecentPriceResponse struct {
	FuelPrice string `json:"fuelPrice"`
}

func GetFuelRecentPrice(c *fiber.Ctx) error {
	fuelPrice, err := getDOEFuelPrice(c.UserContext())
	if err != nil {
		return err
	}

	if fuelPrice == "" {
		return c.Status(http.StatusInternalServerError).SendString("Could not get fuel price")
	}

	return c.Status(http.StatusOK).JSON(&GetFuelRecentPriceResponse{
		FuelPrice: fuelPrice,
	})
}

func getDOEFuelPrice(ctx context.Context) (string, error) {
	cachedFuelPrice, _, err := redis.GetKey[string](ctx, EIAFuelPriceRedisKey)
	if err == nil && cachedFuelPrice != "" {
		log.Info(ctx, "found cached EIA fuel price in Redis")
		return cachedFuelPrice, nil
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, EIAFuelPriceURL, nil)
	if err != nil {
		return "", fmt.Errorf("failed to build get EIA fuel price request: %w", err)
	}

	resp, err := otel.TracingHTTPClient().Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("error reading response body: %w", err)
	}

	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(respBody))
	if err != nil {
		return "", fmt.Errorf("could not parse HTML from EIA fuel price - GET response: %w", err)
	}

	var dieselValue string
	doc.Find("tr.DataRow").Each(func(_ int, row *goquery.Selection) {
		// Check if the table row contains the Diesel (On-Highway) text
		if row.Find("td.DataStub1").Text() == "Diesel (On-Highway) - All Types" {
			// Extract the value from the second-to-last cell, which refers to the current price
			dieselValue = row.Find("td.Current2").Eq(0).Text()
		}
	})

	if dieselValue == "" {
		return "", errors.New("diesel value not found")
	}

	if err = redis.SetKey(ctx, EIAFuelPriceRedisKey, dieselValue, 1*time.Hour); err != nil {
		log.WarnNoSentry(ctx, "error setting cached EIA fuel price in redis", zap.Error(err))
	}

	return dieselValue, nil
}
