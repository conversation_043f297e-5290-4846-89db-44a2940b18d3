package quoteprivate

import (
	"math"
	"net/http"
	"strings"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/integrations/pricing/dat"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	quoteCommon "github.com/drumkitai/drumkit/fn/api/routes/quote/common"
)

type (
	DATRateViewBody struct {
		OriginCity         string                  `json:"originCity"`
		OriginState        string                  `json:"originState"`
		OriginZip          string                  `json:"originZip"`
		OriginCountry      quoteCommon.CountryName `json:"originCountry"`
		DestinationCity    string                  `json:"destinationCity"`
		DestinationState   string                  `json:"destinationState"`
		DestinationZip     string                  `json:"destinationZip"`
		DestinationCountry quoteCommon.CountryName `json:"destinationCountry"`
		TransportType      models.TransportType    `json:"transportType"`
		SpecificTimeFrame  dat.RateTimeframe       `json:"specificTimeFrame"`
		SpecificAreaType   dat.LocationType        `json:"specificAreaType"`
	}

	DATRate struct {
		Target               float64 `json:"target"`
		Low                  float64 `json:"low"`
		High                 float64 `json:"high"`
		TargetPerMile        float64 `json:"targetPerMile"`
		LowPerMile           float64 `json:"lowPerMile"`
		HighPerMile          float64 `json:"highPerMile"`
		FuelSurchargePerMile float64 `json:"fuelSurchargePerMile"`
		FuelSurchargePerTrip float64 `json:"fuelSurchargePerTrip"`
	}

	DATRateViewResponse struct {
		Rate     DATRate        `json:"rate"`
		Metadata map[string]any `json:"metadata,omitempty"`
	}

	UpdatedDATRateViewError string
)

// Map external error messages to FE user-facing messages
var (
	EmptyDATLaneRate        UpdatedDATRateViewError = "emptyDATLaneRate"
	FeatureFlagNotEnabled   UpdatedDATRateViewError = "featureFlagNotEnabled"
	DataProvidedWasNotValid UpdatedDATRateViewError = "dataProvidedWasNotValid"
	DATIntegrationNotFound  UpdatedDATRateViewError = "datIntegrationNotFound"
	DATMileageIsZero        UpdatedDATRateViewError = "datMileageIsZero"
	InvalidCombination      UpdatedDATRateViewError = "invalidCombination"
	GenericDATError         UpdatedDATRateViewError = "genericDATError"

	GetUpdatedDATRateViewErrors = map[UpdatedDATRateViewError]string{
		EmptyDATLaneRate:        "DAT doesn't have any data for this lane",
		FeatureFlagNotEnabled:   "Your service does not allow for updating DAT RateView",
		DataProvidedWasNotValid: "Data provided was not valid for DAT",
		DATIntegrationNotFound:  "No DAT integration found for this service",
		DATMileageIsZero:        "Error calculating rate, DAT mileage is invalid",
		InvalidCombination: "Invalid combination of options selected. " +
			"The options selected for timeframe and area must be valid for the rate type specified.",
		GenericDATError: "Error calculating rate, please verify your inputs and try again",
	}
)

func GetUpdatedDATRateView(c *fiber.Ctx) error {
	ctx := log.With(c.UserContext())
	userServiceID := middleware.ServiceIDFromContext(c)
	email := middleware.ClaimsFromContext(c).Email

	var body DATRateViewBody
	if err := api.Parse(c, nil, nil, &body); err != nil {
		log.Error(ctx, "failed to parse request body", zap.Error(err))
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	log.Debug(ctx, "received DAT lane history request", zap.Any("request", body))

	user, err := rds.GetUserByEmail(c.UserContext(), email)
	if err != nil {
		log.Error(ctx, "failed to get user by email", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if userServiceID != user.ServiceID {
		log.Infof(
			ctx,
			"claims serviceID %d does not match user service ID %d",
			userServiceID,
			user.ServiceID,
		)

		return c.SendStatus(http.StatusUnauthorized)
	}

	service, err := rds.GetServiceWithPreload(ctx, userServiceID)
	if err != nil {
		log.Error(ctx, "could not get service", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if !service.IsUpdateRateViewEnabled {
		log.Error(ctx, "getting updated DAT rate view from service is not enabled")

		return c.Status(http.StatusUnauthorized).
			SendString(GetUpdatedDATRateViewErrors[FeatureFlagNotEnabled])
	}

	if err := quoteCommon.ValidateLocation(
		ctx,
		&body.OriginCity,
		&body.OriginState,
		&body.OriginZip,
		&body.OriginCountry,
	); err != nil {
		log.Warn(ctx, "error validating pickup", zap.Error(err))
	}

	if err := quoteCommon.ValidateLocation(
		ctx,
		&body.DestinationCity,
		&body.DestinationState,
		&body.DestinationZip,
		&body.DestinationCountry,
	); err != nil {
		log.Warn(ctx, "error validating dropoff", zap.Error(err))
	}

	// Map transport type to support DAT enum
	transportType := mapTransportType(body.TransportType)

	if transportType != string(body.TransportType) {
		log.Info(
			ctx,
			"transport type mapped to supported DAT enum",
			zap.String("transportType", transportType),
			zap.String("originalTransportType", string(body.TransportType)),
		)
	}

	log.Info(
		ctx,
		"validated pickup/dropoff locations",
		zap.String("originCity", body.OriginCity),
		zap.String("originState", body.OriginState),
		zap.String("originZip", body.OriginZip),
		zap.String("originCountry", string(body.OriginCountry)),
		zap.String("destCity", body.DestinationCity),
		zap.String("destState", body.DestinationState),
		zap.String("destZip", body.DestinationZip),
		zap.String("destCountry", string(body.DestinationCountry)),
	)

	pricingIntegrations, err := integrationDB.GetPricingByServiceID(ctx, userServiceID)
	if err != nil {
		log.Error(ctx, "error fetching pricing integration", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	var datIntegration models.Integration
	for _, integration := range pricingIntegrations {
		if integration.Name == models.DAT {
			datIntegration = integration
			break
		}
	}

	if datIntegration.ID == 0 {
		log.Error(ctx, "no DAT integration found for service")
		return c.Status(http.StatusNotFound).SendString(
			GetUpdatedDATRateViewErrors[DATIntegrationNotFound],
		)
	}

	req := []dat.RateRequest{
		{
			Origin: dat.InputLocation{
				City:            body.OriginCity,
				PostalCode:      body.OriginZip,
				StateOrProvince: body.OriginState,
			},
			Destination: dat.InputLocation{
				City:            body.DestinationCity,
				PostalCode:      body.DestinationZip,
				StateOrProvince: body.DestinationState,
			},
			RateType:  dat.SpotRateType,
			Equipment: models.TransportType(transportType),
			TargetEscalation: &dat.InputEscalation{
				EscalationType:    dat.SpecificAreaTypeAndSpecificTimeFrameEscalationType,
				SpecificTimeFrame: body.SpecificTimeFrame,
				SpecificAreaType:  body.SpecificAreaType,
			},
		},
	}

	datClient, err := dat.New(ctx, datIntegration, user.DATEmailAddress)
	if err != nil {
		log.Error(ctx, "failed to create DAT client", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	resp, err := datClient.GetLaneRate(ctx, req)
	if err != nil {
		log.Error(ctx, "failed to get DAT lane rate", zap.Error(err))

		originalErrMessage := strings.ToLower(err.Error())
		var errMessage string

		switch {
		case strings.Contains(originalErrMessage, "could not resolve origin location"):
			errMessage = GetUpdatedDATRateViewErrors[DataProvidedWasNotValid]
		case strings.Contains(originalErrMessage, "could not resolve destination location"):
			errMessage = GetUpdatedDATRateViewErrors[DataProvidedWasNotValid]
		case strings.Contains(originalErrMessage, "invalid combination"):
			errMessage = GetUpdatedDATRateViewErrors[InvalidCombination]
		default:
			errMessage = GetUpdatedDATRateViewErrors[GenericDATError]
		}

		return c.Status(http.StatusBadRequest).SendString(errMessage)
	}

	if len(resp.RateResponses) == 0 {
		log.Error(ctx, "DAT rate response is empty", zap.Error(err))
		return c.Status(http.StatusNotFound).SendString(GetUpdatedDATRateViewErrors[EmptyDATLaneRate])
	}

	log.Info(
		ctx,
		"successfully retrieved DAT lane rate",
		zap.Int("ratesCount", len(resp.RateResponses)),
	)

	if resp.RateResponses[0].Response.Rate.Mileage == 0 {
		log.Error(ctx, "DAT rate response mileage is 0")
		return c.Status(http.StatusInternalServerError).SendString(
			GetUpdatedDATRateViewErrors[DATMileageIsZero],
		)
	}

	rateData := calculateDATRate(resp.RateResponses[0].Response)

	rate := DATRate{
		Target:               rateData.Rate.PerTrip.RateUSD,
		Low:                  rateData.Rate.PerTrip.LowUSD,
		High:                 rateData.Rate.PerTrip.HighUSD,
		TargetPerMile:        rateData.Rate.PerMile.RateUSD,
		LowPerMile:           rateData.Rate.PerMile.LowUSD,
		HighPerMile:          rateData.Rate.PerMile.HighUSD,
		FuelSurchargePerMile: rateData.Rate.AverageFuelSurchargePerMileUsd,
		FuelSurchargePerTrip: rateData.Rate.AverageFuelSurchargePerTripUsd,
	}

	return c.Status(http.StatusOK).JSON(DATRateViewResponse{
		Rate: rate,
		Metadata: map[string]any{
			"reports":   rateData.Rate.Reports,
			"companies": rateData.Rate.Companies,
			"timeframe": rateData.Escalation.Timeframe,
		},
	})
}

func calculateDATRate(rateData dat.RateResponse) dat.RateResponse {
	var avgFuelPerMile float64
	var avgRatePerMile float64
	var lowRatePerMile float64
	var highRatePerMile float64

	// DAT per-mile values flaky and randomly unavailable for some lanes, so we calculate them ourselves.
	avgRatePerMile = rateData.Rate.PerTrip.RateUSD / rateData.Rate.Mileage
	lowRatePerMile = rateData.Rate.PerTrip.LowUSD / rateData.Rate.Mileage
	highRatePerMile = rateData.Rate.PerTrip.HighUSD / rateData.Rate.Mileage

	avgFuelPerTrip := rateData.Rate.AverageFuelSurchargePerTripUsd
	avgFuelPerMile = math.Round((avgFuelPerTrip/rateData.Rate.Mileage)*100) / 100

	rateData.Rate.PerMile.RateUSD = avgRatePerMile + avgFuelPerMile
	rateData.Rate.PerMile.LowUSD = lowRatePerMile + avgFuelPerMile
	rateData.Rate.PerMile.HighUSD = highRatePerMile + avgFuelPerMile
	rateData.Rate.AverageFuelSurchargePerMileUsd = avgFuelPerMile

	return rateData
}
