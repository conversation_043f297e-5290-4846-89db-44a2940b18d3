package quoteprivate

import (
	"fmt"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/integrations/customer/trident"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
)

type (
	LaneHistoryBody struct {
		EmailID          uint                 `json:"emailId"`
		ThreadID         string               `json:"threadId"`
		QuoteRequestID   uint                 `json:"quoteRequestId"`
		TransportType    models.TransportType `json:"transportType"`
		OriginDate       time.Time            `json:"originDate"`
		OriginZip        string               `json:"originZip"`
		OriginCity       string               `json:"originCity"`
		OriginState      string               `json:"originState"`
		DestinationDate  time.Time            `json:"destinationDate"`
		DestinationZip   string               `json:"destinationZip"`
		DestinationCity  string               `json:"destinationCity"`
		DestinationState string               `json:"destinationState"`
	}

	MonthResponseItem struct {
		Month       string  `json:"month"`
		Reports     int64   `json:"reports"`
		HighRate    float64 `json:"highRate"`
		P75Rate     float64 `json:"p75Rate"`
		LowRate     float64 `json:"lowRate"`
		P25Rate     float64 `json:"p25Rate"`
		AverageRate float64 `json:"averageRate"`
	}

	Last7DaysResponse struct {
		Reports     int64   `json:"reports"`
		AverageRate float64 `json:"averageRate"`
		LowRate     float64 `json:"lowRate"`
		HighRate    float64 `json:"highRate"`
	}

	LaneHistoryFromServiceResponse struct {
		Origin      string              `json:"origin"`
		Destination string              `json:"destination"`
		Equipment   string              `json:"equipment"`
		Months      []MonthResponseItem `json:"months"`
		Last7Days   Last7DaysResponse   `json:"last7Days"`
	}
)

func GetLaneHistoryFromService(c *fiber.Ctx) error {
	var body LaneHistoryBody
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()
	userServiceID := middleware.ServiceIDFromContext(c)
	log.With(ctx, zap.String("serviceID", fmt.Sprint(userServiceID)))

	service, err := rds.GetServiceWithPreload(ctx, userServiceID)
	if err != nil {
		log.Error(ctx, "could not get service", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if !service.IsGetLaneRateFromServiceEnabled {
		log.Error(ctx, "getting lane history from service is not enabled")
		return c.SendStatus(http.StatusInternalServerError)
	}

	email := middleware.ClaimsFromContext(c).Email
	_, err = userDB.GetByEmail(ctx, email)
	if err != nil {
		log.Warn(ctx, "could not get user by email", zap.Error(err))
	}

	// No feature flag here because multiple services may use this feature in the future so we need
	// a different way to enable it for each service
	if service.ID != TridentTransportID {
		log.Warn(ctx, "skipping lane history from service since service is not Trident")
		return c.Status(http.StatusBadRequest).SendString("Unrecognized Service")
	}

	// First, need to make sure zip codes aren't empty
	if body.OriginZip == "" {
		originZip, err := helpers.LookupZipCodeByCityState(ctx, body.OriginCity, body.OriginState)
		if err != nil {
			log.Error(
				ctx, "error looking up origin zip code",
				zap.Error(err),
				zap.String("originCity", body.OriginCity),
				zap.String("originState", body.OriginState),
			)
			return c.SendStatus(http.StatusInternalServerError)
		}

		if len(originZip) == 0 {
			log.Error(
				ctx,
				"could not find any zip code for origin city/state",
				zap.String("originCity", body.OriginCity),
				zap.String("originState", body.OriginState),
			)
			return c.SendStatus(http.StatusInternalServerError)
		}

		body.OriginZip = originZip
	}

	if body.DestinationZip == "" {
		destZip, err := helpers.LookupZipCodeByCityState(ctx, body.DestinationCity, body.DestinationState)
		if err != nil {
			log.Error(
				ctx,
				"error looking up destination zip code",
				zap.Error(err),
				zap.String("destinationCity", body.DestinationCity),
				zap.String("destinationState", body.DestinationState),
			)

			return c.SendStatus(http.StatusInternalServerError)
		}

		if len(destZip) == 0 {
			log.Error(
				ctx,
				"could not find any zip code for destination city/state",
				zap.String("destinationCity", body.DestinationCity),
				zap.String("destinationState", body.DestinationState),
			)
			return c.SendStatus(http.StatusInternalServerError)
		}

		body.DestinationZip = destZip
	}

	// If pickup date isn't provided, set it to:
	// - today + 1 day if delivery date also isn't provided
	// - delivery date - 1 day otherwise, assuming 1 day delivery
	if body.OriginDate.IsZero() {
		if !body.DestinationDate.IsZero() {
			body.OriginDate = body.DestinationDate.AddDate(0, 0, -1)
		} else {
			body.OriginDate = time.Now().AddDate(0, 0, 1)
		}
	}

	// If delivery date isn't provided, set it to:
	// - today + 2 days if pickup date also isn't provided
	// - pickup date + 1 day otherwise, assuming 1 day delivery
	if body.DestinationDate.IsZero() {
		if !body.OriginDate.IsZero() {
			body.DestinationDate = body.OriginDate.AddDate(0, 0, 1)
		} else {
			body.DestinationDate = time.Now().AddDate(0, 0, 2)
		}
	}

	integration, err := integrationDB.GetByServiceIDAndType(ctx, service.ID, models.CustomerType)
	if err != nil {
		log.Error(ctx, "integrationDB.GetByServiceIDAndType: %w", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	client, err := trident.New(ctx, integration)
	if err != nil {
		log.Error(ctx, "error creating trident client", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	resp, err := client.GetLaneHistory(ctx, trident.LaneHistoryRequest{
		OriginZip:      body.OriginZip,
		DestinationZip: body.DestinationZip,
		Equipment:      string(body.TransportType),
	})
	if err != nil {
		log.Error(ctx, "unable to get Trident lane history", zap.Error(err))
		return c.Status(http.StatusInternalServerError).
			SendString("Trident's DAT integration wasn't able to get lane history")
	}

	numericalHistoryData, err := trident.ParseCalculateLaneHistory(ctx, resp)
	if err != nil {
		log.Error(ctx, "unable to convert Trident lane history numerical data", zap.Error(err))
		return c.Status(http.StatusInternalServerError).
			SendString("Trident's DAT integration wasn't able to convert lane history numerical data")
	}

	// Calculate month names (assuming last month is current month and going back)
	now := time.Now()
	monthNames := []string{
		now.AddDate(0, -5, 0).Format("January 2006"),
		now.AddDate(0, -4, 0).Format("January 2006"),
		now.AddDate(0, -3, 0).Format("January 2006"),
		now.AddDate(0, -2, 0).Format("January 2006"),
		now.AddDate(0, -1, 0).Format("January 2006"),
		now.Format("January 2006"),
	}

	// Create monthly data array
	months := []MonthResponseItem{
		{
			Month:       monthNames[0],
			Reports:     numericalHistoryData.ReportsMonth1,
			HighRate:    numericalHistoryData.HighMonth1,
			P75Rate:     numericalHistoryData.P75Month1,
			LowRate:     numericalHistoryData.LowMonth1,
			P25Rate:     numericalHistoryData.P25Month1,
			AverageRate: numericalHistoryData.RateMonth1,
		},
		{
			Month:       monthNames[1],
			Reports:     numericalHistoryData.ReportsMonth2,
			HighRate:    numericalHistoryData.HighMonth2,
			P75Rate:     numericalHistoryData.P75Month2,
			LowRate:     numericalHistoryData.LowMonth2,
			P25Rate:     numericalHistoryData.P25Month2,
			AverageRate: numericalHistoryData.RateMonth2,
		},
		{
			Month:       monthNames[2],
			Reports:     numericalHistoryData.ReportsMonth3,
			HighRate:    numericalHistoryData.HighMonth3,
			P75Rate:     numericalHistoryData.P75Month3,
			LowRate:     numericalHistoryData.LowMonth3,
			P25Rate:     numericalHistoryData.P25Month3,
			AverageRate: numericalHistoryData.RateMonth3,
		},
		{
			Month:       monthNames[3],
			Reports:     numericalHistoryData.ReportsMonth4,
			HighRate:    numericalHistoryData.HighMonth4,
			P75Rate:     numericalHistoryData.P75Month4,
			LowRate:     numericalHistoryData.LowMonth4,
			P25Rate:     numericalHistoryData.P25Month4,
			AverageRate: numericalHistoryData.RateMonth4,
		},
		{
			Month:       monthNames[4],
			Reports:     numericalHistoryData.ReportsMonth5,
			HighRate:    numericalHistoryData.HighMonth5,
			P75Rate:     numericalHistoryData.P75Month5,
			LowRate:     numericalHistoryData.LowMonth5,
			P25Rate:     numericalHistoryData.P25Month5,
			AverageRate: numericalHistoryData.RateMonth5,
		},
		{
			Month:       monthNames[5],
			Reports:     numericalHistoryData.ReportsMonth6,
			HighRate:    numericalHistoryData.HighMonth6,
			P75Rate:     numericalHistoryData.P75Month6,
			LowRate:     numericalHistoryData.LowMonth6,
			P25Rate:     numericalHistoryData.P25Month6,
			AverageRate: numericalHistoryData.RateMonth6,
		},
	}

	// Create last 7 days data
	last7Days := Last7DaysResponse{
		Reports:     numericalHistoryData.ReportsWeek,
		AverageRate: numericalHistoryData.AvgRateWeek,
		LowRate:     numericalHistoryData.AvgLowWeek,
		HighRate:    numericalHistoryData.AvgHighWeek,
	}

	return c.Status(http.StatusOK).JSON(LaneHistoryFromServiceResponse{
		Origin:      resp.Origin,
		Destination: resp.Destination,
		Equipment:   resp.Equipment,
		Months:      months,
		Last7Days:   last7Days,
	})
}
