package quoteprivate

import (
	"context"
	"time"

	"github.com/drumkitai/drumkit/common/integrations/pricing/dat"
	"github.com/drumkitai/drumkit/common/models"
	quote "github.com/drumkitai/drumkit/fn/api/routes/quote/common"
)

// QuickQuoteHandlers interface for dependency injection for unit testing
type QuickQuoteHandlers interface {
	// DAT operations
	GetDATLaneRate(
		ctx context.Context,
		integration models.Integration,
		datEmailAddress string,
		req *[]dat.RateRequest,
	) (*dat.GetLaneRateResponse, error)

	// Quote record operations
	CreateQuoteRecord(
		ctx context.Context,
		service models.Service,
		userID, emailID uint,
		threadID string, quoteRequestID uint,
		stops []models.Stop,
		transportType models.TransportType,
		pickupDate, deliveryDate time.Time,
		rateData quote.RateData,
		targetSellCost, minTargetSellCost, maxTargetSellCost float64,
		source models.QuoteSource,
		typeInSource models.QuoteTypeInSource,
		datMetadata *models.QuoteDATMetadata,
	) *models.QuickQuote

	// Redis operations
	SetRedisMileDistanceBetweenLocations(
		ctx context.Context,
		originCity, originState, destinationCity, destinationState string,
		mileDistance float64,
	) error
}

// Concrete implementation for production
type QuickQuoteHandlersImpl struct{}

func (h *QuickQuoteHandlersImpl) GetDATLaneRate(
	ctx context.Context,
	integration models.Integration,
	datEmailAddress string,
	req *[]dat.RateRequest,
) (*dat.GetLaneRateResponse, error) {
	return quote.GetDATLaneRate(ctx, integration, datEmailAddress, req)
}

func (h *QuickQuoteHandlersImpl) CreateQuoteRecord(
	ctx context.Context,
	service models.Service,
	userID, emailID uint,
	threadID string, quoteRequestID uint,
	stops []models.Stop,
	transportType models.TransportType,
	pickupDate, deliveryDate time.Time,
	rateData quote.RateData,
	targetSellCost, minTargetSellCost, maxTargetSellCost float64,
	source models.QuoteSource,
	typeInSource models.QuoteTypeInSource,
	datMetadata *models.QuoteDATMetadata,
) *models.QuickQuote {
	return quote.CreateQuoteRecord(
		ctx,
		service,
		userID,
		emailID,
		threadID,
		quoteRequestID,
		stops,
		transportType,
		pickupDate,
		deliveryDate,
		rateData,
		targetSellCost,
		minTargetSellCost,
		maxTargetSellCost,
		source,
		typeInSource,
		datMetadata,
	)
}

func (h *QuickQuoteHandlersImpl) SetRedisMileDistanceBetweenLocations(
	ctx context.Context,
	originCity, originState, destinationCity, destinationState string,
	mileDistance float64,
) error {
	return quote.SetRedisMileDistanceBetweenLocations(
		ctx,
		originCity,
		originState,
		destinationCity,
		destinationState,
		mileDistance,
	)
}
