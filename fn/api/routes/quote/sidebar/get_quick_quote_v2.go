package quoteprivate

import (
	"context"
	"errors"
	"fmt"
	"math"
	"net/http"
	"regexp"
	"slices"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/integrations/pricing/dat"
	"github.com/drumkitai/drumkit/common/integrations/pricing/greenscreens"
	"github.com/drumkitai/drumkit/common/integrations/pricing/truckstop"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	emailDB "github.com/drumkitai/drumkit/common/rds/email"
	emailTemplateDB "github.com/drumkitai/drumkit/common/rds/emailtemplates"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	quoteRequestDB "github.com/drumkitai/drumkit/common/rds/quote_request"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	userGroupsDB "github.com/drumkitai/drumkit/common/rds/usergroups"
	quote "github.com/drumkitai/drumkit/fn/api/routes/quote/common"
	quoteutil "github.com/drumkitai/drumkit/fn/api/routes/quote/util"
)

const (
	IntermediateStopFeeUSD = 200

	// The minimum distance required to prefer DAT/Drumkit leg-to-leg quote over DAT/Drumkit LongestLeg quote
	LegToLegMinimumDistance = 75
)

type (
	Stop struct {
		Order   int               `json:"order"`
		Country quote.CountryName `json:"country,omitempty"`
		State   string            `json:"state"`
		City    string            `json:"city"`
		Zip     string            `json:"zip" validate:"required_without_all=City State"`
	}

	RateValues struct {
		Target float64 `json:"target,omitempty"` // Maps to: TargetBuy, PredictedRate, TargetPerTrip
		Low    float64 `json:"low,omitempty"`    // Maps to: LowerRate, LowPerTrip
		High   float64 `json:"high,omitempty"`   // Maps to: UpperRate, HighPerTrip

		// Per-mile rates (when available)
		TargetPerMile float64 `json:"targetPerMile,omitempty"`
		LowPerMile    float64 `json:"lowPerMile,omitempty"`
		HighPerMile   float64 `json:"highPerMile,omitempty"`
	}

	Quote struct {
		ID     uint                     `json:"id"`
		Source models.QuoteSource       `json:"source"`         // Such as "Greenscreens", "DAT" or "TruckStop"
		Type   models.QuoteTypeInSource `json:"type,omitempty"` // Such as "GS Buy Power" or "GS Network"

		// Common fields across sources
		Rates    RateValues `json:"rates"`
		Distance float64    `json:"distance,omitempty"`

		// Provider-specific metadata
		Metadata map[string]any `json:"metadata,omitempty"`
	}

	QuoteError struct {
		Source models.QuoteSource `json:"source"`
		Error  string             `json:"error"`
	}

	RouteLeg struct {
		Order          int        `json:"order"` // Order of the leg in the route
		StartStopIndex int        `json:"startStopIndex"`
		EndStopIndex   int        `json:"endStopIndex"`
		StartCityState string     `json:"startCityState"`
		EndCityState   string     `json:"endCityState"`
		DistanceMiles  float64    `json:"distanceMiles"`
		Rates          RateValues `json:"rates"`
	}

	Configuration struct {
		FSCProvider               string `json:"fscProvider,omitempty"`
		LowConfidenceThreshold    int    `json:"lowConfidenceThreshold,omitempty"`
		MediumConfidenceThreshold int    `json:"mediumConfidenceThreshold,omitempty"`
		BelowThresholdMessage     string `json:"belowThresholdMessage,omitempty"`
		DefaultPercentMargin      int    `json:"defaultPercentMargin,omitempty"`
		DefaultFlatMargin         int    `json:"defaultFlatMargin,omitempty"`
	}

	QuickQuotePrivateBody struct {
		LoadID string `json:"loadId"` // Load info
		//nolint:lll
		TransportType models.TransportType `json:"transportType" validate:"oneof=VAN REEFER FLATBED 'BOX TRUCK' HOTSHOT SPRINTER"`
		// TODO: Keep the old Stops backwards compatible until users have upgraded past v.0.48.0.
		Stops []Stop `json:"stops"`
		// NOTE: This is the new Stops array that supports >2 stops.
		NewStops []models.Stop `json:"newStops"`
		// Optional; If not provided, defaults to today + 1 day for compatibility with downstream integrations
		PickupDate time.Time `json:"pickupDate"`
		// Optional; If not provided, defaults to today + 2 days for compatibility with downstream integrations
		DeliveryDate         time.Time `json:"deliveryDate"`
		CustomerName         string    `json:"customerName"` // This is the customer external TMS ID
		EmailID              uint      `json:"emailID"`      // Email the user was looking at during submission
		ThreadID             string    `json:"threadID"`     // Thread from the email related Email
		QuoteRequestID       uint      `json:"quoteRequestId"`
		SelectedQuickQuoteID *uint     `json:"selectedQuickQuoteId,omitempty"`
	}

	QuickQuoteEmailTemplateResponse struct {
		Subject string `json:"subject"`
		Body    string `json:"body"`
	}

	QuickQuotePrivateResponse struct {
		QuoteRequestID  uint `json:"quoteRequestId"`
		IsZipCodeLookup bool `json:"isZipCodeLookup"`

		Stops            []Stop                 `json:"stops"`
		SelectedRateName quote.SelectedRateName `json:"selectedRateName"` // Which rate does Drumkit default to?
		// HotShot and Box Truck are proxied with Flatbed and Van, respectively.
		// Use the following to inform users of any proxies used to complete request.
		InputtedTransportType  models.TransportType `json:"inputtedTransportType"`
		SubmittedTransportType models.TransportType `json:"submittedTransportType"`

		Configuration Configuration `json:"configuration"`

		Quotes      []Quote      `json:"quotes"`
		QuoteErrors []QuoteError `json:"quoteErrors"`

		// QuoteReplyDraftTemplate is the email template for the reply to the email used for quoting.
		QuoteReplyDraftTemplate QuickQuoteEmailTemplateResponse `json:"quoteReplyDraftTemplate,omitempty"`
	}

	ErrorData struct {
		Error string `json:"error"`
	}

	// Structured location validation error types for multi-stop support
	// FE validates for expected formats (\d{5} for US, [A-Z0-9]{6} for Canada)
	// but only BE can call API to verify location exists
	ValidationError struct {
		Type    string                `json:"type"`    // "validation_error"
		Message string                `json:"message"` // Human-readable summary
		Details []StopValidationError `json:"details"` // Specific stop errors
	}

	StopValidationError struct {
		StopIndex int    `json:"stopIndex"` // 0-based index
		Field     string `json:"field"`     // "location", "zipCode", etc.
		Message   string `json:"message"`   // Specific error message
		Value     string `json:"value"`     // The invalid value that was provided
	}
)

// Error implements the error interface for ValidationError
func (v *ValidationError) Error() string {
	return v.Message
}

func GetQuickQuoteV2(c *fiber.Ctx) error {
	var body QuickQuotePrivateBody
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()
	userServiceID := middleware.ServiceIDFromContext(c)
	userID := middleware.UserIDFromContext(c)

	ctx = log.With(ctx, zap.Any("requestBody", body))

	service, err := rds.GetServiceWithPreload(ctx, userServiceID)
	if err != nil {
		log.Error(ctx, "could not get service", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	user, err := userDB.GetByID(ctx, userID)
	if err != nil {
		log.Error(ctx, "could not get user", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	customer, pickupDate, deliveryDate, err := quoteutil.PrepareCustomerAndDates(
		ctx,
		service,
		body.CustomerName,
		body.PickupDate,
		body.DeliveryDate,
	)
	if err != nil {
		// Fail-open on customer look up error
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			log.Error(ctx, "error while preparing customer/dates", zap.Error(err))
		}
	}

	body.PickupDate = pickupDate
	body.DeliveryDate = deliveryDate

	resp, err := getQuickQuotes(ctx, &body, service, user, customer)
	if err != nil {
		originStr, destStr := getOriginDestinationStrFromStops(body.Stops)

		log.Error(
			ctx,
			"getQuickQuotes failed",
			zap.String("origin", originStr),
			zap.String("destination", destStr),
			zap.Error(err),
		)

		// For new multi-stop format, return structured validation error with 400 status for invalid location
		// errors.
		var validationErr *ValidationError
		if errors.As(err, &validationErr) {
			return c.Status(http.StatusBadRequest).JSON(validationErr)
		}

		// Return legacy string error with 503 status for server errors.
		return c.Status(http.StatusServiceUnavailable).JSON(ErrorData{
			Error: err.Error(),
		})
	}

	// If getPrivateQuickQuote succeeds, update the parent quote request status to inFlight and store the
	// suggested quote request fields as applied fields.
	pickupLocation := models.Address{
		City:  resp.Stops[0].City,
		State: resp.Stops[0].State,
		Zip:   resp.Stops[0].Zip,
	}

	dropoffLocation := models.Address{
		City:  resp.Stops[len(resp.Stops)-1].City,
		State: resp.Stops[len(resp.Stops)-1].State,
		Zip:   resp.Stops[len(resp.Stops)-1].Zip,
	}

	appliedFields := models.QuoteLoadInfo{
		CustomerID:       customer.ID,
		TransportType:    resp.InputtedTransportType,
		PickupLocation:   pickupLocation,
		PickupDate:       models.NullTime{Time: body.PickupDate, Valid: true},
		DeliveryLocation: dropoffLocation,
		DeliveryDate:     models.NullTime{Time: body.DeliveryDate, Valid: true},
		Stops:            body.NewStops,
	}

	err = quoteRequestDB.UpdateQuoteRequestStatus(
		ctx,
		body.QuoteRequestID,
		&appliedFields,
		&user,
		models.InFlight,
		nil,
		body.SelectedQuickQuoteID,
	)
	if err != nil {
		log.Error(
			ctx,
			"failed to update quote request status",
			zap.Uint("quoteRequestID", body.QuoteRequestID),
			zap.Error(err),
		)
	}

	// Quote requests are typically referenced via a clicked suggestion in the inbox UI.
	// However, since users can now access the sidebar from outside the inbox
	// (where no email suggestions exist), we include the quote request ID in the
	// response to maintain frontend reference tracking.
	resp.QuoteRequestID = body.QuoteRequestID

	var lowConfidenceThreshold int
	if service.QuickQuoteConfig != nil {
		lowConfidenceThreshold = service.QuickQuoteConfig.LowConfidenceThreshold
	}

	// Confidence values are specific for Greenscreens, so if confidence is low for all GS rates
	// and service doesn't omit low confidence warning, add threshold message to the response.
	showLowConfWarning := showLowConfidenceWarning(lowConfidenceThreshold, resp.Quotes)
	if showLowConfWarning && !service.QuickQuoteConfig.OmitUnderLowThreshold {
		resp.Configuration.BelowThresholdMessage = service.QuickQuoteConfig.BelowThresholdMessage
	}

	// Prepare the email template for the user's reply email.
	quoteReplyTemplate := getQuoteReplyTemplate(ctx, user)

	resp.QuoteReplyDraftTemplate = QuickQuoteEmailTemplateResponse{
		Subject: quoteReplyTemplate.Subject,
		Body:    quoteReplyTemplate.Body,
	}

	return c.Status(http.StatusOK).JSON(resp)
}

func mapTransportType(transportType models.TransportType) string {
	// Greenscreens and DAT do not support HOTSHOT, BOX TRUCK, or SPRINTER,
	// so these types are proxied with Flatbed and Van respectively
	switch t := strings.ToUpper(string(transportType)); t {
	case "HOTSHOT":
		return "FLATBED"
	case "BOX TRUCK", "BOXTRUCK":
		return "VAN"
	case "SPRINTER":
		return "VAN"
	default:
		return t
	}
}

// getQuickQuotes fetches quotes from Greenscreens, DAT, and Truckstop,
// depending on the service's available integrations.
func getQuickQuotes(
	ctx context.Context,
	body *QuickQuotePrivateBody,
	service models.Service,
	user models.User,
	customer models.TMSCustomer,
) (_ *QuickQuotePrivateResponse, err error) {

	ctx, metaSpan := otel.StartSpan(ctx, "getQuickQuotes", nil)
	defer func() { metaSpan.End(err) }()

	// Greenscreens rates may vary between adjacent ZIP codes, even within the same city
	// In order to align with the rates on the website, we need to track if the user is looking up
	// via zipcode or not.
	//nolint:lll
	// Check here for more info: https://linear.app/drumkit/issue/ENG-3749/verify-gs-quote-discrepancies-for-specified-routes-in-drumkit
	var isZipCodeLookup bool
	var quotes = []Quote{}
	var quoteErrors = []QuoteError{}
	var selectedRateName quote.SelectedRateName
	var stops []models.Stop
	transportTypeInput := mapTransportType(body.TransportType)

	pricingIntegrations, err := integrationDB.GetPricingByServiceID(ctx, service.ID)
	if err != nil {
		return nil, fmt.Errorf("error fetching pricing integration: %w", err)
	}
	if len(pricingIntegrations) == 0 {
		return nil, errors.New("no pricing integrations found for service")
	}

	// NOTE: Handle stop processing for rate prediction.
	// Legacy format only supports exactly 2 stops (pickup and dropoff).
	// while new format supports multiple stops (pickup, intermediate stops, and dropoff)
	// Old Drumkit versions submit both `stops` and `newStops` in req but
	// new versions with multi-stop UI submit only `newStops`
	if service.IsMultiStopQuickQuoteEnabled && len(body.NewStops) > 0 && len(body.Stops) == 0 {
		log.Info(ctx, "validating new stops", zap.Any("stops", body.NewStops))

		stops, isZipCodeLookup, err = validateNewStops(ctx, body.NewStops)
		if err != nil {
			var validationErr *ValidationError
			if errors.As(err, &validationErr) {
				return nil, validationErr
			}

			// Return legacy string error for backward compatibility
			return nil, fmt.Errorf("failed to validate new stops: %w", err)
		}
	} else {
		log.Info(ctx, "validating legacy stops", zap.Any("stops", body.Stops))
		// Legacy format: Process original two-stop format (pickup and dropoff only).
		// This maintains backward compatibility with existing logic.
		stops, isZipCodeLookup, err = convertAndValidateLegacyStops(ctx, body.Stops)
		if err != nil {
			return nil, fmt.Errorf("failed to validate legacy stops: %w", err)
		}
	}
	// Re-assign the validated stops to the body so RDS has all 3 address fields populated
	// NOTE: From this point forth, handlers should NOT use body.Stops or body.NewStops but instead use `stops`
	body.NewStops = stops

	// Before fetching external quotes, ensure a parent quote request exists
	if len(stops) < 2 {
		return nil, errors.New("at least two stops are required")
	}

	startStop := stops[0]
	endStop := stops[len(stops)-1]

	if body.QuoteRequestID == 0 {
		pickupLocation := models.Address{
			City:    startStop.Address.City,
			State:   startStop.Address.State,
			Zip:     startStop.Address.Zip,
			Country: startStop.Address.Country,
		}

		dropoffLocation := models.Address{
			City:    endStop.Address.City,
			State:   endStop.Address.State,
			Zip:     endStop.Address.Zip,
			Country: endStop.Address.Country,
		}

		appliedFields := models.QuoteLoadInfo{
			CustomerID:       customer.ID,
			TransportType:    body.TransportType,
			PickupLocation:   pickupLocation,
			PickupDate:       models.NullTime{Time: body.PickupDate, Valid: true},
			DeliveryLocation: dropoffLocation,
			DeliveryDate:     models.NullTime{Time: body.DeliveryDate, Valid: true},
			Stops:            stops,
		}

		// Get email's RFCMessageID
		rfcMessageID := ""
		if body.EmailID != 0 {
			email, err := emailDB.GetByID(ctx, body.EmailID)
			if err != nil {
				log.Warn(ctx, "error getting email by ID", zap.Error(err))
			}
			rfcMessageID = email.RFCMessageID
		}

		parentQuoteRequest := models.QuoteRequest{
			UserID:       user.ID,
			EmailID:      body.EmailID,  // may be 0 if user is using a portal
			ThreadID:     body.ThreadID, // may be "" if user is using a portal
			RFCMessageID: rfcMessageID,
			// TODO: Add source to payload to capture portal QQs w/o suggestions
			ServiceID:            service.ID,
			AppliedRequest:       appliedFields,
			Status:               models.Pending, // we update to inFlight once this function succeeds
			SelectedQuickQuoteID: body.SelectedQuickQuoteID,
		}

		err = quoteRequestDB.CreateQuoteRequest(ctx, &parentQuoteRequest)
		if err != nil {
			return nil, fmt.Errorf("error creating parent quote request: %w", err)
		}

		body.QuoteRequestID = parentQuoteRequest.ID

	}

	// Fetch quotes from each integration available for the service
	for _, integration := range pricingIntegrations {
		switch integration.Name {
		case models.DAT:
			if !user.HasGrantedDATPermissions {
				log.Warn(ctx, "user fetched quote but has not granted DAT permissions")

				quoteErrors = append(quoteErrors, QuoteError{
					Source: models.DATSource,
					Error:  fmt.Sprintf("User %s does not have an associated DAT account", user.EmailAddress),
				})

				continue
			}

			// Create concrete implementation for production use
			handlers := &QuickQuoteHandlersImpl{}

			if len(stops) == 2 {
				// Handle basic 2-stop quote
				twoStopQuote, err := handleDATTwoStop(
					ctx,
					&service,
					&user,
					&integration,
					body,
					stops,
					models.TransportType(transportTypeInput),
					true,
					handlers,
				)
				if err != nil {
					log.Error(ctx, "failed to get DAT quote", zap.Error(err))

					quoteErrors = append(quoteErrors, QuoteError{
						Source: models.DATSource,
						Error:  getDATQuoteErrorMsg(ctx, err, user, integration),
					})

					continue
				}

				// Default selected rate to DAT when integration is available for service/user
				selectedRateName = quote.DATRateView
				quotes = append(quotes, *twoStopQuote)

				continue
			}

			// Otherwise if multi-stop, first get leg-to-leg calculation
			var legs []RouteLeg
			var totalDistance float64
			legToLegQuote, legToLegErr := handleDATMultiStopLegToLeg(
				ctx,
				&service,
				&user,
				&integration,
				body,
				stops,
				models.TransportType(transportTypeInput),
				handlers,
			)
			if legToLegErr != nil {
				log.Error(ctx, "failed to get leg-to-leg DAT quote", zap.Error(legToLegErr))

				quoteErrors = append(quoteErrors, QuoteError{
					Source: models.DATSource,
					Error:  getDATQuoteErrorMsg(ctx, legToLegErr, user, integration),
				})
			} else {
				quotes = append(quotes, *legToLegQuote)
				legs = legToLegQuote.Metadata["legs"].([]RouteLeg)
				totalDistance = legToLegQuote.Distance
			}

			// Get longest leg quote
			// If there's an error with getting the LegToLeg but not LongestLeg, then FE will still show
			// quote card for the latter but not the former, and there won't be collapsed legs data
			longestLegQuote, longestLegErr := handleDATMultiStopLongestLeg(
				ctx,
				&service,
				&user,
				&integration,
				body,
				stops,
				models.TransportType(transportTypeInput),
				totalDistance, // Leverage total distance from leg-to-leg quote
				handlers,
			)
			if longestLegErr != nil {
				log.Error(ctx, "failed to get longest leg DAT quote", zap.Error(longestLegErr))

				quoteErrors = append(quoteErrors, QuoteError{
					Source: models.DATSource,
					Error:  getDATQuoteErrorMsg(ctx, longestLegErr, user, integration),
				})
			} else {
				quotes = append(quotes, *longestLegQuote)
			}

			// If Greenscreens is not available, select the best method based on the distance of the legs
			if !slices.ContainsFunc(pricingIntegrations, func(integration models.Integration) bool {
				return integration.Name == models.Greenscreens
			}) {
				if legToLegErr == nil && longestLegErr == nil {
					// If any leg is less than the minimum distance, use the longest leg method
					if slices.ContainsFunc(legs, func(leg RouteLeg) bool {
						return leg.DistanceMiles < LegToLegMinimumDistance
					}) {
						selectedRateName = quote.SelectedRateName(models.DATMultiStopLongestLeg)
					} else {
						selectedRateName = quote.SelectedRateName(models.DATMultiStopLegToLeg)
					}
					continue
				}
				// If one of the quotes failed, use the other quote
				selectedRateName = helpers.Ternary(
					legToLegErr == nil,
					quote.SelectedRateName(models.DATMultiStopLegToLeg),
					quote.SelectedRateName(models.DATMultiStopLongestLeg))
			}

		case models.Greenscreens:
			gsQuotes, err := handleGreenscreens(
				ctx,
				&service,
				&user,
				&integration,
				body,
				stops,
				models.TransportType(transportTypeInput),
				isZipCodeLookup,
			)
			if err != nil {
				log.Error(ctx, "failed to get Greenscreens quote", zap.Error(err))

				quoteErrors = append(quoteErrors, QuoteError{
					Source: models.GreenscreensSource,
					Error:  getGreenscreensQuoteErrorMsg(err, integration),
				})

				continue
			}

			quotes = append(quotes, gsQuotes...)

		case models.Truckstop:
			truckStopQuotes, err := handleTruckstop(
				ctx,
				&service,
				&user,
				&integration,
				body,
				stops,
				models.TransportType(transportTypeInput),
			)
			if err != nil {
				log.Error(ctx, "failed to get Truckstop quote", zap.Error(err))
				continue
			}

			quotes = append(quotes, truckStopQuotes...)
		}
	}

	var config Configuration
	if service.QuickQuoteConfig != nil {
		config = Configuration{
			LowConfidenceThreshold:    service.QuickQuoteConfig.LowConfidenceThreshold,
			MediumConfidenceThreshold: service.QuickQuoteConfig.MediumConfidenceThreshold,
			DefaultPercentMargin:      service.QuickQuoteConfig.DefaultPercentMargin,
			DefaultFlatMargin:         service.QuickQuoteConfig.DefaultFlatMargin,
			FSCProvider:               service.QuickQuoteConfig.FSCProvider,
		}
	}

	// Convert stops to FE format
	var respStops []Stop
	for _, stop := range stops {
		stopCountry := quote.CountryName(stop.Address.Country)
		respStops = append(respStops, Stop{
			Order:   stop.Order,
			Country: stopCountry,
			State:   stop.Address.State,
			City:    stop.Address.City,
			Zip:     stop.Address.Zip,
		})
	}

	return &QuickQuotePrivateResponse{
		Stops:                  respStops,
		SelectedRateName:       selectedRateName,
		InputtedTransportType:  body.TransportType,
		SubmittedTransportType: models.TransportType(transportTypeInput),
		Quotes:                 quotes,
		QuoteErrors:            quoteErrors,
		Configuration:          config,
		IsZipCodeLookup:        isZipCodeLookup,
	}, err
}

// convertAndValidateLegacyStops validates the legacy Stop format and converts it to the new models.Stop format,
// populating city, state and zip for each stop.
// It also returns a boolean indicating whether the original request was a zipcode lookup.
func convertAndValidateLegacyStops(ctx context.Context, inputStops []Stop) ([]models.Stop, bool, error) {
	var stops []models.Stop
	var validationErrors []StopValidationError
	var isZipcodeLookup bool

	for i, item := range inputStops {
		stop := models.Stop{
			Order:      i,
			StopNumber: i,
			Address: models.Address{
				Country: string(item.Country),
				State:   item.State,
				City:    item.City,
				Zip:     item.Zip,
			},
		}

		if !helpers.IsBlank(item.Zip) {
			isZipcodeLookup = true
		}

		// Validate the stop and collect any validation errors
		if validationErr := validateStop(ctx, &stop, i); validationErr != nil {
			validationErrors = append(validationErrors, *validationErr)
			continue // Continue processing other stops even if this one fails
		}

		stops = append(stops, stop)
	}

	// If there were validation errors, return a legacy string error for backward compatibility
	if len(validationErrors) > 0 {
		// Create a legacy string error message for backward compatibility
		var errorMsgs []string

		for _, validationErr := range validationErrors {
			errorMsgs = append(
				errorMsgs,
				fmt.Sprintf("stop %d: %s", validationErr.StopIndex, validationErr.Message),
			)
		}

		return nil, false, fmt.Errorf("validation failed: %s", strings.Join(errorMsgs, "; "))
	}

	return stops, isZipcodeLookup, nil
}

// TODO: isZipcodeLookup needs to be at a per-stop level
func validateNewStops(ctx context.Context, inputStops []models.Stop) ([]models.Stop, bool, error) {
	var stops []models.Stop
	var validationErrors []StopValidationError
	var isZipcodeLookup bool

	for i, stop := range inputStops {
		stop.Order = stop.StopNumber // Assign for backwards compatibility
		if !helpers.IsBlank(stop.Address.Zip) {
			isZipcodeLookup = true
		}

		// Validate the stop and collect any validation errors
		if validationErr := validateStop(ctx, &stop, i); validationErr != nil {
			validationErrors = append(validationErrors, *validationErr)
			continue // Continue processing other stops even if this one fails
		}

		// If validation passed, update the country and add to valid stops
		countryName := quote.CountryName(stop.Address.Country)
		stop.Address.Country = string(countryName)
		stops = append(stops, stop)
	}

	// If there were validation errors, return a structured error
	if len(validationErrors) > 0 {
		return nil, false, createValidationError(validationErrors)
	}

	return stops, isZipcodeLookup, nil
}

// handleGreenscreens handles the logic for getting a Greenscreens quote. Unlike some other integrations,
//
// Greenscreens supports both 2-stop and multi-stop quotes natively.
func handleGreenscreens(
	ctx context.Context,
	service *models.Service,
	user *models.User,
	integration *models.Integration,
	body *QuickQuotePrivateBody,
	stops []models.Stop,
	transportTypeInput models.TransportType,
	isZipCodeLookup bool,
) ([]Quote, error) {

	var quotes []Quote
	var err error

	ctx, metaSpan := otel.StartSpan(ctx, "handleGreenscreens", nil)
	defer func() { metaSpan.End(err) }()

	laneRatePrediction, networkLaneRatePrediction, _, err := quote.
		FetchGreenscreensInfo(
			ctx,
			*service,
			*integration,
			stops,
			user.EmailAddress,
			transportTypeInput,
			body.PickupDate,
			isZipCodeLookup,
		)
	if err != nil {
		// This is a backwards-compatible approach for handling low confidence rates
		// when OmitUnderLowThreshold is enabled.
		//
		// Previously, we would halt all logic and only return an ErrNoConfidentQuote if
		// if Greenscreens returned a low confidence rate. Now, since we can have valid
		// results from other sources, we append a GS Quote object with the respective
		// error under the Metadata attributes.
		if errors.Is(err, quote.ErrNoConfidentQuote) {
			quotes = append(quotes, Quote{
				Source: models.GreenscreensSource,
				Metadata: map[string]any{
					"error": quote.ErrNoConfidentQuote.Error(),
				},
			})
			return quotes, nil
		}

		return nil, fmt.Errorf("failed to get Greenscreens quote: %w", err)
	}

	// Cache mile distance between locations for usage in other features (e.g. GTZ Lane History)
	err = quote.SetRedisMileDistanceBetweenLocations(
		ctx,
		stops[0].Address.City,
		stops[0].Address.State,
		stops[1].Address.City,
		stops[1].Address.State,
		laneRatePrediction.Distance,
	)
	if err != nil {
		log.Warn(ctx, "failed to cache mile distance between locations", zap.Error(err))
	}

	// Create GS Buy Power quote record
	createdGSBuyPower := createGSQuoteRecord(
		ctx,
		*service,
		*user,
		body,
		stops,
		laneRatePrediction,
		models.GSBuyPowerType,
	)

	quotes = append(quotes, Quote{
		ID:     createdGSBuyPower.ID,
		Source: models.GreenscreensSource,
		Type:   models.GSBuyPowerType,
		Rates: RateValues{
			TargetPerMile: laneRatePrediction.TargetBuyRate,
			LowPerMile:    laneRatePrediction.LowBuyRate,
			HighPerMile:   laneRatePrediction.HighBuyRate,
		},
		Distance: laneRatePrediction.Distance,
		Metadata: map[string]any{
			"confidenceLevel": laneRatePrediction.ConfidenceLevel,
		},
	})

	// Create GS Network quote record
	createdGSNetwork := createGSQuoteRecord(
		ctx,
		*service,
		*user,
		body,
		stops,
		networkLaneRatePrediction,
		models.GSNetworkType,
	)
	quotes = append(quotes, Quote{
		ID:     createdGSNetwork.ID,
		Source: models.GreenscreensSource,
		Type:   models.GSNetworkType,
		Rates: RateValues{
			TargetPerMile: networkLaneRatePrediction.TargetBuyRate,
			LowPerMile:    networkLaneRatePrediction.LowBuyRate,
			HighPerMile:   networkLaneRatePrediction.HighBuyRate,
		},
		Distance: networkLaneRatePrediction.Distance,
		Metadata: map[string]any{
			"confidenceLevel": networkLaneRatePrediction.ConfidenceLevel,
		},
	})

	return quotes, nil
}

// handleDATTwoStop handles the logic for getting a DAT quote for a two-stop route.
func handleDATTwoStop(
	ctx context.Context,
	service *models.Service,
	user *models.User,
	integration *models.Integration,
	body *QuickQuotePrivateBody,
	stops []models.Stop,
	transportTypeInput models.TransportType,
	createDBRecord bool,
	handlers QuickQuoteHandlers,
) (*Quote, error) {

	var err error

	ctx, metaSpan := otel.StartSpan(ctx, "handleDATTwoStop", nil)
	defer func() { metaSpan.End(err) }()

	resp, err := getDATQuote(ctx, integration, user, stops[0], stops[1], transportTypeInput, handlers)
	if err != nil {
		return nil, err
	}

	rateData := resp.RateResponses[0].Response
	rateData = calculateDATAllInCost(ctx, rateData, rateData.Rate.AverageFuelSurchargePerTripUsd)

	var createdQuoteID uint
	if createDBRecord {
		createdQuote := createDATQuoteRecord(
			ctx,
			*service,
			*user,
			body,
			stops,
			rateData,
			models.DATSpotType,
			handlers,
		)
		if createdQuote != nil {
			createdQuoteID = createdQuote.ID
		}
	}

	return &Quote{
		ID:     createdQuoteID,
		Source: models.DATSource,
		Type:   models.DATSpotType,
		Rates: RateValues{
			Target:        rateData.Rate.PerTrip.RateUSD,
			Low:           rateData.Rate.PerTrip.LowUSD,
			High:          rateData.Rate.PerTrip.HighUSD,
			TargetPerMile: rateData.Rate.PerMile.RateUSD,
			LowPerMile:    rateData.Rate.PerMile.LowUSD,
			HighPerMile:   rateData.Rate.PerMile.HighUSD,
		},
		Metadata: map[string]any{
			"reports":              rateData.Rate.Reports,
			"companies":            rateData.Rate.Companies,
			"timeframe":            rateData.Escalation.Timeframe,
			"originName":           rateData.Escalation.Origin.Name,
			"originType":           rateData.Escalation.Origin.Type,
			"destinationName":      rateData.Escalation.Destination.Name,
			"destinationType":      rateData.Escalation.Destination.Type,
			"fuelSurchargePerMile": rateData.Rate.AverageFuelSurchargePerMileUsd,
			"fuelSurchargePerTrip": rateData.Rate.AverageFuelSurchargePerTripUsd,
		},
		Distance: rateData.Rate.Mileage,
	}, nil
}

// DAT doesn't natively support multi-stop, so we take a DAT + Drumkit hybrid approach.
// For route with intermediate legs > 75 miles, we sum the DAT quotes for each leg.
// This approach is best for routes with reasonable intermediate leg distances. Otherwise, if the legs are short,
// then each estimate will be an overestimation because of the basic overhead cost of running any load vs.
// the marginal cost of adding stops to a route.
func handleDATMultiStopLegToLeg(
	ctx context.Context,
	service *models.Service,
	user *models.User,
	integration *models.Integration,
	body *QuickQuotePrivateBody,
	stops []models.Stop,
	transportTypeInput models.TransportType,
	handlers QuickQuoteHandlers,
) (*Quote, error) {

	var err error

	ctx, metaSpan := otel.StartSpan(ctx, "handleDATMultiStopLegToLeg", nil)
	defer func() { metaSpan.End(err) }()

	if len(stops) <= 2 {
		return nil, fmt.Errorf("invalid number of stops: %d", len(stops))
	}

	var reports, companies int
	var totalFuelSurcharge float64
	var legs []RouteLeg
	finalQuote := Quote{
		Source: models.DATSource,
		Type:   models.DATMultiStopLegToLeg,
	}

	for i := range len(stops) - 1 {
		startStop := stops[i]
		endStop := stops[i+1]

		// Get DAT quote for this leg
		legQuote, err := handleDATTwoStop(
			ctx,
			service,
			user,
			integration,
			body,
			[]models.Stop{startStop, endStop},
			transportTypeInput,
			false,
			handlers,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to get quote for leg %d: %w", i, err)
		}

		finalQuote.Distance += legQuote.Distance
		finalQuote.Rates = RateValues{
			Target: finalQuote.Rates.Target + legQuote.Rates.Target,
			Low:    finalQuote.Rates.Low + legQuote.Rates.Low,
			High:   finalQuote.Rates.High + legQuote.Rates.High,
		}

		legs = append(legs, RouteLeg{
			Order:          i,
			StartStopIndex: i,
			EndStopIndex:   i + 1,
			StartCityState: startStop.Address.City + ", " + startStop.Address.State,
			EndCityState:   endStop.Address.City + ", " + endStop.Address.State,
			DistanceMiles:  legQuote.Distance,
			Rates:          legQuote.Rates,
		})

		reports += legQuote.Metadata["reports"].(int)
		companies += legQuote.Metadata["companies"].(int)
		totalFuelSurcharge += legQuote.Metadata["fuelSurchargePerTrip"].(float64)
	}

	// Calculate the per-mile values
	finalQuote.Rates.TargetPerMile = finalQuote.Rates.Target / finalQuote.Distance
	finalQuote.Rates.LowPerMile = finalQuote.Rates.Low / finalQuote.Distance
	finalQuote.Rates.HighPerMile = finalQuote.Rates.High / finalQuote.Distance
	avgFuelSurchargePerMile := totalFuelSurcharge / finalQuote.Distance

	finalQuote.Metadata = map[string]any{
		"reports":              reports,
		"companies":            companies,
		"fuelSurchargePerTrip": totalFuelSurcharge,
		"fuelSurchargePerMile": avgFuelSurchargePerMile,
		"legs":                 legs,
	}

	datQuote := dat.RateResponse{
		Rate: dat.Rate{
			Mileage:                        finalQuote.Distance,
			AverageFuelSurchargePerTripUsd: finalQuote.Metadata["fuelSurchargePerTrip"].(float64),
			AverageFuelSurchargePerMileUsd: finalQuote.Metadata["fuelSurchargePerMile"].(float64),
			PerTrip: dat.PriceRange{
				RateUSD: finalQuote.Rates.Target,
				LowUSD:  finalQuote.Rates.Low,
				HighUSD: finalQuote.Rates.High,
			},
			PerMile: dat.PriceRange{
				RateUSD: finalQuote.Rates.TargetPerMile,
				LowUSD:  finalQuote.Rates.LowPerMile,
				HighUSD: finalQuote.Rates.HighPerMile,
			},
		},
	}

	createdQuote := createDATQuoteRecord(
		ctx,
		*service,
		*user,
		body,
		stops,
		datQuote,
		models.DATMultiStopLegToLeg,
		handlers,
	)
	if createdQuote != nil {
		finalQuote.ID = createdQuote.ID
	}

	return &finalQuote, nil
}

// DAT doesn't natively support multi-stop, so we take a DAT + Drumkit hybrid approach.
// For route with short intermediate leg(s), we take DAT spot rate for first to last stop
// then add fee per intermediate stop. This approach is best for routes with short intermediate leg(s)
// to circumvent the fact that there's an overheadcost just to run a lane that would be overestimated by the
// DATMultiStopLegToLeg approach.
// NOTE: This function should be called after `handleDATMultiStopLegToLeg` and pass in the array of
// responses for each leg
func handleDATMultiStopLongestLeg(
	ctx context.Context,
	service *models.Service,
	user *models.User,
	integration *models.Integration,
	body *QuickQuotePrivateBody,
	stops []models.Stop,
	transportTypeInput models.TransportType,
	legToLegTotalDistanceMiles float64,
	handlers QuickQuoteHandlers,
) (*Quote, error) {

	var err error

	ctx, metaSpan := otel.StartSpan(ctx, "handleDATMultiStopLongestLeg", nil)
	defer func() { metaSpan.End(err) }()

	// Get DAT quote for first and last stop
	if len(stops) < 2 {
		return nil, errors.New("at least two stops are required")
	}

	startStop := stops[0]
	endStop := stops[len(stops)-1]

	resp, err := getDATQuote(ctx, integration, user, startStop, endStop, transportTypeInput, handlers)
	if err != nil {
		return nil, fmt.Errorf("failed to get DAT longest leg quote: %w", err)
	}

	rateData := resp.RateResponses[0].Response
	rateData = calculateDATAllInCost(ctx, rateData, rateData.Rate.AverageFuelSurchargePerTripUsd)

	directMileage := rateData.Rate.Mileage
	totalDistance := legToLegTotalDistanceMiles
	if totalDistance <= directMileage {
		totalDistance = directMileage
	}

	// Add fee per intermediate stop based on service config if available. Otherwise, use Drumkit defaults.
	countIntermediateStops := len(stops) - 2
	stopFeeUSD := IntermediateStopFeeUSD

	if user != nil && user.IntermediateStopFeeUSD != nil {
		stopFeeUSD = *user.IntermediateStopFeeUSD
	}

	lowPerMile := rateData.Rate.PerMile.LowUSD
	ratePerMile := rateData.Rate.PerMile.RateUSD
	highPerMile := rateData.Rate.PerMile.HighUSD

	rateData.Rate.PerTrip.LowUSD = lowPerMile*totalDistance + float64(countIntermediateStops*stopFeeUSD)
	rateData.Rate.PerTrip.RateUSD = ratePerMile*totalDistance + float64(countIntermediateStops*stopFeeUSD)
	rateData.Rate.PerTrip.HighUSD = highPerMile*totalDistance + float64(countIntermediateStops*stopFeeUSD)

	rateData.Rate.PerMile.RateUSD = rateData.Rate.PerTrip.RateUSD / totalDistance
	rateData.Rate.PerMile.LowUSD = rateData.Rate.PerTrip.LowUSD / totalDistance
	rateData.Rate.PerMile.HighUSD = rateData.Rate.PerTrip.HighUSD / totalDistance

	rateData.Rate.Mileage = totalDistance
	rateData.Rate.AverageFuelSurchargePerTripUsd = rateData.Rate.AverageFuelSurchargePerMileUsd * totalDistance

	var createdQuoteID uint
	createdQuote := createDATQuoteRecord(
		ctx,
		*service,
		*user,
		body,
		stops,
		rateData,
		models.DATMultiStopLongestLeg,
		handlers,
	)
	if createdQuote != nil {
		createdQuoteID = createdQuote.ID
	}

	return &Quote{
		ID:     createdQuoteID,
		Source: models.DATSource,
		Type:   models.DATMultiStopLongestLeg,
		Rates: RateValues{
			// Total values
			Target: rateData.Rate.PerTrip.RateUSD,
			Low:    rateData.Rate.PerTrip.LowUSD,
			High:   rateData.Rate.PerTrip.HighUSD,
			// Per-mile values
			TargetPerMile: rateData.Rate.PerMile.RateUSD,
			LowPerMile:    rateData.Rate.PerMile.LowUSD,
			HighPerMile:   rateData.Rate.PerMile.HighUSD,
		},
		Metadata: map[string]any{
			"reports":              rateData.Rate.Reports,
			"companies":            rateData.Rate.Companies,
			"timeframe":            rateData.Escalation.Timeframe,
			"originName":           rateData.Escalation.Origin.Name,
			"originType":           rateData.Escalation.Origin.Type,
			"destinationName":      rateData.Escalation.Destination.Name,
			"destinationType":      rateData.Escalation.Destination.Type,
			"fuelSurchargePerMile": rateData.Rate.AverageFuelSurchargePerMileUsd,
			"fuelSurchargePerTrip": rateData.Rate.AverageFuelSurchargePerTripUsd,
			"stopFeeUSD":           stopFeeUSD,
			// TODO: deprecate when users are on latest Vulcan version
			// Only `stopFeeUSDMedium` here is used in older versions for explaining the math. We changed
			// the values here to reflect the new math that only leverages one stop fee instead of three
			// tiers.
			"stopFeeUSDLow":    stopFeeUSD,
			"stopFeeUSDMedium": stopFeeUSD,
			"stopFeeUSDHigh":   stopFeeUSD,
		},
		Distance: totalDistance,
	}, nil
}

// Helper function for getDATQuote with dependency injection
func getDATQuote(
	ctx context.Context,
	integration *models.Integration,
	user *models.User,
	startStop models.Stop,
	endStop models.Stop,
	transportTypeInput models.TransportType,
	handlers QuickQuoteHandlers,
) (*dat.GetLaneRateResponse, error) {

	reqBody := []dat.RateRequest{
		{
			Origin: dat.InputLocation{
				PostalCode:      startStop.Address.Zip,
				City:            startStop.Address.City,
				StateOrProvince: startStop.Address.State,
			},
			Destination: dat.InputLocation{
				PostalCode:      endStop.Address.Zip,
				City:            endStop.Address.City,
				StateOrProvince: endStop.Address.State,
			},
			Equipment: transportTypeInput,
			RateType:  dat.BrokerToCarrierSpotRateType,
		},
	}

	resp, err := handlers.GetDATLaneRate(ctx, *integration, user.DATEmailAddress, &reqBody)
	if err != nil {
		return nil, err
	}

	// Cache mile distance between locations for usage in other features (e.g. GTZ Lane History)
	err = handlers.SetRedisMileDistanceBetweenLocations(
		ctx,
		startStop.Address.City,
		startStop.Address.State,
		endStop.Address.City,
		endStop.Address.State,
		resp.RateResponses[0].Response.Rate.Mileage,
	)
	if err != nil {
		log.Warn(ctx, "failed to cache mile distance between locations", zap.Error(err))
	}

	rateData := resp.RateResponses[0].Response

	// If rate data is all empty, return immediately with an error
	if rateData.Rate == (dat.Rate{}) {
		log.WarnNoSentry(ctx, "DAT rate data is empty")
		return nil, errors.New("DAT rate data is empty")
	}

	var avgFuelPerMile float64
	var avgRatePerMile float64
	var lowRatePerMile float64
	var highRatePerMile float64

	// DAT per-mile values flaky and randomly unavailable for some lanes, so we
	// calculate them ourselves when mileage is available.
	if rateData.Rate.Mileage != 0 {
		avgRatePerMile = rateData.Rate.PerTrip.RateUSD / rateData.Rate.Mileage
		lowRatePerMile = rateData.Rate.PerTrip.LowUSD / rateData.Rate.Mileage
		highRatePerMile = rateData.Rate.PerTrip.HighUSD / rateData.Rate.Mileage
		avgFuelPerTrip := rateData.Rate.AverageFuelSurchargePerTripUsd

		avgFuelPerMile = math.Round((avgFuelPerTrip/rateData.Rate.Mileage)*100) / 100

		rateData.Rate.PerMile.RateUSD = avgRatePerMile
		rateData.Rate.PerMile.LowUSD = lowRatePerMile
		rateData.Rate.PerMile.HighUSD = highRatePerMile
		rateData.Rate.AverageFuelSurchargePerMileUsd = avgFuelPerMile
	}

	resp.RateResponses[0].Response = rateData
	return resp, nil
}

// Helper function for createDATQuoteRecord with dependency injection
func createDATQuoteRecord(
	ctx context.Context,
	service models.Service,
	user models.User,
	body *QuickQuotePrivateBody,
	stops []models.Stop,
	ratePrediction dat.RateResponse,
	typeInSource models.QuoteTypeInSource,
	handlers QuickQuoteHandlers,
) *models.QuickQuote {

	rateData := quote.RateData{
		TargetBuyRate: ratePrediction.Rate.PerTrip.RateUSD,
		LowBuyRate:    ratePrediction.Rate.PerTrip.LowUSD,
		HighBuyRate:   ratePrediction.Rate.PerTrip.HighUSD,
		Distance:      ratePrediction.Rate.Mileage,
	}

	return handlers.CreateQuoteRecord(
		ctx,
		service,
		user.ID,
		body.EmailID,
		body.ThreadID,
		body.QuoteRequestID,
		stops,
		body.TransportType,
		body.PickupDate,
		body.DeliveryDate,
		rateData,
		0,
		0,
		0,
		models.DATSource,
		typeInSource,
		nil,
	)
}

// handleTruckstop handles the logic for getting a Truckstop quote.
func handleTruckstop(
	ctx context.Context,
	service *models.Service,
	user *models.User,
	integration *models.Integration,
	body *QuickQuotePrivateBody,
	stops []models.Stop,
	transportTypeInput models.TransportType,
) ([]Quote, error) {

	var quotes []Quote
	var err error

	ctx, metaSpan := otel.StartSpan(ctx, "handleTruckstop", nil)
	defer func() { metaSpan.End(err) }()

	if stops[0].Address.Country == string(quote.CountryCanada) ||
		stops[1].Address.Country == string(quote.CountryCanada) {

		log.WarnNoSentry(ctx, "TruckStop does not support Canada")
		return nil, nil
	}

	var truckstopStops []models.Stop

	if len(stops) >= 2 {
		log.Warn(ctx, "TruckStop does not rate between each stop, truncating to O/D")
		// For the new format, only extract pickup[0] and dropoff[n-1] stops.
		truckstopStops = []models.Stop{
			stops[0],            // pickup
			stops[len(stops)-1], // delivery
		}
	} else {
		truckstopStops = stops
	}

	bookedEstimate, postedEstimateData, err := quote.FetchTruckStopQuotes(
		ctx,
		*service,
		*integration,
		user.EmailAddress,
		body.LoadID,
		truckstop.LocationDetails{
			StateCode: truckstopStops[0].Address.State,
			City:      truckstopStops[0].Address.City,
			ZipCode:   truckstopStops[0].Address.Zip,
		},
		truckstop.LocationDetails{
			StateCode: truckstopStops[1].Address.State,
			City:      truckstopStops[1].Address.City,
			ZipCode:   truckstopStops[1].Address.Zip,
		},
		transportTypeInput,
		body.PickupDate,
		body.DeliveryDate,
		len(stops)-2, // numStopsInBetween
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get Truckstop quote: %w", err)
	}

	// Cache mile distance between locations for usage in other features (e.g. GTZ Lane History)
	var estimatedMileDistance float64
	if bookedEstimate.RatePerMile > 0 {
		estimatedMileDistance = bookedEstimate.PredictedRate / bookedEstimate.RatePerMile
	} else {
		// Fallback: set distance to 1 to avoid division by 0
		estimatedMileDistance = 1
	}

	err = quote.SetRedisMileDistanceBetweenLocations(
		ctx,
		stops[0].Address.City,
		stops[0].Address.State,
		stops[1].Address.City,
		stops[1].Address.State,
		estimatedMileDistance,
	)
	if err != nil {
		log.Warn(ctx, "failed to cache mile distance between locations", zap.Error(err))
	}

	createdBookRate, createdPostedRate := createTruckstopQuoteRecord(
		ctx,
		*service,
		*user,
		body,
		stops,
		bookedEstimate,
		postedEstimateData,
		estimatedMileDistance,
	)

	quotes = []Quote{
		{
			ID:     createdBookRate.ID,
			Source: models.TruckStopSource,
			Type:   models.TruckStopBookedType,
			Rates: RateValues{
				Target:        bookedEstimate.PredictedRate,
				Low:           bookedEstimate.LowerRate,
				High:          bookedEstimate.UpperRate,
				TargetPerMile: bookedEstimate.RatePerMile,
			},
			Distance: estimatedMileDistance,
			Metadata: map[string]any{
				"confidenceLevel": bookedEstimate.Average,
			},
		},
		{
			ID:     createdPostedRate.ID,
			Source: models.TruckStopSource,
			Type:   models.TruckStopPostedType,
			Rates: RateValues{
				Target:        postedEstimateData.PredictedRate,
				Low:           postedEstimateData.LowerRate,
				High:          postedEstimateData.UpperRate,
				TargetPerMile: postedEstimateData.RatePerMile,
			},
			Distance: estimatedMileDistance,
			Metadata: map[string]any{
				"confidenceLevel": postedEstimateData.Average,
			},
		},
	}

	return quotes, nil
}

func createGSQuoteRecord(
	ctx context.Context,
	service models.Service,
	user models.User,
	body *QuickQuotePrivateBody,
	stops []models.Stop,
	ratePrediction *greenscreens.RatePredictionDetail,
	typeInSource models.QuoteTypeInSource,
) *models.QuickQuote {

	rateTargetCost := ratePrediction.TargetBuyRate * ratePrediction.Distance
	rateData := quote.RateData{
		ExternalID:      ratePrediction.ID,
		TargetBuyRate:   ratePrediction.TargetBuyRate,
		LowBuyRate:      ratePrediction.LowBuyRate,
		HighBuyRate:     ratePrediction.HighBuyRate,
		StartBuyRate:    ratePrediction.StartBuyRate,
		FuelRate:        ratePrediction.FuelRate,
		Distance:        ratePrediction.Distance,
		ConfidenceLevel: float64(ratePrediction.ConfidenceLevel),
	}

	return quote.CreateQuoteRecord(
		ctx,
		service,
		user.ID,
		body.EmailID,
		body.ThreadID,
		body.QuoteRequestID,
		stops,
		body.TransportType,
		body.PickupDate,
		body.DeliveryDate,
		rateData,
		rateTargetCost,
		1.07*rateTargetCost,
		1.10*rateTargetCost,
		models.GreenscreensSource,
		typeInSource,
		nil,
	)
}

func createTruckstopQuoteRecord(
	ctx context.Context,
	service models.Service,
	user models.User,
	body *QuickQuotePrivateBody,
	stops []models.Stop,
	bookedEstimate *truckstop.BookedRateEstimateResp,
	postedEstimate *truckstop.PostedRateResp,
	distance float64,
) (*models.QuickQuote, *models.QuickQuote) {

	// Create Booked Rate record
	bookedRateData := quote.RateData{
		ExternalID:      bookedEstimate.LoadID,
		TargetBuyRate:   bookedEstimate.Average,
		LowBuyRate:      bookedEstimate.LowerRate,
		HighBuyRate:     bookedEstimate.UpperRate,
		StartBuyRate:    bookedEstimate.Average,
		FuelRate:        0, // Truckstop rates are all-inclusive
		Distance:        distance,
		ConfidenceLevel: bookedEstimate.DataRecordsScore,
	}

	// Calculate target sell cost for booked rate
	bookedTargetSellCost := bookedEstimate.Average * distance

	createdBookRate := quote.CreateQuoteRecord(
		ctx,
		service,
		user.ID,
		body.EmailID,
		body.ThreadID,
		body.QuoteRequestID,
		stops,
		body.TransportType,
		body.PickupDate,
		body.DeliveryDate,
		bookedRateData,
		bookedTargetSellCost,
		1.07*bookedTargetSellCost, // 7% markup
		1.10*bookedTargetSellCost, // 10% markup
		models.TruckStopSource,
		models.TruckStopBookedType,
		nil,
	)

	// Create Posted Rate record
	postedEstimateData := quote.RateData{
		ExternalID:      postedEstimate.LoadID,
		TargetBuyRate:   postedEstimate.Average,
		LowBuyRate:      postedEstimate.LowerRate,
		HighBuyRate:     postedEstimate.UpperRate,
		StartBuyRate:    postedEstimate.Average,
		FuelRate:        0, // Truckstop rates are all-inclusive
		Distance:        distance,
		ConfidenceLevel: postedEstimate.DataRecordsScore,
	}

	// Calculate target sell cost for posted rate
	postedTargetSellCost := postedEstimate.Average * distance

	createdPostedRate := quote.CreateQuoteRecord(
		ctx,
		service,
		user.ID,
		body.EmailID,
		body.ThreadID,
		body.QuoteRequestID,
		stops,
		body.TransportType,
		body.PickupDate,
		body.DeliveryDate,
		postedEstimateData,
		postedTargetSellCost,
		1.07*postedTargetSellCost, // 7% markup
		1.10*postedTargetSellCost, // 10% markup
		models.TruckStopSource,
		models.TruckStopPostedType,
		nil,
	)

	return createdBookRate, createdPostedRate
}

func showLowConfidenceWarning(lowConfidenceThreshold int, quotes []Quote) bool {
	if lowConfidenceThreshold != 0 {
		gsQuoteCount := 0
		gsLowThresholdCount := 0
		for _, quote := range quotes {
			if quote.Source == models.GreenscreensSource &&
				(quote.Type == models.GSBuyPowerType || quote.Type == models.GSNetworkType) {
				gsQuoteCount++

				conf, ok := quote.Metadata["confidenceLevel"].(int)
				if ok && conf < lowConfidenceThreshold {
					gsLowThresholdCount++
				}
			}
		}

		if gsQuoteCount > 0 && gsLowThresholdCount == gsQuoteCount {
			return true
		}
	}

	return false
}

// DAT quotes are linehaul only, fuel is separate so this function adds the fuel to the rate
func calculateDATAllInCost(
	ctx context.Context,
	datResp dat.RateResponse,
	avgFuelTotalPerTrip float64,
) dat.RateResponse {

	if avgFuelTotalPerTrip == 0 {
		log.Warn(ctx, "avgFuelPerTrip is 0, skipping DAT all-in cost calculation", zap.Any("datResp", datResp))
		return datResp
	}

	datResp.Rate.PerTrip.RateUSD += avgFuelTotalPerTrip
	datResp.Rate.PerTrip.LowUSD += avgFuelTotalPerTrip
	datResp.Rate.PerTrip.HighUSD += avgFuelTotalPerTrip

	avgFuelPerMile := datResp.Rate.AverageFuelSurchargePerTripUsd / datResp.Rate.Mileage
	datResp.Rate.PerMile.RateUSD += avgFuelPerMile
	datResp.Rate.PerMile.LowUSD += avgFuelPerMile
	datResp.Rate.PerMile.HighUSD += avgFuelPerMile

	datResp.Rate.AverageFuelSurchargePerMileUsd = avgFuelPerMile

	return datResp
}

func getOriginDestinationStrFromStops(stops []Stop) (string, string) {
	if len(stops) == 0 {
		return "", ""
	}

	origin := stops[0].Zip
	dest := stops[len(stops)-1].Zip

	if origin == "" {
		origin = fmt.Sprintf("%s, %s", stops[0].City, stops[0].State)
	}

	if dest == "" {
		dest = fmt.Sprintf("%s, %s", stops[len(stops)-1].City, stops[len(stops)-1].State)
	}

	return origin, dest
}

func getQuoteReplyTemplate(ctx context.Context, user models.User) models.EmailTemplate {
	ctx, metaSpan := otel.StartSpan(ctx, "getQuoteReplyTemplate", nil)
	defer func() { metaSpan.End(nil) }()

	userGroupIDs, err := userGroupsDB.GetAllGroupIDsByUserID(ctx, user.ID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		// Fail open, which just skips checking for user group templates
		log.Error(ctx, "error while getting user group", zap.Error(err))
	}

	templates, err := emailTemplateDB.GetEmailTemplateByTypeAndIDs(
		ctx,
		user.ID,
		userGroupIDs,
		user.ServiceID,
		[]models.EmailTemplateType{models.QuickQuoteReply},
	)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		// Fail open, will use generic template
		log.Error(ctx, "failed to get email templates", zap.Error(err))
	}

	getTemplateOrGeneric := func(templateType models.EmailTemplateType) models.EmailTemplate {
		for _, t := range templates {
			if t.TemplateType == templateType {
				return t
			}
		}

		genericTemplate := models.GenericEmailTemplates[templateType]
		return genericTemplate
	}

	return getTemplateOrGeneric(models.QuickQuoteReply)
}

// createValidationError creates a structured validation error from a list of stop validation errors
func createValidationError(details []StopValidationError) *ValidationError {
	if len(details) == 0 {
		return nil
	}

	return &ValidationError{
		Type:    "validation_error",
		Message: "One or more locations are invalid",
		Details: details,
	}
}

// validateStop validates a single stop and returns a validation error if invalid
func validateStop(ctx context.Context, stop *models.Stop, stopIndex int) *StopValidationError {
	countryName := quote.CountryName(stop.Address.Country)
	err := quote.ValidateLocation(
		ctx,
		&stop.Address.City,
		&stop.Address.State,
		&stop.Address.Zip,
		&countryName,
	)

	if err != nil {
		// Create location string for the value field
		locationStr := stop.Address.City
		if stop.Address.State != "" {
			if locationStr != "" {
				locationStr += ", "
			}
			locationStr += stop.Address.State
		}

		if stop.Address.Zip != "" {
			if locationStr != "" {
				locationStr += " "
			}
			locationStr += stop.Address.Zip
		}

		return &StopValidationError{
			StopIndex: stopIndex,
			Field:     "location",
			Message:   err.Error(),
			Value:     locationStr,
		}
	}

	stop.Address.Country = string(countryName)

	return nil
}

func getDATQuoteErrorMsg(
	ctx context.Context,
	err error,
	user models.User,
	integration models.Integration,
) string {
	lowerOriginalError := strings.ToLower(err.Error())

	// Example: "failed to get user access token: ... invalid credentials supplied"
	isConnexionSeatError := strings.Contains(lowerOriginalError, "invalid credentials supplied") &&
		strings.Contains(lowerOriginalError, "failed to get user access token")

	// Example: "failed to get org access token: ... invalid credentials supplied"
	isOrgTokenError := strings.Contains(lowerOriginalError, "invalid credentials supplied") &&
		strings.Contains(lowerOriginalError, "failed to get org access token")

	isEmptyRateDataError := strings.Contains(lowerOriginalError, "rate data is empty")

	if isEmptyRateDataError {
		return "RateView does not have data for this lane"
	}

	if isConnexionSeatError {
		err := rds.RemoveUserDATCredentials(ctx, &user)
		if err != nil {
			log.Error(ctx, "failed to erase invalid DAT credentials for user", zap.Error(err))
		}

		return fmt.Sprintf(
			"%s needs to have a Connexion seat in order to use RateView data",
			user.DATEmailAddress,
		)
	}

	if isOrgTokenError {
		return fmt.Sprintf(
			"Failed to authenticate DAT Organization with username %s",
			integration.Username,
		)
	}

	return "Unknown error while getting DAT quote"
}

// Greenscreens errors refer to GS as Triumph since they're undergoing a rebranding
func getGreenscreensQuoteErrorMsg(err error, integration models.Integration) string {
	lowerOriginalError := strings.ToLower(err.Error())

	// Example: "Zip not found for Clinchfield,AL - 'stops.0'"
	isInvalidZipError := strings.Contains(lowerOriginalError, "zip not found for")
	// Example: "Invalid client or Invalid client credentials"
	isAccessForbiddenError := strings.Contains(lowerOriginalError, "invalid client credentials")

	if isInvalidZipError {
		re := regexp.MustCompile(`Zip not found for ([^-\s]+(?:,[^-\s]+)*)`)
		matches := re.FindStringSubmatch(err.Error())

		if len(matches) > 1 {
			// Extract just the location name (e.g., "Clinchfield,AL")
			location := matches[1]
			return fmt.Sprintf("Triumph failed to find a zipcode for %s", location)
		}

		return "Triumph failed when validating zipcodes for inputs"
	}

	if isAccessForbiddenError {
		return fmt.Sprintf("Triumph access is forbidden for %s", integration.Username)
	}

	return "Unknown error while getting Triumph quote"
}
