package load

import (
	"context"
	"errors"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
)

type (
	GetLoadBySearchQuery struct {
		// Customer data
		CustomerName string `json:"customerName"`

		// Route data
		PickupCity   string `json:"pickupCity"`
		PickupState  string `json:"pickupState"`
		DropoffCity  string `json:"dropoffCity"`
		DropoffState string `json:"dropoffState"`

		// Appointment dates
		PickupDate  string `json:"pickupDate"`
		DropoffDate string `json:"dropoffDate"`
	}

	GetLoadBySearchResponse struct {
		LoadIDs []string `json:"loadIDs"`
	}
)

// TODO: Enable Mcleod ref number lookups
func GetLoadBySearch(c *fiber.Ctx) error {
	var query GetLoadBySearchQuery
	if err := api.Parse(c, nil, &query, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Any("requestQuery", query))
	userServiceID := middleware.ServiceIDFromContext(c)

	queryMap := query.BuildQueryMap()
	scopeMap := query.BuildScopeMap(ctx)

	var loadsQuery []models.Load
	var dbErr error

	loadsQuery, dbErr = loadDB.GetLoadBySearchQuery(ctx, queryMap, scopeMap, userServiceID)
	if dbErr != nil {
		if errors.Is(dbErr, gorm.ErrRecordNotFound) {
			return c.SendStatus(http.StatusNotFound)
		}
		return c.SendStatus(http.StatusInternalServerError)
	}

	foundLoadsIDs := []string{}
	for _, load := range loadsQuery {
		foundLoadsIDs = append(foundLoadsIDs, load.FreightTrackingID)
	}

	result := GetLoadBySearchResponse{
		LoadIDs: foundLoadsIDs,
	}

	return c.Status(http.StatusOK).JSON(result)
}

func (q *GetLoadBySearchQuery) BuildScopeMap(ctx context.Context) map[string]string {
	dateParseTemplete := "2006-01-02T15:04:05.000Z"
	dateFormatTemplete := "2006-01-02"

	scopeMap := make(map[string]string)

	if q.CustomerName != "" {
		scopeMap["customer_name"] = q.CustomerName
	}
	// States handled in query builder
	if q.PickupCity != "" {
		scopeMap["pickup_city"] = q.PickupCity
	}
	if q.DropoffCity != "" {
		scopeMap["dropoff_city"] = q.DropoffCity
	}

	if q.PickupDate != "" {
		t, err := time.Parse(dateParseTemplete, q.PickupDate)
		if err != nil {
			log.ErrorNoSentry(ctx, "error parsing date from advanced load search", zap.Error(err))
		} else {
			scopeMap["pickup_appt"] = t.Format(dateFormatTemplete)
		}
	}
	if q.DropoffDate != "" {
		t, err := time.Parse(dateParseTemplete, q.DropoffDate)
		if err != nil {
			log.ErrorNoSentry(ctx, "error parsing date from advanced load search", zap.Error(err))
		} else {
			scopeMap["dropoff_appt"] = t.Format(dateFormatTemplete)
		}
	}

	return scopeMap
}

// State is 2-letter state abbreviations, e.g. "CA" for California so use direct string comparison
func (q *GetLoadBySearchQuery) BuildQueryMap() map[string]string {
	queryMap := make(map[string]string, 2)

	if q.PickupState != "" {
		queryMap["pickup_state"] = q.PickupState
	}

	if q.DropoffState != "" {
		queryMap["consignee_state"] = q.DropoffState
	}

	return queryMap
}
