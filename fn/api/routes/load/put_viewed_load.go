package load

import (
	"errors"
	"fmt"
	"net/http"
	"slices"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
)

type (
	PutViewedLoadPath struct {
		LoadID string `params:"loadID" validate:"required"`
	}

	PutViewedLoadResponse struct {
		ViewedLoadsHistory []string `json:"viewedLoadsHistory"`
	}
)

func PutViewedLoad(c *fiber.Ctx) error {
	var path PutStarredLoadPath
	err := api.Parse(c, &path, nil, nil)
	if err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()
	claims := middleware.ClaimsFromContext(c)

	user, err := userDB.GetByEmail(ctx, claims.Email)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(http.StatusNotFound).SendString(
				fmt.Sprintf("email %s not found", claims.Email))
		}

		log.Error(ctx, "user query error", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	// if load has already been viewed and is on the array, just move it to the end (asc most recent).
	if slices.Contains(user.ViewedLoads, path.LoadID) {
		indexValue := slices.Index(user.ViewedLoads, path.LoadID)
		shiftValueToEnd(user.ViewedLoads, indexValue)
	} else {
		user.ViewedLoads = append(user.ViewedLoads, path.LoadID)
	}

	if err := userDB.Update(ctx, user); err != nil {
		return c.SendStatus(http.StatusInternalServerError)
	}

	var viewedLoads []string
	if len(user.ViewedLoads) > 5 {
		viewedLoads = user.ViewedLoads[len(user.ViewedLoads)-5:]
	} else {
		viewedLoads = user.ViewedLoads
	}

	return c.Status(http.StatusOK).JSON(PutViewedLoadResponse{ViewedLoadsHistory: viewedLoads})
}

func shiftValueToEnd(slice []string, indexValue int) []string {
	if indexValue < 0 || indexValue >= len(slice)-1 {
		return slice
	}

	tmp := slice[indexValue]

	slice = append(slice[:indexValue], slice[indexValue+1:]...)
	slice = append(slice, tmp)

	return slice
}
