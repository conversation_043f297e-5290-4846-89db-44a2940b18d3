package load

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/hardfinhq/go-date"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/errtypes"
	braintrustutil "github.com/drumkitai/drumkit/common/helpers/braintrust"
	"github.com/drumkitai/drumkit/common/integrations/tms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	emailDB "github.com/drumkitai/drumkit/common/rds/email"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
	quoteRequestDB "github.com/drumkitai/drumkit/common/rds/quote_request"
	suggestionsDB "github.com/drumkitai/drumkit/common/rds/suggestedloadchanges"
	tmsUserDB "github.com/drumkitai/drumkit/common/rds/tmsuser"
	"github.com/drumkitai/drumkit/common/sentry"
)

type (
	// LoadWithoutGormModel contains all fields from models.Load except gorm.Model
	loadWithoutGormModel struct {
		models.LoadCoreInfo
		ServiceID          uint               `json:"serviceID"`
		TMSID              uint               `json:"tmsID"`
		ExternalTMSID      string             `json:"externalTMSID"`
		FreightTrackingID  string             `json:"freightTrackingID"`
		IsPlaceholder      bool               `json:"isPlaceholder"`
		Commodities        []models.Commodity `json:"commodities"`
		DeclaredValueUSD   float32            `json:"declaredValueUSD"`
		PickupWarehouseID  uint               `json:"pickupWarehouseID"`
		DropoffWarehouseID uint               `json:"dropoffWarehouseID"`
	}

	CreateLoadAppointmentMetadata struct {
		IsPickupDateOnly  bool `json:"isPickupDateOnly"`
		IsDropoffDateOnly bool `json:"isDropoffDateOnly"`
	}

	CreateLoadMetadata struct {
		CreateLoadAppointmentMetadata
	}

	CreateLoadBody struct {
		// TODO: Update FE to pass email threadID in case user is building load without suggestion,
		// 		 so newly built load is still associated with an email thread
		SuggestionID uint
		Load         loadWithoutGormModel
		Metadata     CreateLoadMetadata
	}

	CreatedLoadResponse struct {
		Load    models.Load `json:"load"`
		Message string      `json:"message,omitempty"`
	}

	GetLoadParams struct {
		ExternalTMSID string `params:"externalTMSID" validate:"required"`
	}

	GetLoadQuery struct {
		TMSID uint `query:"tmsID" validate:"required"`
	}
)

func CreateLoad(c *fiber.Ctx) error {
	var body CreateLoadBody
	if err := api.Parse(c, nil, nil, &body); err != nil {
		log.Error(
			c.UserContext(),
			"error parsing create load body. On-Call: Load Building Failed For User",
			zap.Error(err),
		)
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()
	ctx = log.With(ctx, zap.Any("requestBody", body))
	userServiceID := middleware.ServiceIDFromContext(c)
	userID := middleware.UserIDFromContext(c)

	// Validate pickup and dropoff date logic
	if body.Metadata.IsPickupDateOnly || body.Metadata.IsDropoffDateOnly {
		body.Load = replaceLoadApptTimesWithDateOnly(body.Metadata, body.Load)
	}

	tmsIntegration, err := integrationDB.Get(ctx, body.Load.TMSID)
	if err != nil {
		log.Error(ctx, "error fetching TMS integration", zap.Error(err))
		return c.SendStatus(http.StatusUnprocessableEntity)
	}

	client, err := tms.New(ctx, tmsIntegration)
	if err != nil {
		log.Error(ctx, "error creating TMS client", zap.Error(err))
		return c.SendStatus(http.StatusServiceUnavailable)
	}

	// Convert LoadWithoutGormModel to models.Load once body is validated and parsed
	loadModel := models.Load{
		LoadCoreInfo:       body.Load.LoadCoreInfo,
		ServiceID:          body.Load.ServiceID,
		TMSID:              body.Load.TMSID,
		ExternalTMSID:      body.Load.ExternalTMSID,
		FreightTrackingID:  body.Load.FreightTrackingID,
		IsPlaceholder:      body.Load.IsPlaceholder,
		Commodities:        body.Load.Commodities,
		DeclaredValueUSD:   body.Load.DeclaredValueUSD,
		PickupWarehouseID:  body.Load.PickupWarehouseID,
		DropoffWarehouseID: body.Load.DropoffWarehouseID,
	}

	tmsUser, err := tmsUserDB.GetByDrumkitUserID(ctx, userID, tmsIntegration.ID)
	if err != nil {
		log.ErrorNoSentry(ctx, "error fetching TMS user", zap.Error(err))
	}

	newLoad, err := client.CreateLoad(ctx, loadModel, tmsUser)
	if err != nil {
		var ufErr errtypes.UserFacingError
		isUfErr := errors.As(err, &ufErr)

		// TODO: Filter out user-facing errors from Sentry after observation period
		log.Error(ctx, "error creating load in TMS", zap.Error(err))

		if isUfErr {
			return c.Status(http.StatusServiceUnavailable).
				JSON(CreatedLoadResponse{
					Message: ufErr.Error(),
				})
		}

		return c.SendStatus(http.StatusServiceUnavailable)
	}
	newLoad.ServiceID = userServiceID

	if err = loadDB.Create(ctx, &newLoad); err != nil {
		log.WarnNoSentry(ctx, "error saving new load in DB", zap.Error(err))
		response := CreatedLoadResponse{
			Load:    newLoad,
			Message: "Load created successfully",
		}

		return c.Status(http.StatusOK).JSON(response)
	}

	// Start async bookkeeping operations
	// At this point, we have a new load in the DB.
	// We need to update the load suggestion to accepted and associate the created load with a quote request.
	go sentry.WithHub(ctx, func(ctx context.Context) {
		asyncCtx := log.InheritContext(ctx, context.Background())
		asyncCtx, cancel := context.WithTimeout(asyncCtx, 10*time.Minute) // Plenty of time
		defer cancel()

		var suggestion *models.SuggestedLoadChange
		var emailIDToAssociate uint

		if body.SuggestionID > 0 {
			var err error
			suggestion, err = UpdateLoadSuggestion(asyncCtx, body.SuggestionID, newLoad)
			if err != nil {
				log.WarnNoSentry(asyncCtx, "error updating load suggestion", zap.Error(err))
			} else if suggestion != nil && suggestion.EmailID != nil {
				emailIDToAssociate = *suggestion.EmailID
			}
		} else {
			// Create a SuggestedLoadChange record for loads built without AI suggestions
			var err error
			suggestion, err = CreateLoadChangeWithoutSuggestion(asyncCtx, newLoad, userServiceID)
			if err != nil {
				log.WarnNoSentry(asyncCtx, "error creating load change without suggestion", zap.Error(err))
			}
		}

		err := quoteRequestDB.AssociateLoadWithQuoteRequest(asyncCtx, suggestion, newLoad)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				log.Info(asyncCtx, "no quote requests found to associate load with")
			} else {
				log.WarnNoSentry(asyncCtx, "error associating load with quote request", zap.Error(err))
			}
		}

		if emailIDToAssociate > 0 && newLoad.ID > 0 {
			if err := emailDB.AssociateLoadWithEmail(asyncCtx, emailIDToAssociate, newLoad.ID); err != nil {
				log.WarnNoSentry(
					asyncCtx,
					"error associating load with email",
					zap.Error(err),
					zap.Uint("emailID", emailIDToAssociate),
					zap.Uint("loadID", newLoad.ID),
				)
			}
		}
	})

	response := CreatedLoadResponse{
		Load:    newLoad,
		Message: "Load created successfully",
	}

	return c.Status(http.StatusOK).JSON(response)
}

func UpdateLoadSuggestion(
	ctx context.Context,
	suggestionID uint,
	newLoad models.Load,
) (*models.SuggestedLoadChange, error) {

	suggestion, err := suggestionsDB.GetSuggestionByID(ctx, suggestionID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("suggestion ID %d not found", suggestionID)
		}

		log.WarnNoSentry(ctx, "get suggestions endpoint - suggestion query error", zap.Error(err))
		return nil, fmt.Errorf("get suggestions endpoint - suggestion query error: %w", err)
	}

	braintrustutil.UpdateAppliedSuggestionWithLoad(newLoad, suggestion)

	suggestion.Status = models.Accepted

	// For load building suggestions, submit feedback to Braintrust
	if suggestion.Pipeline == models.LoadBuildingPipeline {
		err = braintrustutil.SubmitBraintrustLoadBuildingFeedback(ctx, *suggestion)
		if err != nil {
			log.ErrorNoSentry(ctx, "error submitting feedback to Braintrust", zap.Error(err))
		}
	}

	if err = suggestionsDB.UpdateSuggestion(ctx, suggestion); err != nil {
		log.WarnNoSentry(ctx, "error updating suggestion", zap.Error(err))
	}

	return suggestion, nil
}

// Creates a SuggestedLoadChange record for loads that were built without AI suggestions.
func CreateLoadChangeWithoutSuggestion(
	ctx context.Context,
	load models.Load,
	serviceID uint,
) (*models.SuggestedLoadChange, error) {
	suggestion := &models.SuggestedLoadChange{
		Account:           "",
		ServiceID:         serviceID,
		FreightTrackingID: load.FreightTrackingID,
		Suggested:         models.SuggestedChanges{},
		Applied:           models.SuggestedChanges{},
		Status:            models.Accepted,
		Pipeline:          models.LoadBuildingPipeline,
		Category:          models.LoadBuilding,
		LoadID:            &load.ID,
		Load:              load,
		EmailID:           nil,
		ThreadID:          "",
	}

	// Populate the Applied field with the load data
	braintrustutil.UpdateAppliedSuggestionWithLoad(load, suggestion)
	// Set Pipeline on Applied SuggestedChanges
	suggestion.Applied.Pipeline = models.LoadBuildingPipeline

	// When EmailID is nil, use Omit to exclude it from the INSERT
	if suggestion.EmailID == nil {
		err := rds.WithContext(ctx).
			Omit("EmailID", "Email").
			Clauses(clause.Returning{}).
			Create(suggestion).Error
		if err != nil {
			return nil, fmt.Errorf("error creating load change without suggestion: %w", err)
		}
		return suggestion, nil
	}

	// When EmailID is not nil, use the normal create path
	if err := suggestionsDB.Create(ctx, suggestion); err != nil {
		return nil, fmt.Errorf("error creating load change without suggestion: %w", err)
	}

	return suggestion, nil
}

func GetLoad(c *fiber.Ctx) error {
	var params GetLoadParams
	var query GetLoadQuery
	if err := api.Parse(c, &params, &query, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()
	userServiceID := middleware.ServiceIDFromContext(c)

	tmsIntegration, err := integrationDB.Get(ctx, query.TMSID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Warn(ctx, "trying to fetch customers for a service with no active TMS",
				zap.Uint("serviceID", userServiceID))

			return c.SendStatus(http.StatusNotFound)
		}

		log.Error(ctx, "error fetching integrations from DB", zap.Error(err))
		return err
	}

	if tmsIntegration.ServiceID != userServiceID {
		log.Infof(
			ctx,
			"user serviceID %d does not match TMS service ID %d",
			userServiceID,
			tmsIntegration.ServiceID,
		)

		return c.SendStatus(http.StatusUnauthorized)
	}

	client, err := tms.New(ctx, tmsIntegration)
	if err != nil {
		log.Error(ctx, "error creating TMS client", zap.Error(err))
		return c.SendStatus(http.StatusServiceUnavailable)
	}

	load, _, err := client.GetLoad(ctx, params.ExternalTMSID)
	if err != nil {
		log.Error(ctx, "error fetching load from TMS", zap.Error(err))
		return c.SendStatus(http.StatusServiceUnavailable)
	}

	err = loadDB.UpsertLoad(ctx, &load, nil)
	if err != nil {
		log.WarnNoSentry(
			ctx,
			"error upserting load in DB",
			zap.Error(err),
			zap.String("loadID", load.ExternalTMSID),
		)
	} else {
		log.Info(ctx, "load upserted in DB", zap.String("loadID", load.ExternalTMSID))
	}

	return c.Status(http.StatusOK).JSON(load)
}

func replaceLoadApptTimesWithDateOnly(metadata CreateLoadMetadata, load loadWithoutGormModel) loadWithoutGormModel {
	updatedLoad := load

	if metadata.IsPickupDateOnly && load.Pickup.ReadyTime.Valid {
		timestamp := load.Pickup.ReadyTime.Time
		updatedLoad.PickupDate = date.Date{
			Year:  timestamp.Year(),
			Month: timestamp.Month(),
			Day:   timestamp.Day(),
		}
		updatedLoad.Pickup.ReadyTime = models.NullTime{}
	}

	if metadata.IsDropoffDateOnly && load.Consignee.MustDeliver.Valid {
		timestamp := load.Consignee.MustDeliver.Time
		updatedLoad.DropoffDate = date.Date{
			Year:  timestamp.Year(),
			Month: timestamp.Month(),
			Day:   timestamp.Day(),
		}
		updatedLoad.Consignee.MustDeliver = models.NullTime{}
	}

	return updatedLoad
}
