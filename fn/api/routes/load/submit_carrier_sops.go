package load

import (
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/lib/pq"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers/timezone"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	genEmailDB "github.com/drumkitai/drumkit/common/rds/generatedemails"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	apiutil "github.com/drumkitai/drumkit/fn/api/util"
)

const preNotificationBuffer = -30

type (
	CarrierSOPPath struct {
		LoadID uint `params:"loadID" validate:"required"`
	}

	MilestoneDetails struct {
		CheckIn    bool      `json:"checkIn"`
		SendTime   time.Time `json:"sendTime" validate:"required"`
		Duration   int       `json:"duration"`
		TimeUnit   string    `json:"timeUnit"`
		Recipient  string    `json:"recipient" validate:"required_if=CheckIn true"`
		CC         string    `json:"cc"`
		Subject    string    `json:"subject" validate:"required_if=CheckIn true"`
		EmailDraft string    `json:"emailDraft" validate:"required_if=CheckIn true"`
	}

	InTransitDetails struct {
		CheckIn    bool   `json:"checkIn"`
		Frequency  int    `json:"frequency"`
		TimeUnit   string `json:"timeUnit"`
		Recipient  string `json:"recipient" validate:"required_if=CheckIn true"`
		CC         string `json:"cc"`
		Subject    string `json:"subject" validate:"required_if=CheckIn true"`
		EmailDraft string `json:"emailDraft" validate:"required_if=CheckIn true"`
	}

	CarrierSOPBody struct {
		FreightTrackingID string           `json:"freightTrackingID"`
		Dispatch          MilestoneDetails `json:"dispatch"`
		Pickup            MilestoneDetails `json:"pickup"`
		AfterPickup       MilestoneDetails `json:"afterPickup"`
		InTransit         InTransitDetails `json:"inTransit"`
		Dropoff           MilestoneDetails `json:"dropoff"`
		AfterDropoff      MilestoneDetails `json:"afterDropoff"`
	}
)

func SubmitCarrierSOPs(c *fiber.Ctx) error {
	var path CarrierSOPPath
	var body CarrierSOPBody
	if err := api.Parse(c, &path, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()
	userServiceID := middleware.ServiceIDFromContext(c)

	user, err := userDB.GetByEmail(ctx, middleware.ClaimsFromContext(c).Email)
	if err != nil {
		return apiutil.HandleDBError(c, err, false, "user %s not found", middleware.ClaimsFromContext(c).Email)
	}

	ctx = log.With(ctx, zap.String("freightTrackingID", body.FreightTrackingID))

	service, err := rds.GetServiceByID(ctx, userServiceID)
	if err != nil {
		log.Error(ctx, "could not get service", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	load, err := loadDB.GetLoadByID(ctx, path.LoadID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(http.StatusNotFound).SendString(
				fmt.Sprintf("load %d not found", path.LoadID),
			)
		}

		log.Error(ctx, "submit carrier sops loadDB failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	var generatedEmails []*models.GeneratedEmail
	if user.EmailSignature == "" {
		log.Warn(ctx, "user without signature trying to generate carrier emails")
	}

	var pickupApptTime, dropoffApptTime time.Time

	if body.Pickup.SendTime.Equal(load.Pickup.ApptStartTime.Time) {
		pickupApptTime = load.Pickup.ApptStartTime.Time
	} else {
		pickupApptTime = body.Pickup.SendTime
	}

	if body.Dropoff.SendTime.Equal(load.Consignee.ApptStartTime.Time) {
		dropoffApptTime = load.Consignee.ApptStartTime.Time
	} else {
		pickupApptTime = body.Dropoff.SendTime
	}

	// If this is an Aljex load, convert the timezone-agnostic pickup or dropoff timestamp to the warehouse's locale.
	//
	// Example: load.Pickup.ApptStartTime is originally 2024-07-01T10:00:00Z and the location is Boston.
	//
	// Conversion means that pickupApptTime is actually 2024-07-01T6:00:00+04:00 (EDT).
	if load.TMS.Name == models.Aljex {
		pickupApptTime, err = timezone.DenormalizeUTC(pickupApptTime, load.Pickup.Timezone)
		if err != nil {
			log.Error(
				ctx,
				"error converting aljex pickup appt time to its timezone",
				zap.Error(err),
				zap.String("timezone", load.Pickup.Timezone),
			)

			return c.SendStatus(http.StatusInternalServerError)
		}
	}

	if load.TMS.Name == models.Aljex {
		dropoffApptTime, err = timezone.DenormalizeUTC(dropoffApptTime, load.Consignee.Timezone)
		if err != nil {
			log.Error(
				ctx,
				"error converting aljex dropoff appt time to its timezone",
				zap.Error(err),
				zap.String("timezone", load.Consignee.Timezone),
			)

			return c.SendStatus(http.StatusInternalServerError)
		}
	}

	if body.Dispatch.CheckIn {
		timeUnit := getTimeUnit(body.Dispatch.TimeUnit)
		delay := getDelay(body.Dispatch.Duration, timeUnit, models.DispatchMilestone)

		generatedEmails = append(generatedEmails, &models.GeneratedEmail{
			FreightTrackingID: load.FreightTrackingID,
			User:              user,
			Service:           service,
			Loads:             []models.Load{load},
			Recipients:        pq.StringArray{body.Dispatch.Recipient},
			CC:                pq.StringArray{body.Dispatch.CC},
			Subject:           body.Dispatch.Subject,
			Body:              body.Dispatch.EmailDraft,
			Milestone:         models.DispatchMilestone,
			Status:            models.PendingStatus,
			ScheduleSend: models.NullTime{
				Time:  body.Dispatch.SendTime.Add(delay),
				Valid: true,
			},
		})
	}

	if body.Pickup.CheckIn {
		if !load.Pickup.ApptStartTime.Valid {
			log.Error(ctx, "no pickup appointment time set", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}

		timeUnit := getTimeUnit(body.Pickup.TimeUnit)
		delay := getDelay(body.Pickup.Duration, timeUnit, models.PickupMilestone)

		generatedEmails = append(generatedEmails, &models.GeneratedEmail{
			FreightTrackingID: load.FreightTrackingID,
			User:              user,
			Service:           service,
			Loads:             []models.Load{load},
			Recipients:        pq.StringArray{body.Pickup.Recipient},
			CC:                pq.StringArray{body.Pickup.CC},
			Subject:           body.Pickup.Subject,
			Body:              body.Pickup.EmailDraft,
			Milestone:         models.PickupMilestone,
			Status:            models.PendingStatus,
			ScheduleSend: models.NullTime{
				Time:  pickupApptTime.Add(delay),
				Valid: true,
			},
		})
	}

	if body.AfterPickup.CheckIn {
		if !load.Pickup.ApptStartTime.Valid {
			log.Error(ctx, "no pickup appointment time set", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}

		timeUnit := getTimeUnit(body.AfterPickup.TimeUnit)
		delay := getDelay(body.AfterPickup.Duration, timeUnit, models.LoadedMilestone)

		generatedEmails = append(generatedEmails, &models.GeneratedEmail{
			FreightTrackingID: load.FreightTrackingID,
			User:              user,
			Service:           service,
			Loads:             []models.Load{load},
			Recipients:        pq.StringArray{body.AfterPickup.Recipient},
			CC:                pq.StringArray{body.AfterPickup.CC},
			Subject:           body.AfterPickup.Subject,
			Body:              body.AfterPickup.EmailDraft,
			Milestone:         models.LoadedMilestone,
			Status:            models.PendingStatus,
			ScheduleSend: models.NullTime{
				Time:  pickupApptTime.Add(delay),
				Valid: true,
			},
		})
	}

	if body.InTransit.CheckIn {
		if !load.Pickup.ApptStartTime.Valid || !load.Consignee.ApptStartTime.Valid {
			log.Error(ctx, "no appointment times set for pickup or dropoff", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}

		timeUnit := getTimeUnit(body.InTransit.TimeUnit)

		var scheduleTimes []time.Time
		var currentTime time.Time

		if pickupApptTime.After(time.Now()) {
			currentTime = pickupApptTime.Add(time.Duration(body.AfterPickup.Duration) * timeUnit)
		} else {
			currentTime = time.Now()
		}

		endTime := dropoffApptTime

		for currentTime.Before(endTime) {
			scheduleTimes = append(scheduleTimes, currentTime)
			currentTime = currentTime.Add(time.Duration(body.InTransit.Frequency) * timeUnit)
		}

		for _, scheduleTime := range scheduleTimes {
			generatedEmails = append(generatedEmails, &models.GeneratedEmail{
				FreightTrackingID: load.FreightTrackingID,
				User:              user,
				Service:           service,
				Loads:             []models.Load{load},
				Recipients:        pq.StringArray{body.InTransit.Recipient},
				CC:                pq.StringArray{body.InTransit.CC},
				Subject:           body.InTransit.Subject,
				Body:              body.InTransit.EmailDraft,
				Milestone:         models.InTransitMilestone,
				Status:            models.PendingStatus,
				ScheduleSend: models.NullTime{
					Time:  scheduleTime,
					Valid: true,
				},
			})
		}
	}

	if body.Dropoff.CheckIn {
		if !load.Consignee.ApptStartTime.Valid {
			log.Error(ctx, "no dropoff appointment time set", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}

		timeUnit := getTimeUnit(body.Dropoff.TimeUnit)
		delay := getDelay(body.Dropoff.Duration, timeUnit, models.DropoffMilestone)

		generatedEmails = append(generatedEmails, &models.GeneratedEmail{
			FreightTrackingID: load.FreightTrackingID,
			User:              user,
			Service:           service,
			Loads:             []models.Load{load},
			Recipients:        pq.StringArray{body.Dropoff.Recipient},
			CC:                pq.StringArray{body.Dropoff.CC},
			Subject:           body.Dropoff.Subject,
			Body:              body.Dropoff.EmailDraft,
			Milestone:         models.DropoffMilestone,
			Status:            models.PendingStatus,
			ScheduleSend: models.NullTime{
				Time:  dropoffApptTime.Add(delay),
				Valid: true,
			},
		})
	}

	if body.AfterDropoff.CheckIn {
		if !load.Consignee.ApptStartTime.Valid {
			log.Error(ctx, "no dropoff appointment time set", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}

		timeUnit := getTimeUnit(body.AfterDropoff.TimeUnit)
		delay := getDelay(body.AfterDropoff.Duration, timeUnit, models.UnloadedMilestone)

		generatedEmails = append(generatedEmails, &models.GeneratedEmail{
			FreightTrackingID: load.FreightTrackingID,
			User:              user,
			Service:           service,
			Loads:             []models.Load{load},
			Recipients:        pq.StringArray{body.AfterDropoff.Recipient},
			CC:                pq.StringArray{body.AfterDropoff.CC},
			Subject:           body.AfterDropoff.Subject,
			Body:              body.AfterDropoff.EmailDraft,
			Milestone:         models.UnloadedMilestone,
			Status:            models.PendingStatus,
			ScheduleSend: models.NullTime{
				Time:  dropoffApptTime.Add(delay),
				Valid: true,
			},
		})
	}

	if err = genEmailDB.BatchCreateGeneratedEmails(ctx, generatedEmails); err != nil {
		log.Error(ctx, "error storing generated emails", zap.Error(err))
		return c.Status(http.StatusInternalServerError).
			SendString("There was an issue saving the emails that will be sent. Please try again.")
	}

	// NOTE: If in dev, lambda will hang until email's send time
	errs := apiutil.SendEmail(ctx, user, generatedEmails)
	if len(errs) > 0 {
		for _, err := range errs {
			log.Error(ctx, "error sending email", zap.Error(err))
		}
	}

	return c.SendStatus(http.StatusCreated)
}

func getTimeUnit(unit string) time.Duration {
	switch unit {
	case "Minutes":
		return time.Minute
	case "Hours":
		return time.Hour
	case "Days":
		return 24 * time.Hour
	default:
		return time.Hour
	}
}

func getDelay(duration int, timeUnit time.Duration, milestone models.MilestoneStatus) time.Duration {
	if duration == 0 && (milestone == models.PickupMilestone || milestone == models.DropoffMilestone) {
		return preNotificationBuffer * time.Minute
	}

	return time.Duration(duration) * timeUnit
}
