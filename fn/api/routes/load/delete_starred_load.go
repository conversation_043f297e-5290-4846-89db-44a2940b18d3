package load

import (
	"errors"
	"fmt"
	"net/http"
	"slices"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
)

type (
	DeleteStarredLoadPath struct {
		LoadID string `params:"loadID" validate:"required"`
	}
)

func DeleteStarredLoad(c *fiber.Ctx) error {
	var path DeleteStarredLoadPath
	err := api.Parse(c, &path, nil, nil)
	if err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()
	claims := middleware.ClaimsFromContext(c)

	user, err := userDB.GetByEmail(ctx, claims.Email)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(http.StatusNotFound).SendString(
				fmt.Sprintf("email %s not found", claims.Email))
		}

		log.Error(ctx, "user query error", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if !slices.Contains(user.StarredLoads, path.LoadID) {
		errDesc := "load not starred by user"
		log.Error(ctx, errDesc, zap.Error(err))
		return c.Status(http.StatusBadRequest).SendString(errDesc)
	}

	user.StarredLoads = slices.DeleteFunc(user.StarredLoads, func(slID string) bool {
		return slID == path.LoadID
	})
	if err := userDB.Update(ctx, user); err != nil {
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.Status(http.StatusOK).JSON(PutStarredLoadResponse{StarredLoads: user.StarredLoads})
}
