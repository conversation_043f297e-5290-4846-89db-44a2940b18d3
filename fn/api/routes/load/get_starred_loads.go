package load

import (
	"errors"
	"fmt"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
)

type (
	GetStarredLoadsResponse struct {
		StarredLoads []string `json:"starredLoads"`
	}
)

func GetStarredLoads(c *fiber.Ctx) error {
	ctx := log.With(c.UserContext())
	claims := middleware.ClaimsFromContext(c)

	user, err := userDB.GetByEmail(ctx, claims.Email)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(http.StatusNotFound).SendString(
				fmt.Sprintf("email %s not found", claims.Email),
			)
		}

		log.Error(ctx, "user query error", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.Status(http.StatusOK).JSON(GetStarredLoadsResponse{
		StarredLoads: user.StarredLoads,
	})
}
