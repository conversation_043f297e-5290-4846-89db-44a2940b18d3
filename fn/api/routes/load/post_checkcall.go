package load

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/helpers/perms"
	"github.com/drumkitai/drumkit/common/integrations/tms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	checkCallsDB "github.com/drumkitai/drumkit/common/rds/checkcalls"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
	suggestionsDB "github.com/drumkitai/drumkit/common/rds/suggestedloadchanges"
)

type (
	PostCheckCallPath struct {
		LoadID uint `params:"loadID" validate:"required"`
	}

	PostCheckCallBody struct {
		CheckCall    models.CheckCall `json:"checkCall"`
		SuggestionID uint             `json:"suggestionID,omitempty"`
		//nolint:revive // omitempty is a valid validator
		SuggestionStatus string `json:"suggestionStatus,omitempty" validate:"omitempty,oneof=accepted rejected"`
	}

	PostCheckCallResponseV2 struct {
		History []models.CheckCall `json:"history"`
		Message string             `json:"message"`
	}
)

func PostCheckCall(c *fiber.Ctx) error {
	var path PostCheckCallPath
	var body PostCheckCallBody
	if err := api.Parse(c, &path, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.String("freightTrackingID", body.CheckCall.FreightTrackingID))

	load, err := loadDB.GetLoadByID(ctx, path.LoadID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(http.StatusNotFound).SendString(
				fmt.Sprintf("load %d not found", path.LoadID))
		}

		log.Error(ctx, "get checkcalls loadDB failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	log.With(ctx, zap.String("tmsName", string(load.TMS.Name)))

	client, err := tms.New(ctx, load.TMS)
	if err != nil {
		msg := fmt.Sprintf("error creating %s client", load.TMS.Name)
		log.Error(ctx, msg, zap.Error(err))
		return c.Status(http.StatusServiceUnavailable).SendString("error connecting to TMS")
	}

	if !perms.HasLoadReadWritePermissions(c, load, client) {
		err := fmt.Errorf("not allowed to write to load %d", load.ID)
		log.Info(ctx, "forbidden", zap.Error(err))
		return c.Status(http.StatusForbidden).SendString(err.Error())
	}

	body.CheckCall.LoadID = load.ID
	body.CheckCall.FreightTrackingID = load.FreightTrackingID

	if body.SuggestionID > 0 && body.SuggestionStatus != "" {
		suggestion, err := suggestionsDB.GetSuggestionByID(ctx, body.SuggestionID)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return c.Status(http.StatusNotFound).SendString(
					fmt.Sprintf("suggestion ID %d not found", body.SuggestionID),
				)
			}

			log.WarnNoSentry(ctx, "get suggestions endpoint - suggestion query error", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}

		datetime := body.CheckCall.DateTime
		if load.TMS.Name == models.Aljex || load.TMS.Name == models.Tai {
			datetime = body.CheckCall.DateTimeWithoutTimezone
		}

		suggestion.Status = models.SuggestionStatus(body.SuggestionStatus)
		suggestion.Applied = models.SuggestedChanges{
			CheckCallChanges: &models.CheckCallChanges{
				Status:    body.CheckCall.Status,
				Timestamp: datetime,
				City:      body.CheckCall.City,
				State:     body.CheckCall.State,
				Notes:     body.CheckCall.Notes,
			},
		}

		if err = suggestionsDB.UpdateSuggestion(ctx, suggestion); err != nil {
			errMsg := fmt.Sprintf("Suggestion %d was not saved in the DB with status %s."+
				" Please check the DB and make the change manually if necessary.",
				suggestion.ID, body.SuggestionStatus)

			log.Error(ctx, errMsg, zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}
	}

	// In Aljex and TAI, for milestone statuses, POST check call updates timestamp but not load status. In contrast,
	// updating the timestamps directly both creates a check call and updates the load status.
	// TODO: When more TMS's added, refactor handling of TMS differences in AI suggestions
	status := strings.ToLower(body.CheckCall.Status)
	if load.TMS.Name == models.Aljex && isMilestone(status) {
		return handleAljexMilestoneCheckCall(ctx, c, client, load, body)
	}
	if load.TMS.Name == models.Tai && isMilestone(status) {
		return handleTaiMilestoneCheckCall(ctx, c, client, load, body)
	}

	// Otherwise, use regular tms.PostCheckCall flow
	err = client.PostCheckCall(ctx, &load, body.CheckCall)
	if err != nil {
		log.Error(ctx, "error POSTing check call", zap.Error(err))
		return c.Status(http.StatusServiceUnavailable).SendString("error submitting check call to TMS")
	}

	// POST doesn't return the newly created check call, so re-parse list
	curHistory, err := client.GetCheckCallsHistory(ctx, load.ID, load.FreightTrackingID)
	if err != nil {
		// Fail-open; front-end will display list without new check call
		log.Warn(ctx, "error re-parsing check call history", zap.Error(err))
	}

	if err = checkCallsDB.BatchUpsertCheckCalls(ctx, curHistory); err != nil {
		// Fail-open
		log.Warn(ctx, "error upserting check calls", zap.Error(err))
	}

	return c.Status(http.StatusCreated).
		JSON(PostCheckCallResponseV2{History: curHistory, Message: "Submitted check call"})
}

func isMilestone(status string) bool {
	status = strings.ToLower(status)

	return status == "at shipper" || status == "loaded" || status == "at consignee" || status == "delivered"
}

func handleAljexMilestoneCheckCall(
	ctx context.Context,
	c *fiber.Ctx,
	client tms.Interface,
	load models.Load,
	body PostCheckCallBody,
) error {

	refreshedExistingLoad, _, err := client.GetLoad(ctx, load.FreightTrackingID)
	if err != nil {
		log.Error(ctx, "error getting load to update carrier timestamps", zap.Error(err))
		return c.Status(http.StatusServiceUnavailable).SendString("error getting load from TMS")
	}

	reqLoad := refreshedExistingLoad
	// NOTE: Aljex timestamps are significant only to the seconds; truncate so that diff doesn't throw false positive
	t := body.CheckCall.DateTimeWithoutTimezone
	truncatedTime := models.NullTime{Time: t.Time.Truncate(time.Minute), Valid: t.Valid}

	status := strings.ToLower(body.CheckCall.Status)
	switch status {
	case "at shipper":
		reqLoad.Carrier.PickupStart = truncatedTime
	case "loaded":
		reqLoad.Carrier.PickupEnd = truncatedTime
	case "at consignee":
		reqLoad.Carrier.DeliveryStart = truncatedTime
	case "delivered":
		reqLoad.Carrier.DeliveryEnd = truncatedTime
	}

	if refreshedExistingLoad.Diff(reqLoad).IsEmpty() {
		return c.Status(http.StatusOK).
			JSON(PostCheckCallResponseV2{Message: "No changes detected, TMS not updated"})
	}

	updatedLoad, _, err := client.UpdateLoad(ctx, &refreshedExistingLoad, &reqLoad)
	if err != nil {
		log.Error(ctx, "error updating load with carrier timestamp", zap.Error(err))

		return c.Status(http.StatusServiceUnavailable).
			JSON(PostCheckCallResponseV2{Message: "Error updating TMS"})
	}
	if err = loadDB.UpsertLoad(ctx, &updatedLoad, &load.Service); err != nil {
		log.Error(ctx, "error updating load in DB", zap.Error(err))
	}

	// Check if there are differences between the Aljex load before and after the update on the website.
	// There should be a diff IFF an *unsuccessful* update was made.
	diff := updatedLoad.Diff(reqLoad)

	if !diff.IsEmpty() {
		errMsg := diff.BuildErrMsg()

		log.Error(ctx,
			"aljex update didn't raise errors, but there's a mismatch between updates "+
				"requested and aljex load after update",
			zap.String("diff error message", errMsg),
			zap.Any("requested updates on load", reqLoad),
			zap.Any("updated load", updatedLoad),
		)

		return c.Status(http.StatusUnprocessableEntity).
			JSON(PostCheckCallResponseV2{Message: errMsg})
	}

	// POST doesn't return the newly created check call, so re-parse list
	curHistory, err := client.GetCheckCallsHistory(ctx, load.ID, load.FreightTrackingID)
	if err != nil {
		// Fail-open; front-end will display list without new check call
		log.Warn(ctx, "error re-parsing check call history", zap.Error(err))
	}

	if err = checkCallsDB.BatchUpsertCheckCalls(ctx, curHistory); err != nil {
		// Fail-open
		log.Warn(ctx, "error upserting check calls", zap.Error(err))
	}

	response := PostCheckCallResponseV2{
		History: curHistory,
		Message: "Updating load succeeded",
	}

	return c.Status(http.StatusOK).JSON(response)
}

func handleTaiMilestoneCheckCall(
	ctx context.Context,
	c *fiber.Ctx,
	client tms.Interface,
	load models.Load,
	body PostCheckCallBody,
) error {
	err := client.PostCheckCall(ctx, &load, body.CheckCall)
	if err != nil {
		log.Error(ctx, "error POSTing check call location", zap.Error(err))
	}

	refreshedExistingLoad, _, err := client.GetLoad(ctx, load.FreightTrackingID)
	if err != nil {
		log.Error(ctx, "error getting load to update carrier timestamps", zap.Error(err))
		return c.Status(http.StatusServiceUnavailable).SendString("error getting load from TMS")
	}

	reqLoad := refreshedExistingLoad
	t := body.CheckCall.DateTimeWithoutTimezone
	if !t.Valid {
		t = body.CheckCall.DateTime
	}
	truncatedTime := models.NullTime{Time: t.Time.Truncate(time.Minute), Valid: t.Valid}

	status := strings.ToLower(body.CheckCall.Status)
	switch status {
	case "at shipper":
		reqLoad.Carrier.PickupStart = truncatedTime
	case "loaded":
		reqLoad.Carrier.PickupEnd = truncatedTime
	case "at consignee":
		reqLoad.Carrier.DeliveryStart = truncatedTime
	case "delivered":
		reqLoad.Carrier.DeliveryEnd = truncatedTime
	}

	if refreshedExistingLoad.Diff(reqLoad).IsEmpty() {
		curHistory, err := client.GetCheckCallsHistory(ctx, load.ID, load.FreightTrackingID)
		if err != nil {
			log.Warn(ctx, "error re-parsing check call history", zap.Error(err))
		} else if err = checkCallsDB.BatchUpsertCheckCalls(ctx, curHistory); err != nil {
			log.Warn(ctx, "error upserting check calls", zap.Error(err))
		}

		return c.Status(http.StatusOK).
			JSON(PostCheckCallResponseV2{
				History: curHistory,
				Message: "Location check call posted, no timestamp changes detected",
			})
	}

	updatedLoad, _, err := client.UpdateLoad(ctx, &refreshedExistingLoad, &reqLoad)
	if err != nil {
		log.Error(ctx, "error updating load with carrier timestamp", zap.Error(err))

		return c.Status(http.StatusServiceUnavailable).
			JSON(PostCheckCallResponseV2{Message: "Error updating TMS"})
	}
	if err = loadDB.UpsertLoad(ctx, &updatedLoad, &load.Service); err != nil {
		log.Error(ctx, "error updating load in DB", zap.Error(err))
	}

	diff := updatedLoad.Diff(reqLoad)

	if !diff.IsEmpty() {
		errMsg := diff.BuildErrMsg()

		log.Error(
			ctx,
			"tai update didn't raise errors, but there's a mismatch between updates "+
				"requested and tai load after update",
			zap.String("diff error message", errMsg),
			zap.Any("requested updates on load", reqLoad),
			zap.Any("updated load", updatedLoad),
		)

		return c.Status(http.StatusUnprocessableEntity).
			JSON(PostCheckCallResponseV2{Message: errMsg})
	}

	// POST doesn't return the newly created check call, so re-parse list
	curHistory, err := client.GetCheckCallsHistory(ctx, load.ID, load.FreightTrackingID)
	if err != nil {
		// Fail-open; front-end will display list without new check call
		log.Warn(ctx, "error re-parsing check call history", zap.Error(err))
	}

	if err = checkCallsDB.BatchUpsertCheckCalls(ctx, curHistory); err != nil {
		// Fail-open
		log.Warn(ctx, "error upserting check calls", zap.Error(err))
	}

	response := PostCheckCallResponseV2{
		History: curHistory,
		Message: "Updating load succeeded",
	}

	return c.Status(http.StatusOK).JSON(response)
}
