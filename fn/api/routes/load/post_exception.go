package load

import (
	"errors"
	"fmt"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/helpers/perms"
	"github.com/drumkitai/drumkit/common/integrations/tms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	exceptionsDB "github.com/drumkitai/drumkit/common/rds/exceptions"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
)

type (
	PostExceptionPath struct {
		LoadID uint `params:"loadID" validate:"required"`
	}

	PostExceptionBody = models.Exception
)

func PostException(c *fiber.Ctx) error {
	var path PostExceptionPath
	var body PostExceptionBody
	if err := api.Parse(c, &path, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.String("freightTrackingID", body.Load.FreightTrackingID))

	load, err := loadDB.GetLoadByID(ctx, path.LoadID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(http.StatusNotFound).SendString(
				fmt.Sprintf("load %d not found", path.LoadID))
		}

		log.Error(ctx, "get exceptions loadDB failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	client, err := tms.New(ctx, load.TMS)
	if err != nil {
		log.Error(ctx, "error creating TMS client", zap.Error(err))
		return c.Status(http.StatusServiceUnavailable).SendString("error connecting to TMS")
	}

	if !perms.HasLoadReadWritePermissions(c, load, client) {
		err := fmt.Errorf("not allowed to read load %d", load.ID)

		log.Info(ctx, "forbidden", zap.Error(err))
		return c.Status(http.StatusForbidden).SendString(err.Error())
	}

	if err = client.PostException(ctx, &load, body); err != nil {
		log.Error(ctx, "error POSTing exception", zap.Error(err))
		return c.Status(http.StatusServiceUnavailable).SendString("error submitting exception to TMS")
	}

	// POST doesn't return the newly created exception, so re-parse list
	curHistory, err := client.GetExceptionHistory(ctx, load.ID, load.FreightTrackingID)
	if err != nil {
		// Fail-open; front-end will display list without new exception
		log.Warn(ctx, "error re-parsing exception history", zap.Error(err))
	}

	if err = exceptionsDB.BatchUpsertExceptions(ctx, curHistory); err != nil {
		// Fail-open
		log.Warn(ctx, "error upserting exceptions", zap.Error(err))
	}

	return c.Status(http.StatusCreated).JSON(curHistory)
}
