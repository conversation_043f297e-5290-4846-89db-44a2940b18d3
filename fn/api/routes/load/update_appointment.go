package load

import (
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers/perms"
	"github.com/drumkitai/drumkit/common/integrations/tms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
)

type (
	SubmitAppointmentPath struct {
		LoadID uint `params:"loadID" validate:"required"`
	}

	SubmitAppointmentBody struct {
		Load struct {
			FreightTrackingID string `json:"freightTrackingID"`
			Pickup            *struct {
				ApptStartTime models.NullTime `json:"apptStartTime"`
				ApptEndTime   models.NullTime `json:"apptEndTime"`
				ApptType      string          `json:"apptType"`
			} `json:"pickup,omitempty"`
			Consignee *struct {
				ApptStartTime models.NullTime `json:"apptStartTime"`
				ApptEndTime   models.NullTime `json:"apptEndTime"`
				ApptType      string          `json:"apptType"`
			} `json:"consignee,omitempty"`
		} `json:"load"`
	}

	SubmitAppointmentResponse struct {
		Load           models.Load           `json:"load"`
		LoadAttributes models.LoadAttributes `json:"loadAttributes"`
		Message        string                `json:"message,omitempty"`
	}

	ErrorResponse struct {
		Message string `json:"message"`
	}
)

// UpdateLoadWithAppointment updates a load in TMS with newly provided appointment details
func UpdateLoadWithAppointment(c *fiber.Ctx) error {
	var path SubmitAppointmentPath
	var body SubmitAppointmentBody
	if err := api.Parse(c, &path, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).JSON(ErrorResponse{
			Message: "There was an error with your request to update the load in TMS.",
		})
	}

	ctx := log.With(c.UserContext(), zap.Any("pathParams", path))

	existingLoad, err := loadDB.GetLoadByID(ctx, path.LoadID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(http.StatusNotFound).JSON(ErrorResponse{
				Message: fmt.Sprintf("load with ID %d not found", path.LoadID),
			})
		}

		log.Error(ctx, "loadDB query error", zap.Error(err))
		return c.Status(http.StatusInternalServerError).JSON(ErrorResponse{
			Message: "Failed to find load. Please update the load manually in your TMS.",
		})
	}

	// Use load from DB if fresh, otherwise refresh from TMS if partial load updates are not supported
	baseLoad := existingLoad
	if shouldRefreshFromTMS(existingLoad.UpdatedAt, 2*time.Minute, existingLoad.TMS.Name) {
		refreshedLoad, _, err := getLoadFromTMS(ctx, existingLoad.TMS, existingLoad.FreightTrackingID)
		if err != nil {
			log.ErrorNoSentry(ctx, "failed to get load from TMS for appointment details update", zap.Error(err))

			return c.Status(http.StatusServiceUnavailable).JSON(ErrorResponse{
				Message: fmt.Sprintf(
					`There was an error updating this load in %s. Please try again. `+
						`If the issue persists, contact support.`,
					models.FormatIntegrationName(existingLoad.TMS.Name),
				),
			})
		}
		baseLoad = refreshedLoad
		log.Info(
			ctx,
			"using refreshed load from TMS as base for appointment update",
			zap.String("freightTrackingID", existingLoad.FreightTrackingID),
		)
	}

	// Start with the base load data to preserve all fields
	updateLoad := baseLoad

	// Only update the appointment fields that are provided
	if body.Load.Pickup != nil {
		if body.Load.Pickup.ApptStartTime.Valid {
			updateLoad.Pickup.ApptStartTime = body.Load.Pickup.ApptStartTime
		}
		if body.Load.Pickup.ApptEndTime.Valid {
			updateLoad.Pickup.ApptEndTime = body.Load.Pickup.ApptEndTime
		}
		if body.Load.Pickup.ApptType != "" {
			updateLoad.Pickup.ApptType = body.Load.Pickup.ApptType
		}
	}

	if body.Load.Consignee != nil {
		if body.Load.Consignee.ApptStartTime.Valid {
			updateLoad.Consignee.ApptStartTime = body.Load.Consignee.ApptStartTime
		}
		if body.Load.Consignee.ApptEndTime.Valid {
			updateLoad.Consignee.ApptEndTime = body.Load.Consignee.ApptEndTime
		}
		if body.Load.Consignee.ApptType != "" {
			updateLoad.Consignee.ApptType = body.Load.Consignee.ApptType
		}
	}

	if body.Load.FreightTrackingID != "" {
		updateLoad.FreightTrackingID = body.Load.FreightTrackingID
	}

	client, err := tms.New(ctx, existingLoad.TMS)
	if err != nil {
		log.Error(ctx, "error creating TMS client", zap.Error(err))

		return c.Status(http.StatusServiceUnavailable).JSON(ErrorResponse{
			Message: "Error connecting to TMS",
		})
	}

	if !perms.HasLoadReadWritePermissions(c, existingLoad, client) {
		err := fmt.Errorf("user not allowed to write to load %d", existingLoad.ID)
		log.Info(ctx, "forbidden", zap.Error(err))

		return c.Status(http.StatusForbidden).JSON(ErrorResponse{
			Message: "Not allowed to update this load in TMS. Please contact support for help.",
		})
	}

	// Update TMS
	updatedLoad, attrs, err := client.UpdateLoad(ctx, &baseLoad, &updateLoad)
	if err != nil {
		var ufErr errtypes.UserFacingError
		isUfErr := errors.As(err, &ufErr)

		// TODO: Filter out user-facing errors from Sentry after observation period
		log.Error(ctx, "error updating TMS", zap.Error(err))
		if isUfErr {
			return c.Status(http.StatusBadRequest).JSON(ErrorResponse{
				Message: ufErr.Error(),
			})
		}

		return c.Status(http.StatusServiceUnavailable).JSON(ErrorResponse{
			Message: "Error while updating TMS",
		})
	}

	// Update database with the updated load from TMS
	if err = loadDB.UpsertLoad(ctx, &updatedLoad, nil); err != nil {
		log.Error(ctx, "error updating load in DB", zap.Error(err))
		// Don't return error here - TMS update was successful, DB update is secondary
	}

	response := SubmitAppointmentResponse{
		Load:           updatedLoad,
		LoadAttributes: attrs,
		Message: fmt.Sprintf(
			"Load successfully updated with appointment details in %s",
			models.FormatIntegrationName(existingLoad.TMS.Name),
		),
	}

	return c.Status(http.StatusOK).JSON(response)
}

// shouldRefreshFromTMS returns true when the DB copy is older than the threshold and partial updates are not supported
func shouldRefreshFromTMS(updatedAt time.Time, threshold time.Duration, tmsName models.IntegrationName) bool {
	// No need to refresh from TMS (to avoid overwriting live load with stale DB data if partial updates are supported)
	if models.SupportsPartialLoadUpdates(tmsName) {
		return false
	}

	if updatedAt.IsZero() {
		return true
	}

	return time.Since(updatedAt) > threshold
}
