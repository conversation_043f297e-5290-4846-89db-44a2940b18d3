package load

import (
	"net/http"
	"strconv"

	"github.com/gofiber/fiber/v2"

	"github.com/drumkitai/drumkit/common/models"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
)

type GetOrdersForLoadResponse struct {
	Orders []models.Order `json:"orders"`
}

// GetOrdersForLoad retrieves all orders associated with a load
func GetOrdersForLoad(c *fiber.Ctx) error {
	loadID, err := strconv.ParseUint(c.Params("loadID"), 10, 32)
	if err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	load, err := loadDB.GetLoadByID(c.UserContext(), uint(loadID))
	if err != nil {
		return c.Status(http.StatusInternalServerError).SendString(err.Error())
	}

	orders, err := loadDB.GetOrdersForLoad(
		c.UserContext(),
		uint(loadID),
		load.ExternalTMSID,
	)
	if err != nil {
		return c.Status(http.StatusInternalServerError).SendString(err.Error())
	}

	return c.JSON(GetOrdersForLoadResponse{
		Orders: orders,
	})
}
