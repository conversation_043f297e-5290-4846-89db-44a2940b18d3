package metabase

import (
	"errors"
	"fmt"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	metabaseDashboardDB "github.com/drumkitai/drumkit/common/rds/metabase_dashboard"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
)

type (
	DeleteDashboardPath struct {
		DashboardID uint `path:"dashboardId" validate:"required"`
	}
)

// AdminDeleteDashboard deletes a Metabase dashboard
func AdminDeleteDashboard(c *fiber.Ctx) error {
	var path DeleteDashboardPath
	if err := api.Parse(c, &path, nil, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Any("pathParams", path))

	// Only allow Drumkit admins to delete dashboards
	email := middleware.ClaimsFromContext(c).Email
	if err := isDrumkitAdmin(email); err != nil {
		log.WarnNoSentry(ctx, "unauthorized: non-drumkit user trying to delete dashboard")
		return c.SendStatus(http.StatusUnauthorized)
	}

	user, err := userDB.GetByEmail(ctx, email)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(http.StatusNotFound).SendString(
				fmt.Sprintf("email %s not found", email))
		}

		log.Error(ctx, "user query error", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	serviceID := middleware.ServiceIDFromContext(c)
	if user.ServiceID != serviceID {
		log.Infof(
			ctx,
			"claims serviceID %d does not match user service ID %d",
			serviceID,
			user.ServiceID,
		)

		return c.SendStatus(http.StatusUnauthorized)
	}

	dashboard, err := metabaseDashboardDB.GetByID(ctx, path.DashboardID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(http.StatusNotFound).
				SendString(fmt.Sprintf("dashboard ID %d not found", path.DashboardID))
		}

		log.Error(ctx, "error fetching dashboard", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if user.ServiceID != dashboard.ServiceID {
		log.Infof(
			ctx,
			"user serviceID %d does not match dashboard service ID %d",
			user.ServiceID,
			dashboard.ServiceID,
		)

		return c.SendStatus(http.StatusForbidden)
	}

	if err := metabaseDashboardDB.Delete(ctx, path.DashboardID); err != nil {
		log.Error(ctx, "error deleting dashboard", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.SendStatus(http.StatusNoContent)
}
