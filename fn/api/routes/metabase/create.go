package metabase

import (
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	metabaseDashboardDB "github.com/drumkitai/drumkit/common/rds/metabase_dashboard"
)

type (
	CreateDashboardBody struct {
		URL           string               `json:"url"`
		QuestionID    int                  `json:"questionId"`
		DashboardID   int                  `json:"dashboardId"`
		Name          string               `json:"name" validate:"required"`
		Description   string               `json:"description"`
		DashboardType models.DashboardType `json:"dashboardType"`
		UserID        *uint                `json:"userId"`
		ServiceID     uint                 `json:"serviceId" validate:"required"`
	}
)

// AdminCreateDashboard creates a new Metabase dashboard
func AdminCreateDashboard(c *fiber.Ctx) error {
	var body CreateDashboardBody
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Any("requestBody", body))

	// Only allow Drumkit admins to create dashboards
	email := middleware.ClaimsFromContext(c).Email
	if err := isDrumkitAdmin(email); err != nil {
		log.WarnNoSentry(ctx, "unauthorized: non-drumkit user trying to create dashboard")
		return c.SendStatus(http.StatusUnauthorized)
	}

	var uuid string
	if body.URL != "" {
		uuid = extractUUIDFromURL(body.URL)
		if uuid == "" {
			return c.Status(http.StatusBadRequest).SendString("invalid Metabase dashboard URL format")
		}
	}

	dashboard := models.MetabaseDashboard{
		URL:           body.URL,
		QuestionID:    body.QuestionID,
		DashboardID:   body.DashboardID,
		UUID:          uuid,
		Name:          body.Name,
		Description:   body.Description,
		DashboardType: body.DashboardType,
		ServiceID:     body.ServiceID,
		UserID:        body.UserID,
	}

	if err := metabaseDashboardDB.Create(ctx, &dashboard); err != nil {
		log.Error(ctx, "error creating dashboard", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.Status(http.StatusCreated).JSON(DashboardResponse{
		ID:            dashboard.ID,
		URL:           dashboard.URL,
		QuestionID:    dashboard.QuestionID,
		DashboardID:   dashboard.DashboardID,
		UUID:          dashboard.UUID,
		Name:          dashboard.Name,
		Description:   dashboard.Description,
		DashboardType: dashboard.DashboardType,
		ServiceID:     dashboard.ServiceID,
		UserID:        dashboard.UserID,
		CreatedAt:     dashboard.CreatedAt.String(),
		UpdatedAt:     dashboard.UpdatedAt.String(),
	})
}
