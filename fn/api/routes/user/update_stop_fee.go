package user

import (
	"errors"
	"fmt"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
)

type (
	UpdateStopFeeBody struct {
		StopFee int `json:"stopFee" validate:"required"`
	}
)

func UpdateStopFee(c *fiber.Ctx) error {
	var body UpdateStopFeeBody
	err := api.Parse(c, nil, nil, &body)
	if err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Int("stopFee", body.StopFee))
	claims := middleware.ClaimsFromContext(c)

	user, err := userDB.GetByEmail(ctx, claims.Email)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(http.StatusNotFound).SendString(fmt.Sprintf("email %s not found", claims.Email))
		}

		log.Error(ctx, "error getting user", zap.String("email", claims.Email), zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	userEmptyStopFee := user.IntermediateStopFeeUSD == nil
	userMatchingStopFee := !userEmptyStopFee && body.StopFee == *user.IntermediateStopFeeUSD

	if userMatchingStopFee {
		return c.Status(http.StatusOK).SendString("submitted values match existing stop fee")
	}

	user.IntermediateStopFeeUSD = &body.StopFee

	if err := userDB.Update(ctx, user); err != nil {
		log.Error(ctx, "could not update user", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.SendStatus(http.StatusOK)
}
