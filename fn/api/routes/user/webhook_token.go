package user

import (
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api/jwt"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/fn/api/env"
)

type TokenResponse struct {
	AccessToken     string `json:"access_token"`
	TokenExpiration int64  `json:"token_expiration"` // In Unix time
	Email           string `json:"email"`
	ServiceID       uint   `json:"service_id"`
	TokenType       string `json:"token_type"`
}

// generate JWT token that expires in 5 years
func WebhookTokenGenerator(c *fiber.Ctx) error {
	claims := middleware.ClaimsFromContext(c)
	ctx := log.With(c.UserContext())

	accessToken, err := jwt.NewWebhookToken(
		claims.Email,
		jwt.WithAppEnv(env.Vars.AppEnv),
		jwt.WithServiceID(claims.ServiceID),
		jwt.WithJWT(env.Vars.JWT),
	)
	if err != nil {
		log.Error(ctx, "creating new jwt failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.Status(http.StatusOK).JSON(TokenResponse{
		AccessToken:     accessToken,
		Email:           claims.Email,
		ServiceID:       *claims.ServiceID,
		TokenExpiration: time.Now().Add(jwt.WebhookTokenDuration).Unix(),
		TokenType:       jwt.TokenTypeWebhook,
	})
}
