package user

import (
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	userGroupsDB "github.com/drumkitai/drumkit/common/rds/usergroups"
)

type (
	UpdateUserGroupsBody struct {
		UserGroupID uint `json:"userGroupID"`
	}
)

func UpdateUserGroups(c *fiber.Ctx) error {
	var body UpdateUserGroupsBody
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Any("bodyParams", body))

	email := middleware.ClaimsFromContext(c).Email
	user, err := rds.GetUserByEmail(ctx, email)
	if err != nil {
		log.Error(ctx, "user query error", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	userGroup, err := userGroupsDB.GetByID(ctx, body.UserGroupID)
	if err != nil {
		log.Error(ctx, "error while getting user group", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	user.UserGroups = []models.UserGroup{userGroup}
	if err := rds.UpdateUserReplaceGroups(ctx, user); err != nil {
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.SendStatus(http.StatusOK)
}
