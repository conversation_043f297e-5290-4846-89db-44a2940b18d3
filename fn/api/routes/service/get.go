package service

import (
	"errors"
	"fmt"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/rds"
)

type (
	GetServicePath struct {
		Nickname string `json:"nickname"`
	}

	GetServiceResponse struct {
		ID   uint   `json:"id"`
		Name string `json:"name"`
	}
)

// NOTE: Unprotected endpoint. Do not expose private information.
// If more info needed, update/create ProtectedGetService handler instead
func PublicGetService(c *fiber.Ctx) error {
	var path GetServicePath
	if err := api.Parse(c, &path, nil, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()

	service, err := rds.GetServiceByNickname(ctx, path.Nickname)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(http.StatusNotFound).
				SendString(fmt.Sprintf("service %s not found", path.Nickname))
		}

		log.Error(ctx, "serviceDB query error", zap.Error(err))
		return c.SendStatus(http.StatusNotFound)
	}

	return c.Status(http.StatusOK).JSON(GetServiceResponse{
		ID:   service.ID,
		Name: service.Name,
	})
}
