package service

import (
	"fmt"
	"net/http"
	"reflect"
	"strings"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

type EnableFeatureFlagPath struct {
	ServiceID uint `json:"service_id"`
}

type EnableFeatureFlagBody struct {
	FeatureFlag string `json:"featureFlag" validate:"required"`
}

type EnableFeatureFlagResponse struct {
	ServiceID    uint                `json:"serviceId"`
	FeatureFlag  string              `json:"featureFlag"`
	NewValue     bool                `json:"newValue"`
	FeatureFlags models.FeatureFlags `json:"featureFlags"`
}

func EnableFeatureFlag(c *fiber.Ctx) error {
	var path EnableFeatureFlagPath
	var body EnableFeatureFlagBody
	if err := api.Parse(c, &path, nil, &body); err != nil {
		log.Error(
			c.UserContext(),
			fmt.Sprintf(
				"error validating request for enabling feature flag %s on service %d",
				body.FeatureFlag,
				path.ServiceID,
			),
			zap.Error(err),
		)

		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Any("pathParams", path), zap.Any("body", body))
	userID := middleware.UserIDFromContext(c)

	user, err := rds.GetUserByID(ctx, userID)
	if err != nil {
		log.Error(ctx, "error getting user", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	// <NAME_EMAIL> to access this endpoint
	if user.EmailAddress != "<EMAIL>" {
		log.Warn(ctx, "unauthorized access attempt to enableFeatureFlag", zap.String("email", user.EmailAddress))
		return c.SendStatus(http.StatusForbidden)
	}

	// Get the service
	service, err := rds.GetServiceByID(ctx, path.ServiceID)
	if err != nil {
		log.Error(ctx, "error getting service", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	// Find the field by JSON name (case-insensitive)
	field, found := findFieldByJSONName(&service.FeatureFlags, body.FeatureFlag)

	if !found {
		validFlags := getValidFeatureFlags()
		errorMsg := fmt.Sprintf("invalid feature flag: %s. Valid feature flags are: %s",
			body.FeatureFlag, strings.Join(validFlags, ", "))
		log.Error(ctx, "invalid feature flag name", zap.String("featureFlag", body.FeatureFlag))
		return c.Status(http.StatusBadRequest).SendString(errorMsg)
	}

	if field.Kind() != reflect.Bool {
		log.Error(ctx, "feature flag is not a boolean field", zap.String("featureFlag", body.FeatureFlag))
		return c.Status(http.StatusBadRequest).SendString(
			fmt.Sprintf("feature flag %s is not a boolean field", body.FeatureFlag),
		)
	}

	// Set the boolean value to true
	oldValue := field.Bool()
	field.SetBool(true)
	newValue := field.Bool()

	// Update the service in the database
	if err := rds.UpdateServiceFeatureFlags(ctx, &service); err != nil {
		log.Error(ctx, "error updating service", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	log.Info(
		ctx,
		"feature flag enabled successfully",
		zap.Uint("serviceID", path.ServiceID),
		zap.String("featureFlag", body.FeatureFlag),
		zap.Bool("oldValue", oldValue),
		zap.Bool("newValue", newValue),
	)

	return c.Status(http.StatusOK).JSON(EnableFeatureFlagResponse{
		ServiceID:    path.ServiceID,
		FeatureFlag:  body.FeatureFlag,
		NewValue:     newValue,
		FeatureFlags: service.FeatureFlags,
	})
}
