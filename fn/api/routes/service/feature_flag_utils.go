package service

import (
	"reflect"
	"strings"

	"github.com/drumkitai/drumkit/common/models"
)

// getValidFeatureFlags returns a list of all valid feature flag JSON field names
func getValidFeatureFlags() []string {
	var flags []string
	featureFlagsType := reflect.TypeOf(models.FeatureFlags{})

	for i := 0; i < featureFlagsType.NumField(); i++ {
		field := featureFlagsType.Field(i)
		if field.Type.Kind() == reflect.Bool {
			jsonTag := field.Tag.Get("json")
			if jsonTag != "" && jsonTag != "-" {
				flags = append(flags, jsonTag)
			}
		}
	}

	return flags
}

// findFieldByJSONName finds a field by its JSON tag name (case-insensitive)
func findFieldByJSONName(featureFlags *models.FeatureFlags, jsonName string) (reflect.Value, bool) {
	serviceValue := reflect.ValueOf(featureFlags).Elem()
	featureFlagsType := reflect.TypeOf(*featureFlags)

	for i := 0; i < featureFlagsType.NumField(); i++ {
		field := featureFlagsType.Field(i)
		jsonTag := field.Tag.Get("json")

		if jsonTag != "" && jsonTag != "-" && strings.EqualFold(jsonTag, jsonName) {
			return serviceValue.Field(i), true
		}
	}

	return reflect.Value{}, false
}
