package service

import (
	"errors"
	"fmt"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
)

type (
	UpdateServicePriceMarginBody struct {
		DefaultMargin     int               `json:"defaultMargin" validate:"required"`
		DefaultMarginType models.MarginType `json:"defaultMarginType" validate:"required"`
	}
)

func UpdateServiceDefaultPriceMargin(c *fiber.Ctx) error {
	var body UpdateServicePriceMarginBody
	err := api.Parse(c, nil, nil, &body)
	if err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	claims := middleware.ClaimsFromContext(c)
	ctx := log.With(
		c.UserContext(),
		zap.String("emailAddress", claims.Email),
		zap.Uint("serviceID", *claims.ServiceID),
	)

	user, err := userDB.GetByEmail(ctx, claims.Email)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(http.StatusNotFound).SendString(fmt.Sprintf("email %s not found", claims.Email))
		}

		log.Error(ctx, "error getting user", zap.String("email", claims.Email), zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if user.Role != models.Admin {
		return c.SendStatus(http.StatusUnauthorized)
	}

	service, err := rds.GetServiceByID(ctx, user.ServiceID)
	if err != nil {
		log.Error(ctx, "could not get service", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if service.QuickQuoteConfig == nil {
		service.QuickQuoteConfig = &models.QuickQuoteConfig{
			ServiceID: service.ID,
		}
	}

	service.QuickQuoteConfig.DefaultMarginType = body.DefaultMarginType

	if body.DefaultMarginType == models.Percentage {
		service.QuickQuoteConfig.DefaultPercentMargin = body.DefaultMargin
	} else {
		service.QuickQuoteConfig.DefaultFlatMargin = body.DefaultMargin
	}

	if err = rds.UpdateService(ctx, &service); err != nil {
		log.Error(ctx, "could not update service quick quote config", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.SendStatus(http.StatusOK)
}
