package service

import (
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

type GetAllFeaturesResponse struct {
	Services []FeaturesSummary `json:"services"`
}

type FeaturesSummary struct {
	ID   uint   `json:"id"`
	Name string `json:"name"`
	models.FeatureFlags
}

func GetAllServiceFeatures(c *fiber.Ctx) error {
	ctx := c.UserContext()
	userID := middleware.UserIDFromContext(c)

	user, err := rds.GetUserByID(ctx, userID)
	if err != nil {
		log.Error(ctx, "error getting user", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	// <NAME_EMAIL> to access this endpoint
	if user.EmailAddress != "<EMAIL>" {
		log.Warn(
			ctx,
			"unauthorized access attempt to getAllServiceFeatures",
			zap.String("email", user.EmailAddress),
		)

		return c.SendStatus(http.StatusForbidden)
	}

	services, err := rds.GetServiceAll(ctx)
	if err != nil {
		log.Error(ctx, "error getting all services", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	var serviceSummaries []FeaturesSummary
	for _, service := range services {
		serviceSummaries = append(serviceSummaries, FeaturesSummary{
			ID:           service.ID,
			Name:         service.Name,
			FeatureFlags: service.FeatureFlags,
		})
	}

	return c.Status(http.StatusOK).JSON(GetAllFeaturesResponse{
		Services: serviceSummaries,
	})
}
