package env

import (
	"context"
	"fmt"
	"os"

	"github.com/joho/godotenv"
	"github.com/kelseyhightower/envconfig"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/awsutil"
	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/rds"
)

var Vars Variables

type Variables struct {
	rds.EnvConfig

	// Deployment stage: "dev" or "prod"
	AppEnv string `envconfig:"APP_ENV" required:"true"`

	RedisURL string `envconfig:"REDIS_URL"`
	TraceOn  bool   `envconfig:"TRACE_ON"`

	// Associated with the Drumkit Google/Microsoft apps
	GoogleClientID    string `envconfig:"GOOGLE_CLIENT_ID" required:"true"`
	MicrosoftClientID string `envconfig:"MICROSOFT_CLIENT_ID" required:"true"`

	// For internal API authentication
	InternalDrumkitKey string `envconfig:"INTERNAL_DRUMKIT_KEY"`

	// ProdTopicName    = "projects/beacon-397017/topics/GmailInboxPush"
	// StagingTopicName = "projects/beacon-staging-417618/topics/GmailInboxPush-Staging"
	// Register Drumkit Gmail webhooks to this topic
	GmailWebhookTopic string `envconfig:"GMAIL_WEBHOOK_TOPIC" default:"projects/beacon-397017/topics/GmailInboxPush"`
	// Where should outlook send msg webhooks? I.e. beacon-outlook-ingestion function URL
	MicrosoftWebhookURL string `envconfig:"MICROSOFT_WEBHOOK_URL" required:"true"`
	DrumkitProcessorURL string `envconfig:"DRUMKIT_PROCESSOR_URL"`

	AxiomLogDataset   string `envconfig:"AXIOM_LOG_DATASET"`
	AxiomTraceDataset string `envconfig:"AXIOM_TRACE_DATASET"`
	AxiomOrgID        string `envconfig:"AXIOM_ORG_ID"`
	AxiomToken        string `envconfig:"AXIOM_TOKEN"`

	BraintrustBaseURL string `envconfig:"BRAINTRUST_BASE_URL"`
	BraintrustAPIKey  string `envconfig:"BRAINTRUST_API_KEY"`

	CyclopsURL string `envconfig:"CYCLOPS_URL"`
	// Prod Pipedream URL: https://eoet14oob9cgxro.m.pipedream.net
	OnboardingPipedreamURL string `envconfig:"ONBOARDING_PIPEDREAM_URL"`

	// For prod only
	APISecretARN    string `envconfig:"API_SECRET_ARN"`
	StateMachineARN string `envconfig:"STATE_MACHINE_ARN"`
	S3BucketName    string `envconfig:"S3_BUCKET_NAME"`

	BackfillSQSQueueURL string `envconfig:"BACKFILL_SQS_QUEUE_URL"`
	BackfillHours       int    `envconfig:"BACKFILL_HOURS" default:"24"`

	// In dev, these are set directly.
	// In prod, these are loaded from APISecretARN at startup.
	GoogleClientSecret    string `envconfig:"GOOGLE_CLIENT_SECRET"`
	MicrosoftClientSecret string `envconfig:"MICROSOFT_CLIENT_SECRET"`
	JWT                   string `envconfig:"JWT"`
	USPSUserID            string `envconfig:"USPS_USER_ID"`

	// Metabase
	MetabaseSiteURL   string `envconfig:"METABASE_SITE_URL"`
	MetabaseSecretKey string `envconfig:"METABASE_SECRET_KEY"`
}

// Format in AWS secrets manager
type envSecrets struct {
	AESKey                string `json:"AES_KEY"`
	GoogleClientSecret    string `json:"GOOGLE_CLIENT_SECRET"`
	MicrosoftClientSecret string `json:"MICROSOFT_CLIENT_SECRET"`
	JWT                   string `json:"JWT"`
	USPSUserID            string `json:"USPS_USER_ID"`
	StateMachineARN       string `json:"STATE_MACHINE_ARN"`
	InternalDrumkitKey    string `json:"INTERNAL_DRUMKIT_KEY"`
}

// Load environment variables into Vars global
func Load(ctx context.Context) error {
	if stage := os.Getenv("APP_ENV"); stage == "" || stage == "dev" {
		if err := godotenv.Load(); err != nil {
			log.Warn(ctx, "no .env file found", zap.Error(err))
		}
		ctx = log.NewFromEnv(ctx)
	}

	if err := envconfig.Process("", &Vars); err != nil {
		return err
	}

	switch Vars.AppEnv {
	case "dev":
		if Vars.RedisURL == "" {
			log.WarnNoSentry(ctx, "missing Redis URL")
		}

		if Vars.GoogleClientSecret == "" {
			log.WarnNoSentry(ctx, "missing GOOGLE_CLIENT_SECRET")
		}

		if Vars.MicrosoftClientSecret == "" {
			log.WarnNoSentry(ctx, "missing MICROSOFT_CLIENT_SECRET")
		}

		if Vars.S3BucketName == "" {
			log.WarnNoSentry(ctx, "missing AWS S3 bucket name")
		}

		if Vars.USPSUserID == "" {
			log.WarnNoSentry(ctx, "missing USPS User ID for zipcode lookups")
		}

	case "prod", "staging":
		if Vars.AxiomLogDataset == "" {
			log.Warn(ctx, "missing Axiom Log Dataset")
		}

		if Vars.AxiomTraceDataset == "" {
			log.Warn(ctx, "missing Axiom Trace Dataset")
		}

		if Vars.AxiomOrgID == "" {
			log.Warn(ctx, "missing Axiom Org ID")
		}

		var secret envSecrets

		if err := awsutil.ReadSecretJSON(ctx, Vars.APISecretARN, &secret); err != nil {
			return err
		}

		if secret.AESKey == "" || secret.GoogleClientSecret == "" || secret.MicrosoftClientSecret == "" ||
			secret.JWT == "" ||
			secret.USPSUserID == "" || secret.StateMachineARN == "" || secret.InternalDrumkitKey == "" {

			return fmt.Errorf("%s is missing some fields", Vars.APISecretARN)
		}

		crypto.AESKey = []byte(secret.AESKey)
		Vars.GoogleClientSecret = secret.GoogleClientSecret
		Vars.MicrosoftClientSecret = secret.MicrosoftClientSecret
		Vars.JWT = secret.JWT
		Vars.USPSUserID = secret.USPSUserID
		Vars.StateMachineARN = secret.StateMachineARN
		Vars.InternalDrumkitKey = secret.InternalDrumkitKey
	}

	return nil
}
