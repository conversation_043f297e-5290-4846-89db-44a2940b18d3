# =============================================================================
# Drumkit API fn Env Template
# =============================================================================
# Copy this file to your `.env` in `fn/api` folder and fill values as needed
# For production, most secrets are loaded from AWS Secrets Manager
# If you make changes/add a new env variable please document it here for other
# engineers!
# =============================================================================

# -----------------------------------------------------------------------------
# Application Environment
# -----------------------------------------------------------------------------
# Required: "dev" or "prod"
APP_ENV=dev

# -----------------------------------------------------------------------------
# Database Configuration (from rds.EnvConfig)
# -----------------------------------------------------------------------------
# Required: Database host
DB_HOST=localhost
# Optional: Read replica host (if using read replicas)
DB_HOST_READER=
# Required: Database name
DB_NAME=drumkit
# Optional: Database port (default: 5432)
DB_PORT=5432
# Optional: Database user
DB_USER=postgres
# Optional: Database password
DB_PASSWORD=postgres
# Optional: AWS Secrets Manager ARN for database credentials (prod only)
DB_SECRET_ARN=

# -----------------------------------------------------------------------------
# Redis Configuration
# -----------------------------------------------------------------------------
# Optional: Redis connection URL (e.g., redis://localhost:6379)
REDIS_URL=redis://localhost:6379

# -----------------------------------------------------------------------------
# Observability & Debugging
# -----------------------------------------------------------------------------
# Optional: Enable tracing (true/false)
TRACE_ON=false

# Optional: Axiom logging configuration (required for prod/staging)
AXIOM_LOG_DATASET=drumkit-logs
AXIOM_TRACE_DATASET=drumkit-traces
AXIOM_ORG_ID=your_axiom_org_id
AXIOM_TOKEN=your_axiom_token

# -----------------------------------------------------------------------------
# OAuth Provider Configuration
# -----------------------------------------------------------------------------
# Required: Google OAuth Client ID
GOOGLE_CLIENT_ID=968651685013-i120oufqf06lonr2lj3ahh92il7j67qo.apps.googleusercontent.com
# Optional: Google OAuth Client Secret (ask engineer)
GOOGLE_CLIENT_SECRET=

# Required: Microsoft OAuth Client ID
MICROSOFT_CLIENT_ID=7ef2023f-908c-4cd7-99ba-866a75fa15d0
# Optional: Microsoft OAuth Client Secret (ask engineer)
MICROSOFT_CLIENT_SECRET=

# -----------------------------------------------------------------------------
# Internal Authentication
# -----------------------------------------------------------------------------
# Optional: Internal Drumkit API key (see 1Pass)
INTERNAL_DRUMKIT_KEY=

# -----------------------------------------------------------------------------
# Webhook Configuration
# -----------------------------------------------------------------------------
# Optional: Gmail webhook topic (default: projects/beacon-397017/topics/GmailInboxPush)
GMAIL_WEBHOOK_TOPIC=
# Required: Microsoft webhook URL, see setup instructions at `fn/ingestion/outlook/README.md`
MICROSOFT_WEBHOOK_URL=
# Optional: Drumkit processor URL (does not need to be set locally - handled by fn/processor/dev.go)
DRUMKIT_PROCESSOR_URL=

# -----------------------------------------------------------------------------
# LLM Observability
# -----------------------------------------------------------------------------
# Optional: Braintrust configuration for LLM observability
BRAINTRUST_BASE_URL=
BRAINTRUST_API_KEY=

# -----------------------------------------------------------------------------
# External Services
# -----------------------------------------------------------------------------
# Optional: Cyclops URL
CYCLOPS_URL=http://localhost:8000
# Optional: Pipedream onboarding webhook URL
# Prod example: https://eoet14oob9cgxro.m.pipedream.net
ONBOARDING_PIPEDREAM_URL=

# -----------------------------------------------------------------------------
# AWS Configuration (prod only)
# -----------------------------------------------------------------------------
# Optional: AWS Secrets Manager ARN containing sensitive credentials
# Format: arn:aws:secretsmanager:region:account:secret:name
API_SECRET_ARN=
# Optional: AWS Step Functions State Machine ARN (loaded from API_SECRET_ARN in prod)
STATE_MACHINE_ARN=
# Optional: S3 bucket name for file storage - this is more impportant for processor env for attachment processing
# Host a local S3 bucket "drumkit-local" using localstack x docker see `scripts/s3/README.md` for setup details.
# You can also connect to the dev S3 bucket dev-axle-beacon-ingestion
S3_BUCKET_NAME=drumkit-local

# -----------------------------------------------------------------------------
# Queue Configuration
# -----------------------------------------------------------------------------
# Optional: SQS queue URL for backfill operations
BACKFILL_SQS_QUEUE_URL=
# Optional: Backfill hours window (default: 24)
BACKFILL_HOURS=

# -----------------------------------------------------------------------------
# Security & Secrets (dev only - loaded from AWS Secrets Manager in prod)
# -----------------------------------------------------------------------------
# Optional: JWT secret key (loaded from API_SECRET_ARN in prod)
JWT=
# Optional: USPS User ID for zipcode lookups
USPS_USER_ID=70AXLETA37809

# -----------------------------------------------------------------------------
# Metabase Configuration
# -----------------------------------------------------------------------------
# Optional: Metabase site URL
METABASE_SITE_URL=
# Optional: Metabase secret key
METABASE_SECRET_KEY=

# =============================================================================
# Notes:
# =============================================================================
# - In production (APP_ENV=prod), the following are loaded from AWS Secrets Manager:
#   * AES_KEY (used internally by crypto package)
#   * GOOGLE_CLIENT_SECRET
#   * MICROSOFT_CLIENT_SECRET
#   * JWT
#   * USPS_USER_ID
#   * STATE_MACHINE_ARN
#   * INTERNAL_DRUMKIT_KEY
#
# - For dev environment, you can set these directly in this file
# - For prod/staging, ensure AXIOM_* variables are set for observability
# - DB_USER and DB_PASSWORD in prod may be JSON keys referencing AWS Secrets Manager
# =============================================================================

