name: Refresh materialized view

on:
  schedule:
    - cron: '0 7 * * *'
  workflow_dispatch:

jobs:
  refresh-mv:
    runs-on: ubuntu-latest
    permissions:
      contents: read
    steps:
      - name: Checkout
        uses: actions/checkout@v5

      - name: Install dependencies
        run: sudo apt-get update && sudo apt-get install -y postgresql-client

      - name: Refresh materialized views
        env:
          PGHOST: drumkit-prod.cluster-c7kpei57f4sp.us-east-1.rds.amazonaws.com
          PGPORT: 5432
          PGDATABASE: beacon
          PGUSER: ${{ secrets.MV_REFRESH_USER }}
          PGPASSWORD: ${{ secrets.MV_REFRESH_PASSWORD }}
          PGOPTIONS: "-c statement_timeout=3600000"
        run: |
          set -euo pipefail
          : "${PGUSER:?MV_REFRESH_USER secret is not set}"
          : "${PGPASSWORD:?MV_REFRESH_PASSWORD secret is not set}"
          PGPORT="${PGPORT:-5432}"

          MVS=("mv_qr_qq_users" "mv_daily_opportunities" "mv_daily_qq_search")

          for mv in "${MVS[@]}"; do
            echo "Refreshing ${mv} on ${PGHOST}:${PGPORT}/${PGDATABASE}"
            # Run the refresh and then print a timestamp + row count. Use two -c invocations to avoid quoting headaches.
            psql "host=${PGHOST} port=${PGPORT} dbname=${PGDATABASE} user=${PGUSER} sslmode=require" -v ON_ERROR_STOP=1 -c "REFRESH MATERIALIZED VIEW ${mv};" -c "SELECT now() AS refreshed_at, (SELECT count(*) FROM ${mv}) AS row_count;"
            echo "Refresh of ${mv} completed"
            echo "----------------------------------------"
          done
